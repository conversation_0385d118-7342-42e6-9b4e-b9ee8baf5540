{"name": "lottery-forecast", "version": "1.0.0", "description": "A Telegram bot for lottery forecasting and prediction", "main": "index.js", "scripts": {"test": "jest", "test:watch": "jest --watch", "start": "node index.js", "dev": "nodemon index.js", "bot": "node bot.js", "bot:dev": "nodemon bot.js"}, "license": "MIT", "dependencies": {"@tensorflow/tfjs-node": "^4.15.0", "async": "^3.2.4", "axios": "^1.6.2", "bcryptjs": "^2.4.3", "cheerio": "^1.0.0-rc.12", "config": "^3.3.9", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "moment": "^2.29.4", "mongoose": "^7.5.0", "ms": "^2.1.3", "node-cron": "^3.0.3", "node-telegram-bot-api": "^0.64.0", "nodemailer": "^6.9.5", "openai": "4.79.1", "redis": "^4.6.8", "rr": "^0.1.0", "socket.io": "^4.7.2", "telegraf": "^4.15.6", "uuid": "^11.1.0", "winston": "2.4.6", "winston-daily-rotate-file": "3.10.0"}, "devDependencies": {"jest": "^29.7.0", "mongodb-memory-server": "^9.1.3", "nodemon": "^3.0.1", "supertest": "^6.3.3"}}