# Lottery Prediction Bot

Bot Telegram dự đoán xổ số miền <PERSON><PERSON> sử dụng AI và phân tích thống kê.

## 🏗️ Cấu Trúc Dự Án

```
lib/
├── bot/                    # Telegram Bot Handler
│   ├── handlers/          # Command handlers
│   ├── middleware/        # Bot middleware
│   └── utils/            # Bot utilities
├── prediction/            # ML Prediction Engine
│   ├── models/           # ML models (LSTM, Statistical, Ensemble)
│   ├── trainers/         # Model training
│   └── analyzers/        # Data analysis
├── data/                  # Data Management
│   ├── repositories/     # Database repositories
│   ├── cache/           # Redis cache
│   └── collectors/      # Data collection
├── llm/                   # LLM Service
│   ├── providers/       # LLM providers (OpenAI)
│   ├── prompts/         # Prompt templates
│   └── formatters/      # Output formatting
└── scheduler/             # Task Scheduler
    ├── jobs/            # Scheduled jobs
    └── cron/            # Cron management
```

## 🚀 Cài Đặt

### 1. Clone Repository
```bash
git clone <repository-url>
cd lottery-forecast
```

### 2. Cài Đặt Dependencies
```bash
npm install
```

### 3. <PERSON><PERSON><PERSON>
```bash
cp .env.example .env
```

Chỉnh sửa file `.env` với các thông tin cần thiết:
- `TELEGRAM_BOT_TOKEN`: Token bot Telegram
- `MONGODB_URI`: URI kết nối MongoDB
- `REDIS_URL`: URL kết nối Redis
- `OPENAI_API_KEY`: API key OpenAI

### 4. Khởi Động Services

#### MongoDB
```bash
# Sử dụng Docker
docker run -d -p 27017:27017 --name mongodb mongo:latest

# Hoặc cài đặt local
# Tham khảo: https://docs.mongodb.com/manual/installation/
```

#### Redis
```bash
# Sử dụng Docker
docker run -d -p 6379:6379 --name redis redis:latest

# Hoặc cài đặt local
# Tham khảo: https://redis.io/download
```

## 🎯 Chạy Ứng Dụng

### Development Mode
```bash
npm run bot:dev
```

### Production Mode
```bash
npm run bot
```

### API Server (existing)
```bash
npm start
```

## 🤖 Lệnh Bot

| Lệnh | Mô Tả |
|------|-------|
| `/start` | Bắt đầu sử dụng bot |
| `/help` | Hiển thị trợ giúp |
| `/dukienlo` | Dự đoán số lô |
| `/dukiende` | Dự đoán số đề |
| `/lichsu` | Xem lịch sử kết quả |
| `/xuhuonglo` | Xu hướng số lô nóng/lạnh |
| `/xuhuongde` | Xu hướng số đề nóng/lạnh |
| `/number [số]` | Phân tích số cụ thể |

## 📊 Tính Năng

### 🧠 Machine Learning
- **LSTM/RNN Models**: Dự đoán dựa trên time series
- **Statistical Analysis**: Phân tích tần suất và xác suất
- **Ensemble Methods**: Kết hợp nhiều mô hình

### 🤖 AI Reports
- **OpenAI Integration**: Báo cáo thông minh bằng tiếng Việt
- **Fallback System**: Báo cáo cơ bản khi LLM không khả dụng
- **Cost Management**: Quản lý ngân sách API

### ⏰ Automation
- **Daily Predictions**: Dự đoán hàng ngày tự động
- **Weekly Reports**: Báo cáo xu hướng hàng tuần
- **Data Collection**: Thu thập dữ liệu tự động

### 📈 Analytics
- **User Tracking**: Theo dõi tương tác người dùng
- **Performance Metrics**: Đo lường độ chính xác
- **Trend Analysis**: Phân tích xu hướng số

## 🔧 Configuration

### Bot Configuration (`config/bot.json`)
- Cấu hình lệnh bot
- Giới hạn rate limiting
- Tin nhắn hệ thống

### Database Configuration (`config/database.json`)
- Cấu hình MongoDB và Redis
- Indexes và performance tuning

### ML Configuration (`config/ml.json`)
- Cấu hình mô hình học máy
- Tham số training và prediction

## 🧪 Testing

```bash
# Chạy tất cả tests
npm test

# Chạy tests với watch mode
npm run test:watch
```

## 📝 Development

### Thêm Command Handler Mới
1. Tạo handler trong `lib/bot/handlers/`
2. Đăng ký trong `lib/bot/index.js`
3. Thêm vào `config/bot.json`

### Thêm ML Model Mới
1. Tạo model class trong `lib/prediction/models/`
2. Implement interface chuẩn
3. Đăng ký trong `lib/prediction/index.js`

### Thêm Scheduled Job Mới
1. Tạo job class trong `lib/scheduler/jobs/`
2. Implement execute method
3. Đăng ký trong `lib/scheduler/index.js`

## 🚨 Monitoring

### Health Check
```bash
curl http://localhost:3000/health
```

### Application Status
Bot cung cấp các endpoint monitoring:
- Service status
- Performance metrics
- Error tracking

## 🔒 Security

- **Environment Variables**: Sensitive data trong .env
- **Rate Limiting**: Chống spam và abuse
- **Input Validation**: Sanitize user input
- **Error Handling**: Không expose internal errors

## 📚 Dependencies

### Core Dependencies
- `node-telegram-bot-api`: Telegram Bot API
- `telegraf`: Alternative Telegram framework
- `@tensorflow/tfjs-node`: Machine Learning
- `openai`: OpenAI API integration
- `mongoose`: MongoDB ODM
- `redis`: Redis client
- `node-cron`: Cron job scheduling

### Development Dependencies
- `jest`: Testing framework
- `nodemon`: Development server
- `supertest`: API testing

## 🤝 Contributing

1. Fork repository
2. Tạo feature branch
3. Commit changes
4. Push to branch
5. Create Pull Request

## 📄 License

MIT License - xem file LICENSE để biết thêm chi tiết.

## 🆘 Support

Nếu gặp vấn đề, vui lòng:
1. Kiểm tra logs trong thư mục `logs/`
2. Xem documentation
3. Tạo issue trên GitHub