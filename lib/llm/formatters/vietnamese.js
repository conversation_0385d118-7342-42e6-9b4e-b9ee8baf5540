/**
 * Vietnamese Language Formatter
 * Formats LLM responses for Vietnamese audience
 */

class VietnameseFormatter {
  /**
   * Format LLM response for Vietnamese audience
   * @param {string} content - Raw LLM response
   * @param {string} type - Type of content (prediction, trend, analysis)
   * @returns {string} Formatted content
   */
  static formatResponse(content, type = 'general') {
    if (!content) return '';

    let formatted = content;

    // Basic formatting
    formatted = this.cleanupText(formatted);
    formatted = this.addEmojis(formatted, type);
    formatted = this.formatStructure(formatted);
    formatted = this.improveVietnamese(formatted);

    return formatted;
  }

  /**
   * Clean up text formatting
   * @param {string} text - Input text
   * @returns {string} Cleaned text
   */
  static cleanupText(text) {
    return text
      // Remove excessive whitespace
      .replace(/\n\s*\n\s*\n/g, '\n\n')
      .replace(/[ \t]+/g, ' ')
      // Fix punctuation spacing
      .replace(/\s+([,.!?:;])/g, '$1')
      .replace(/([,.!?:;])\s*([a-zA-ZÀ-ỹ])/g, '$1 $2')
      // Remove trailing whitespace
      .replace(/[ \t]+$/gm, '')
      .trim();
  }

  /**
   * Add appropriate emojis based on content type
   * @param {string} text - Input text
   * @param {string} type - Content type
   * @returns {string} Text with emojis
   */
  static addEmojis(text, type) {
    // Don't add emojis if they already exist
    if (/[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]/u.test(text)) {
      return text;
    }

    const emojiMap = {
      prediction: {
        'dự đoán': '🎯',
        'độ tin cậy': '📊',
        'phân tích': '🔍',
        'khuyến nghị': '💡',
        'cảnh báo': '⚠️',
        'lưu ý': '📝'
      },
      trend: {
        'xu hướng': '📈',
        'số nóng': '🔥',
        'số lạnh': '❄️',
        'tăng': '📈',
        'giảm': '📉',
        'ổn định': '➡️'
      },
      analysis: {
        'phân tích': '🔍',
        'thống kê': '📊',
        'tần suất': '📈',
        'mẫu': '🔄',
        'tương quan': '🔗'
      }
    };

    const emojis = emojiMap[type] || emojiMap.prediction;

    Object.entries(emojis).forEach(([keyword, emoji]) => {
      const regex = new RegExp(`\\b${keyword}\\b`, 'gi');
      text = text.replace(regex, `${emoji} ${keyword}`);
    });

    return text;
  }

  /**
   * Format text structure for better readability
   * @param {string} text - Input text
   * @returns {string} Structured text
   */
  static formatStructure(text) {
    // Add bold formatting for headers
    text = text.replace(/^(\d+\.\s*[^:\n]+):?$/gm, '**$1:**');
    text = text.replace(/^([A-ZÀÁẢÃẠĂẮẰẲẴẶÂẤẦẨẪẬĐÉÈẺẼẸÊẾỀỂỄỆÍÌỈĨỊÓÒỎÕỌÔỐỒỔỖỘƠỚỜỞỠỢÚÙỦŨỤƯỨỪỬỮỰÝỲỶỸỴ][^:\n]*):$/gm, '**$1:**');

    // Format bullet points
    text = text.replace(/^[-•]\s*/gm, '• ');

    // Format numbered lists
    text = text.replace(/^(\d+)\.\s*/gm, '$1. ');

    return text;
  }

  /**
   * Improve Vietnamese language quality
   * @param {string} text - Input text
   * @returns {string} Improved Vietnamese text
   */
  static improveVietnamese(text) {
    // Common Vietnamese improvements
    const improvements = {
      // Fix common translation issues
      'xác suất cao': 'khả năng cao',
      'có thể có thể': 'có thể',
      'sẽ sẽ': 'sẽ',
      'rất rất': 'rất',

      // Improve formal language
      'bạn nên': 'khuyến nghị',
      'tôi nghĩ': 'theo phân tích',
      'tôi tin': 'dự báo',

      // Fix grammar
      'một cách': 'theo cách',
      'trong khi': 'trong khi đó',
      'tuy nhiên': 'tuy nhiên,',
      'do đó': 'do đó,',
      'vì vậy': 'vì vậy,',

      // Improve lottery-specific terms
      'con số': 'số',
      'các con số': 'các số',
      'dự đoán số': 'dự đoán',
      'kết quả xổ số': 'kết quả'
    };

    Object.entries(improvements).forEach(([old, improved]) => {
      const regex = new RegExp(old, 'gi');
      text = text.replace(regex, improved);
    });

    return text;
  }

  /**
   * Format prediction report specifically
   * @param {string} content - Prediction content
   * @param {Object} metadata - Additional metadata
   * @returns {string} Formatted prediction report
   */
  static formatPredictionReport(content, metadata = {}) {
    let formatted = this.formatResponse(content, 'prediction');

    // Add header if not present
    if (!formatted.includes('Báo Cáo') && !formatted.includes('Dự Đoán')) {
      const type = metadata.type === 'de' ? 'Đề' : 'Lô';
      formatted = `📊 **Báo Cáo Dự Đoán ${type}**\n\n${formatted}`;
    }

    // Add footer disclaimer
    if (!formatted.includes('tham khảo') && !formatted.includes('rủi ro')) {
      formatted += '\n\n⚠️ **Lưu ý:** Thông tin chỉ mang tính tham khảo. Xổ số có tính ngẫu nhiên cao, hãy chơi có trách nhiệm!';
    }

    return formatted;
  }

  /**
   * Format trend analysis specifically
   * @param {string} content - Trend content
   * @param {Object} metadata - Additional metadata
   * @returns {string} Formatted trend analysis
   */
  static formatTrendAnalysis(content, metadata = {}) {
    let formatted = this.formatResponse(content, 'trend');

    // Add header if not present
    if (!formatted.includes('Xu Hướng') && !formatted.includes('Phân Tích')) {
      const type = metadata.type === 'de' ? 'Đề' : 'Lô';
      formatted = `📈 **Phân Tích Xu Hướng ${type}**\n\n${formatted}`;
    }

    return formatted;
  }

  /**
   * Format number analysis specifically
   * @param {string} content - Analysis content
   * @param {Object} metadata - Additional metadata
   * @returns {string} Formatted number analysis
   */
  static formatNumberAnalysis(content, metadata = {}) {
    let formatted = this.formatResponse(content, 'analysis');

    // Add header if not present
    if (!formatted.includes('Phân Tích Số') && metadata.number) {
      formatted = `🔍 **Phân Tích Số ${metadata.number}**\n\n${formatted}`;
    }

    return formatted;
  }

  /**
   * Truncate content if too long
   * @param {string} content - Content to truncate
   * @param {number} maxLength - Maximum length
   * @returns {string} Truncated content
   */
  static truncateContent(content, maxLength = 4000) {
    if (content.length <= maxLength) {
      return content;
    }

    // Find a good breaking point
    const truncated = content.substring(0, maxLength - 100);
    const lastParagraph = truncated.lastIndexOf('\n\n');
    const lastSentence = truncated.lastIndexOf('.');

    let breakPoint = lastParagraph > 0 ? lastParagraph : lastSentence;
    if (breakPoint < maxLength * 0.7) {
      breakPoint = maxLength - 100;
    }

    return truncated.substring(0, breakPoint) + '\n\n📝 *[Báo cáo đã được rút gọn để phù hợp với giới hạn tin nhắn]*';
  }

  /**
   * Add contextual information
   * @param {string} content - Main content
   * @param {Object} context - Context information
   * @returns {string} Content with context
   */
  static addContext(content, context = {}) {
    let result = content;

    // Add timestamp if provided
    if (context.timestamp) {
      const time = new Date(context.timestamp).toLocaleString('vi-VN');
      result = `🕐 *Cập nhật: ${time}*\n\n${result}`;
    }

    // Add data source if provided
    if (context.dataSource) {
      result += `\n\n📊 *Dữ liệu: ${context.dataSource}*`;
    }

    // Add model info if provided
    if (context.model) {
      result += `\n🤖 *Phân tích bởi: ${context.model}*`;
    }

    return result;
  }

  /**
   * Validate Vietnamese text quality
   * @param {string} text - Text to validate
   * @returns {Object} Validation result
   */
  static validateText(text) {
    const issues = [];

    // Check for common issues
    if (text.includes('tôi')) {
      issues.push('Sử dụng ngôi thứ nhất không phù hợp');
    }

    if (text.length < 50) {
      issues.push('Nội dung quá ngắn');
    }

    if (text.length > 4000) {
      issues.push('Nội dung quá dài cho Telegram');
    }

    if (!text.includes('tham khảo') && !text.includes('rủi ro')) {
      issues.push('Thiếu cảnh báo về rủi ro');
    }

    return {
      isValid: issues.length === 0,
      issues,
      length: text.length
    };
  }
}

module.exports = VietnameseFormatter;