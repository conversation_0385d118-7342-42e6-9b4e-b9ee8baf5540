const VietnameseFormatter = require('./formatters/vietnamese');

/**
 * Report Generator for LLM Service
 * Handles report generation with Vietnamese language formatting and cost tracking
 */
class ReportGenerator {
  constructor(llmService, options = {}) {
    this.llmService = llmService;
    this.options = {
      defaultLanguage: options.defaultLanguage || 'vi',
      maxReportLength: options.maxReportLength || 4000,
      enableCostTracking: options.enableCostTracking !== false,
      reportTemplates: options.reportTemplates || {},
      ...options
    };

    // Cost tracking
    this.dailyCosts = new Map();
    this.monthlyCosts = new Map();
    this.reportCounts = new Map();

    // Report templates
    this.templates = {
      prediction: this.getPredictionTemplates(),
      trend: this.getTrendTemplates(),
      analysis: this.getAnalysisTemplates(),
      weekly: this.getWeeklyTemplates(),
      monthly: this.getMonthlyTemplates(),
      ...this.options.reportTemplates
    };
  }

  /**
   * Generate prediction report with Vietnamese formatting
   * @param {Array} predictions - Prediction data
   * @param {Array} historicalData - Historical lottery data
   * @param {string} type - Type ('lo' or 'de')
   * @param {Object} options - Generation options
   * @returns {Promise<Object>} Generated report with metadata
   */
  async generatePredictionReport(predictions, historicalData, type = 'lo', options = {}) {
    const startTime = Date.now();

    try {
      // Track report generation
      this.trackReportGeneration('prediction', type);

      // Generate report using LLM service
      const content = await this.llmService.generatePredictionReport(predictions, historicalData, type);

      // Format for Vietnamese audience
      const formattedContent = VietnameseFormatter.formatPredictionReport(content, { type });

      // Ensure proper length
      const finalContent = VietnameseFormatter.truncateContent(formattedContent, this.options.maxReportLength);

      // Add context information
      const contextualContent = VietnameseFormatter.addContext(finalContent, {
        timestamp: new Date().toISOString(),
        dataSource: `${historicalData.length} kết quả gần đây`,
        model: this.llmService.options.model
      });

      // Validate content quality
      const validation = VietnameseFormatter.validateText(contextualContent);

      const report = {
        content: contextualContent,
        metadata: {
          type: 'prediction',
          subtype: type,
          generatedAt: new Date().toISOString(),
          generationTime: Date.now() - startTime,
          predictionsCount: predictions.length,
          historicalDataPoints: historicalData.length,
          contentLength: contextualContent.length,
          validation,
          language: 'vi'
        }
      };

      // Track costs if enabled
      if (this.options.enableCostTracking) {
        await this.trackReportCost(report, 'prediction');
      }

      return report;

    } catch (error) {
      console.error('Report generation error:', error.message);

      // Generate fallback report
      const fallbackContent = this.generateFallbackPredictionReport(predictions, type);

      return {
        content: fallbackContent,
        metadata: {
          type: 'prediction',
          subtype: type,
          generatedAt: new Date().toISOString(),
          generationTime: Date.now() - startTime,
          isFallback: true,
          error: error.message,
          language: 'vi'
        }
      };
    }
  }

  /**
   * Generate trend analysis report
   * @param {Object} trends - Trend data
   * @param {string} type - Type ('lo' or 'de')
   * @param {string} period - Analysis period
   * @param {Object} options - Generation options
   * @returns {Promise<Object>} Generated report with metadata
   */
  async generateTrendReport(trends, type = 'lo', period = '30 ngày', options = {}) {
    const startTime = Date.now();

    try {
      this.trackReportGeneration('trend', type);

      const content = await this.llmService.generateTrendAnalysis(trends, type, period);
      const formattedContent = VietnameseFormatter.formatTrendAnalysis(content, { type });
      const finalContent = VietnameseFormatter.truncateContent(formattedContent, this.options.maxReportLength);

      const contextualContent = VietnameseFormatter.addContext(finalContent, {
        timestamp: new Date().toISOString(),
        dataSource: `Phân tích ${period}`,
        model: this.llmService.options.model
      });

      const validation = VietnameseFormatter.validateText(contextualContent);

      const report = {
        content: contextualContent,
        metadata: {
          type: 'trend',
          subtype: type,
          period,
          generatedAt: new Date().toISOString(),
          generationTime: Date.now() - startTime,
          hotNumbers: trends.hot?.length || 0,
          coldNumbers: trends.cold?.length || 0,
          contentLength: contextualContent.length,
          validation,
          language: 'vi'
        }
      };

      if (this.options.enableCostTracking) {
        await this.trackReportCost(report, 'trend');
      }

      return report;

    } catch (error) {
      console.error('Trend report generation error:', error.message);

      const fallbackContent = this.generateFallbackTrendReport(trends, type);

      return {
        content: fallbackContent,
        metadata: {
          type: 'trend',
          subtype: type,
          period,
          generatedAt: new Date().toISOString(),
          generationTime: Date.now() - startTime,
          isFallback: true,
          error: error.message,
          language: 'vi'
        }
      };
    }
  }

  /**
   * Generate number analysis report
   * @param {string} number - Number to analyze
   * @param {Object} frequency - Frequency data
   * @param {Array} patterns - Detected patterns
   * @param {Object} correlations - Number correlations
   * @param {Object} options - Generation options
   * @returns {Promise<Object>} Generated report with metadata
   */
  async generateNumberAnalysisReport(number, frequency, patterns, correlations = {}, options = {}) {
    const startTime = Date.now();

    try {
      this.trackReportGeneration('analysis', number);

      const content = await this.llmService.generateNumberAnalysis(number, frequency, patterns, correlations);
      const formattedContent = VietnameseFormatter.formatNumberAnalysis(content, { number });
      const finalContent = VietnameseFormatter.truncateContent(formattedContent, this.options.maxReportLength);

      const contextualContent = VietnameseFormatter.addContext(finalContent, {
        timestamp: new Date().toISOString(),
        dataSource: `Phân tích số ${number}`,
        model: this.llmService.options.model
      });

      const validation = VietnameseFormatter.validateText(contextualContent);

      const report = {
        content: contextualContent,
        metadata: {
          type: 'analysis',
          subtype: 'number',
          number,
          generatedAt: new Date().toISOString(),
          generationTime: Date.now() - startTime,
          frequency: frequency.frequency || 0,
          patternsCount: patterns.length || 0,
          correlationsCount: Object.keys(correlations).length,
          contentLength: contextualContent.length,
          validation,
          language: 'vi'
        }
      };

      if (this.options.enableCostTracking) {
        await this.trackReportCost(report, 'analysis');
      }

      return report;

    } catch (error) {
      console.error('Number analysis report generation error:', error.message);

      const fallbackContent = this.generateFallbackNumberAnalysis(number, frequency);

      return {
        content: fallbackContent,
        metadata: {
          type: 'analysis',
          subtype: 'number',
          number,
          generatedAt: new Date().toISOString(),
          generationTime: Date.now() - startTime,
          isFallback: true,
          error: error.message,
          language: 'vi'
        }
      };
    }
  }

  /**
   * Generate weekly summary report
   * @param {Array} weeklyData - Weekly data
   * @param {string} type - Type ('lo' or 'de')
   * @param {Object} options - Generation options
   * @returns {Promise<Object>} Generated report with metadata
   */
  async generateWeeklyReport(weeklyData, type = 'lo', options = {}) {
    const startTime = Date.now();

    try {
      this.trackReportGeneration('weekly', type);

      const content = await this.llmService.generateWeeklyReport(weeklyData, type);
      const formattedContent = VietnameseFormatter.formatResponse(content, 'trend');
      const finalContent = VietnameseFormatter.truncateContent(formattedContent, this.options.maxReportLength);

      const contextualContent = VietnameseFormatter.addContext(finalContent, {
        timestamp: new Date().toISOString(),
        dataSource: `Báo cáo tuần ${type}`,
        model: this.llmService.options.model
      });

      const validation = VietnameseFormatter.validateText(contextualContent);

      const report = {
        content: contextualContent,
        metadata: {
          type: 'weekly',
          subtype: type,
          generatedAt: new Date().toISOString(),
          generationTime: Date.now() - startTime,
          weeksAnalyzed: weeklyData.length,
          contentLength: contextualContent.length,
          validation,
          language: 'vi'
        }
      };

      if (this.options.enableCostTracking) {
        await this.trackReportCost(report, 'weekly');
      }

      return report;

    } catch (error) {
      console.error('Weekly report generation error:', error.message);

      const fallbackContent = this.generateFallbackWeeklyReport(weeklyData, type);

      return {
        content: fallbackContent,
        metadata: {
          type: 'weekly',
          subtype: type,
          generatedAt: new Date().toISOString(),
          generationTime: Date.now() - startTime,
          isFallback: true,
          error: error.message,
          language: 'vi'
        }
      };
    }
  }

  /**
   * Track report generation for analytics
   * @param {string} reportType - Type of report
   * @param {string} subtype - Subtype or additional info
   */
  trackReportGeneration(reportType, subtype) {
    const today = new Date().toDateString();
    const key = `${reportType}_${subtype}`;

    if (!this.reportCounts.has(today)) {
      this.reportCounts.set(today, new Map());
    }

    const dailyCounts = this.reportCounts.get(today);
    dailyCounts.set(key, (dailyCounts.get(key) || 0) + 1);
  }

  /**
   * Track report generation costs
   * @param {Object} report - Generated report
   * @param {string} reportType - Type of report
   */
  async trackReportCost(report, reportType) {
    if (!this.options.enableCostTracking) return;

    try {
      // Get usage stats from LLM service
      const usageStats = this.llmService.getUsageStats();
      const estimatedCost = this.estimateReportCost(report, reportType);

      const today = new Date().toDateString();
      const month = new Date().toISOString().substring(0, 7); // YYYY-MM

      // Track daily costs
      if (!this.dailyCosts.has(today)) {
        this.dailyCosts.set(today, { total: 0, reports: [] });
      }

      const dailyData = this.dailyCosts.get(today);
      dailyData.total += estimatedCost;
      dailyData.reports.push({
        type: reportType,
        cost: estimatedCost,
        timestamp: new Date().toISOString(),
        contentLength: report.content.length,
        generationTime: report.metadata.generationTime
      });

      // Track monthly costs
      if (!this.monthlyCosts.has(month)) {
        this.monthlyCosts.set(month, { total: 0, dailyBreakdown: new Map() });
      }

      const monthlyData = this.monthlyCosts.get(month);
      monthlyData.total += estimatedCost;
      monthlyData.dailyBreakdown.set(today, (monthlyData.dailyBreakdown.get(today) || 0) + estimatedCost);

    } catch (error) {
      console.error('Cost tracking error:', error.message);
    }
  }

  /**
   * Estimate report generation cost
   * @param {Object} report - Generated report
   * @param {string} reportType - Type of report
   * @returns {number} Estimated cost in USD
   */
  estimateReportCost(report, reportType) {
    // Base cost estimates by report type
    const baseCosts = {
      prediction: 0.01,
      trend: 0.008,
      analysis: 0.012,
      weekly: 0.015,
      monthly: 0.02
    };

    const baseCost = baseCosts[reportType] || 0.01;

    // Adjust based on content length
    const lengthMultiplier = Math.max(0.5, report.content.length / 2000);

    // Adjust based on generation time (complexity indicator)
    const timeMultiplier = Math.max(0.8, report.metadata.generationTime / 3000);

    return baseCost * lengthMultiplier * timeMultiplier;
  }

  /**
   * Get cost statistics
   * @param {string} period - Period ('daily', 'monthly', 'all')
   * @returns {Object} Cost statistics
   */
  getCostStats(period = 'daily') {
    const today = new Date().toDateString();
    const month = new Date().toISOString().substring(0, 7);

    switch (period) {
      case 'daily':
        return {
          date: today,
          costs: this.dailyCosts.get(today) || { total: 0, reports: [] },
          reportCounts: this.reportCounts.get(today) || new Map()
        };

      case 'monthly':
        return {
          month,
          costs: this.monthlyCosts.get(month) || { total: 0, dailyBreakdown: new Map() },
          totalReports: this.getTotalReportsForMonth(month)
        };

      case 'all':
        return {
          dailyCosts: Object.fromEntries(this.dailyCosts),
          monthlyCosts: Object.fromEntries(this.monthlyCosts),
          reportCounts: Object.fromEntries(this.reportCounts)
        };

      default:
        return this.getCostStats('daily');
    }
  }

  /**
   * Get total reports for a month
   * @param {string} month - Month in YYYY-MM format
   * @returns {number} Total reports count
   */
  getTotalReportsForMonth(month) {
    let total = 0;

    for (const [date, counts] of this.reportCounts.entries()) {
      if (date.startsWith(month)) {
        for (const count of counts.values()) {
          total += count;
        }
      }
    }

    return total;
  }

  /**
   * Generate fallback prediction report
   * @param {Array} predictions - Predictions
   * @param {string} type - Type
   * @returns {string} Fallback report
   */
  generateFallbackPredictionReport(predictions, type) {
    const template = this.templates.prediction[0];
    return this.fillTemplate(template, { predictions, type });
  }

  /**
   * Generate fallback trend report
   * @param {Object} trends - Trends data
   * @param {string} type - Type
   * @returns {string} Fallback report
   */
  generateFallbackTrendReport(trends, type) {
    const template = this.templates.trend[0];
    return this.fillTemplate(template, { trends, type });
  }

  /**
   * Generate fallback number analysis
   * @param {string} number - Number
   * @param {Object} frequency - Frequency data
   * @returns {string} Fallback analysis
   */
  generateFallbackNumberAnalysis(number, frequency) {
    const template = this.templates.analysis[0];
    return this.fillTemplate(template, { number, frequency });
  }

  /**
   * Generate fallback weekly report
   * @param {Array} weeklyData - Weekly data
   * @param {string} type - Type
   * @returns {string} Fallback report
   */
  generateFallbackWeeklyReport(weeklyData, type) {
    const template = this.templates.weekly[0];
    return this.fillTemplate(template, { weeklyData, type });
  }

  /**
   * Fill template with data
   * @param {string} template - Template string
   * @param {Object} data - Data to fill
   * @returns {string} Filled template
   */
  fillTemplate(template, data) {
    let filled = template;

    // Replace common placeholders
    filled = filled.replace(/{type}/g, data.type === 'de' ? 'Đề' : 'Lô');
    filled = filled.replace(/{date}/g, new Date().toLocaleDateString('vi-VN'));
    filled = filled.replace(/{time}/g, new Date().toLocaleTimeString('vi-VN'));

    // Replace specific data
    if (data.predictions) {
      const predList = data.predictions.slice(0, 5).map((p, i) =>
        `${i + 1}. **${p.number}** - Độ tin cậy: ${p.confidence}%`
      ).join('\n');
      filled = filled.replace(/{predictions}/g, predList);
    }

    if (data.trends) {
      const hotList = data.trends.hot?.slice(0, 3).map(n => n.number).join(', ') || 'Không có';
      const coldList = data.trends.cold?.slice(0, 3).map(n => n.number).join(', ') || 'Không có';
      filled = filled.replace(/{hotNumbers}/g, hotList);
      filled = filled.replace(/{coldNumbers}/g, coldList);
    }

    if (data.number) {
      filled = filled.replace(/{number}/g, data.number);
      filled = filled.replace(/{frequency}/g, data.frequency?.frequency || 0);
      filled = filled.replace(/{period}/g, data.frequency?.period || 30);
    }

    return filled;
  }

  /**
   * Get prediction report templates
   * @returns {Array<string>} Templates
   */
  getPredictionTemplates() {
    return [
      `📊 **Báo Cáo Dự Đoán {type}** - {date}

🎯 **Dự đoán hàng đầu:**
{predictions}

📈 **Nhận định:**
Dựa trên phân tích dữ liệu lịch sử và mô hình học máy, các số trên có tiềm năng xuất hiện trong kỳ quay sắp tới.

💡 **Lời khuyên:**
• Kết hợp với kinh nghiệm cá nhân
• Chơi có trách nhiệm
• Chỉ đầu tư số tiền có thể chấp nhận mất

⚠️ **Lưu ý:** Thông tin chỉ mang tính tham khảo. Xổ số có tính ngẫu nhiên cao.`
    ];
  }

  /**
   * Get trend report templates
   * @returns {Array<string>} Templates
   */
  getTrendTemplates() {
    return [
      `📈 **Phân Tích Xu Hướng {type}** - {date}

🔥 **Số nóng:** {hotNumbers}
❄️ **Số lạnh:** {coldNumbers}

📊 **Nhận xét:**
Xu hướng dựa trên dữ liệu thống kê gần đây. Số nóng có tần suất xuất hiện cao, số lạnh ít xuất hiện hoặc chưa xuất hiện.

💭 **Phân tích:**
• Số nóng có thể tiếp tục xu hướng hoặc "nguội" đi
• Số lạnh có tiềm năng "bùng nổ"
• Cân bằng tự nhiên có thể xảy ra

⚖️ **Kết luận:** Xu hướng chỉ là tham khảo, không đảm bảo kết quả tương lai.`
    ];
  }

  /**
   * Get analysis report templates
   * @returns {Array<string>} Templates
   */
  getAnalysisTemplates() {
    return [
      `🔍 **Phân Tích Số {number}** - {date}

📊 **Thống kê:**
• Tần suất: {frequency} lần trong {period} ngày
• Trạng thái: Đang được theo dõi

📈 **Đánh giá:**
Số này có tần suất xuất hiện ở mức trung bình, cho thấy tính ổn định trong dữ liệu lịch sử.

🎯 **Tiềm năng:**
• Có khả năng xuất hiện dựa trên chu kỳ thống kê
• Cần theo dõi thêm để xác định xu hướng

💡 **Nhận xét:** Phân tích dựa trên dữ liệu lịch sử, chỉ mang tính tham khảo.`
    ];
  }

  /**
   * Get weekly report templates
   * @returns {Array<string>} Templates
   */
  getWeeklyTemplates() {
    return [
      `📅 **Báo Cáo Tuần {type}** - {date}

📊 **Tóm tắt tuần:**
Phân tích xu hướng và thống kê của tuần qua.

📈 **Xu hướng chính:**
• Các số nổi bật đã được xác định
• Mẫu số có sự thay đổi so với tuần trước

💡 **Khuyến nghị tuần tới:**
• Theo dõi các số có xu hướng tích cực
• Chú ý đến sự thay đổi của các mẫu số

⚠️ **Lưu ý:** Báo cáo dựa trên dữ liệu thống kê, chỉ mang tính tham khảo.`
    ];
  }

  /**
   * Get monthly report templates
   * @returns {Array<string>} Templates
   */
  getMonthlyTemplates() {
    return [
      `📆 **Báo Cáo Tháng {type}** - {date}

📊 **Tổng quan tháng:**
Phân tích toàn diện xu hướng và thống kê của tháng qua.

📈 **Điểm nổi bật:**
• Số thống trị tháng đã được xác định
• Xu hướng dài hạn có sự biến động

🔍 **Phân tích sâu:**
• Chu kỳ xuất hiện có những thay đổi đáng chú ý
• Mối tương quan giữa các số được phát hiện

💡 **Dự báo tháng tới:**
• Một số xu hướng có thể tiếp tục
• Cần theo dõi sự thay đổi của các mẫu số

📝 **Kết luận:** Báo cáo tổng hợp dựa trên dữ liệu thống kê tháng qua.`
    ];
  }

  /**
   * Reset cost tracking data
   * @param {string} period - Period to reset ('daily', 'monthly', 'all')
   */
  resetCostTracking(period = 'daily') {
    const today = new Date().toDateString();
    const month = new Date().toISOString().substring(0, 7);

    switch (period) {
      case 'daily':
        this.dailyCosts.delete(today);
        this.reportCounts.delete(today);
        break;

      case 'monthly':
        this.monthlyCosts.delete(month);
        // Remove all daily data for this month
        for (const date of this.dailyCosts.keys()) {
          if (date.startsWith(month)) {
            this.dailyCosts.delete(date);
            this.reportCounts.delete(date);
          }
        }
        break;

      case 'all':
        this.dailyCosts.clear();
        this.monthlyCosts.clear();
        this.reportCounts.clear();
        break;
    }
  }
}

module.exports = ReportGenerator;