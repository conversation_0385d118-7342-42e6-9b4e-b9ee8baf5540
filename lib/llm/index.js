// LLM Service - AI-powered report generation and analysis
// Integrates with OpenAI API for intelligent lottery analysis

const OpenAIProvider = require('./providers/openai');
const FallbackProvider = require('./providers/fallback');
const PredictionPrompts = require('./prompts/prediction');
const AnalysisPrompts = require('./prompts/analysis');
const TrendsPrompts = require('./prompts/trends');
const VietnameseFormatter = require('./formatters/vietnamese');
const ReportGenerator = require('./reportGenerator');

class LLMService {
  constructor(options = {}) {
    this.options = {
      provider: options.provider || 'openai',
      apiKey: options.apiKey,
      model: options.model || process.env.OPENAI_MODEL || 'gpt-3.5-turbo',
      url: options.url || process.env.OPENAI_URL || 'https://api.openai.com/v1',
      maxTokens: options.maxTokens || parseInt(process.env.OPENAI_MAX_TOKENS) || 1000,
      temperature: options.temperature || 0.7,
      dailyBudget: options.dailyBudget || parseFloat(process.env.OPENAI_DAILY_BUDGET) || 50, // USD
      fallbackEnabled: options.fallbackEnabled !== false,
      ...options
    };

    this.primaryProvider = null;
    this.fallbackProvider = null;
    this.dailyUsage = 0;
    this.lastResetDate = new Date().toDateString();
    this.isInitialized = false;

    // Initialize report generator
    this.reportGenerator = new ReportGenerator(this, {
      enableCostTracking: options.enableCostTracking !== false,
      maxReportLength: options.maxReportLength || 4000
    });
  }

  async initialize() {
    try {
      // Initialize primary provider (OpenAI or HeyAI)
      if (this.options.provider === 'openai' || this.options.provider === 'heyai') {
        this.primaryProvider = new OpenAIProvider({
          apiKey: this.options.apiKey,
          model: this.options.model,
          url: this.options.url,
          maxTokens: this.options.maxTokens,
          temperature: this.options.temperature
        });

        // Test connection
        const isConnected = await this.primaryProvider.testConnection();
        if (!isConnected) {
          console.warn('LLM connection test failed, will use fallback');
          this.primaryProvider = null;
        }
      }

      // Initialize fallback provider
      if (this.options.fallbackEnabled) {
        this.fallbackProvider = new FallbackProvider();
      }

      this.isInitialized = true;
      console.log('LLM Service initialized successfully');

    } catch (error) {
      console.error('LLM Service initialization error:', error.message);

      // Use fallback only if enabled
      if (this.options.fallbackEnabled) {
        this.fallbackProvider = new FallbackProvider();
        this.isInitialized = true;
        console.log('LLM Service initialized with fallback only');
      } else {
        throw error;
      }
    }
  }

  async generatePredictionReport(predictions, historicalData, type = 'lo') {
    if (!this.isInitialized) {
      await this.initialize();
    }

    // Check budget first
    if (!this.checkBudget()) {
      console.log('Daily budget exceeded, using fallback');
      return this.generateFallbackPredictionReport(predictions, type);
    }

    try {
      const prompt = PredictionPrompts.generatePredictionPrompt(predictions, historicalData, type);
      const response = await this.callLLM(prompt);

      this.trackUsage(response.usage);

      const formatted = VietnameseFormatter.formatPredictionReport(response.content, { type });
      return VietnameseFormatter.truncateContent(formatted);

    } catch (error) {
      console.error('LLM prediction report error:', error.message);
      return this.generateFallbackPredictionReport(predictions, type);
    }
  }

  async generateTrendAnalysis(trends, type = 'lo', period = '30 ngày') {
    if (!this.isInitialized) {
      await this.initialize();
    }

    if (!this.checkBudget()) {
      console.log('Daily budget exceeded, using fallback');
      return this.generateFallbackTrendReport(trends, type);
    }

    try {
      const prompt = TrendsPrompts.generateTrendAnalysisPrompt(trends, type, period);
      const response = await this.callLLM(prompt);

      this.trackUsage(response.usage);

      const formatted = VietnameseFormatter.formatTrendAnalysis(response.content, { type });
      return VietnameseFormatter.truncateContent(formatted);

    } catch (error) {
      console.error('LLM trend analysis error:', error.message);
      return this.generateFallbackTrendReport(trends, type);
    }
  }

  async generateNumberAnalysis(number, frequency, patterns, correlations = {}) {
    if (!this.isInitialized) {
      await this.initialize();
    }

    if (!this.checkBudget()) {
      console.log('Daily budget exceeded, using fallback');
      return this.generateFallbackNumberAnalysis(number, frequency);
    }

    try {
      const prompt = AnalysisPrompts.generateNumberAnalysisPrompt(number, frequency, patterns, correlations);
      const response = await this.callLLM(prompt);

      this.trackUsage(response.usage);

      const formatted = VietnameseFormatter.formatNumberAnalysis(response.content, { number });
      return VietnameseFormatter.truncateContent(formatted);

    } catch (error) {
      console.error('LLM number analysis error:', error.message);
      return this.generateFallbackNumberAnalysis(number, frequency);
    }
  }

  /**
   * Generate weekly trend report
   * @param {Array} weeklyData - Weekly trend data
   * @param {string} type - Type ('lo' or 'de')
   * @returns {Promise<string>} Formatted weekly report
   */
  async generateWeeklyReport(weeklyData, type = 'lo') {
    if (!this.isInitialized) {
      await this.initialize();
    }

    if (!this.checkBudget()) {
      return this.generateFallbackWeeklyReport(weeklyData, type);
    }

    try {
      const prompt = TrendsPrompts.generateWeeklyTrendPrompt(weeklyData, type);
      const response = await this.callLLM(prompt);

      this.trackUsage(response.usage);

      const formatted = VietnameseFormatter.formatResponse(response.content, 'trend');
      return VietnameseFormatter.truncateContent(formatted);

    } catch (error) {
      console.error('LLM weekly report error:', error.message);
      return this.generateFallbackWeeklyReport(weeklyData, type);
    }
  }

  /**
   * Generate prediction explanation
   * @param {Object} prediction - Single prediction object
   * @param {Object} supportingData - Supporting data
   * @returns {Promise<string>} Formatted explanation
   */
  async generatePredictionExplanation(prediction, supportingData) {
    if (!this.isInitialized) {
      await this.initialize();
    }

    if (!this.checkBudget()) {
      return `Số ${prediction.number} được dự đoán với độ tin cậy ${prediction.confidence}% dựa trên phân tích thống kê và mô hình học máy.`;
    }

    try {
      const prompt = PredictionPrompts.generateExplanationPrompt(prediction, supportingData);
      const response = await this.callLLM(prompt, { maxTokens: 200 });

      this.trackUsage(response.usage);
      return VietnameseFormatter.formatResponse(response.content);

    } catch (error) {
      console.error('LLM explanation error:', error.message);
      return `Số ${prediction.number} được dự đoán dựa trên ${prediction.method || 'phân tích thống kê'} với độ tin cậy ${prediction.confidence}%.`;
    }
  }

  async callLLM(prompt, options = {}) {
    // Try primary provider first
    if (this.primaryProvider) {
      try {
        return await this.primaryProvider.generateCompletion(prompt, options);
      } catch (error) {
        console.error('Primary LLM provider failed:', error.message);

        // If primary fails, fall back to fallback provider
        if (this.fallbackProvider) {
          console.log('Switching to fallback provider');
          return await this.fallbackProvider.generateCompletion(prompt, options);
        }

        throw error;
      }
    }

    // Use fallback provider if primary is not available
    if (this.fallbackProvider) {
      return await this.fallbackProvider.generateCompletion(prompt, options);
    }

    throw new Error('No LLM provider available');
  }

  /**
   * Generate fallback prediction report
   * @param {Array} predictions - Predictions array
   * @param {string} type - Type ('lo' or 'de')
   * @returns {string} Fallback report
   */
  generateFallbackPredictionReport(predictions, type) {
    if (this.fallbackProvider) {
      const prompt = `Dự đoán ${type}: ${JSON.stringify(predictions)}`;
      return this.fallbackProvider.generateCompletion(prompt).then(response => response.content);
    }
    return this.getFallbackReport(predictions, type);
  }

  /**
   * Generate fallback trend report
   * @param {Object} trends - Trends data
   * @param {string} type - Type ('lo' or 'de')
   * @returns {string} Fallback trend report
   */
  generateFallbackTrendReport(trends, type) {
    if (this.fallbackProvider) {
      const prompt = `Xu hướng ${type}: nóng ${JSON.stringify(trends.hot)}, lạnh ${JSON.stringify(trends.cold)}`;
      return this.fallbackProvider.generateCompletion(prompt).then(response => response.content);
    }
    return this.getFallbackTrendReport(trends, type);
  }

  /**
   * Generate fallback number analysis
   * @param {string} number - Number to analyze
   * @param {Object} frequency - Frequency data
   * @returns {string} Fallback analysis
   */
  generateFallbackNumberAnalysis(number, frequency) {
    if (this.fallbackProvider) {
      const prompt = `Phân tích số ${number}: tần suất ${frequency.frequency}`;
      return this.fallbackProvider.generateCompletion(prompt).then(response => response.content);
    }
    return this.getFallbackNumberAnalysis(number, frequency);
  }

  /**
   * Generate fallback weekly report
   * @param {Array} weeklyData - Weekly data
   * @param {string} type - Type ('lo' or 'de')
   * @returns {string} Fallback weekly report
   */
  generateFallbackWeeklyReport(weeklyData, type) {
    const typeText = type === 'de' ? 'Đề' : 'Lô';
    return `📊 **Báo Cáo Tuần ${typeText}**\n\n` +
           `📈 Dữ liệu tuần qua cho thấy xu hướng ổn định.\n` +
           `💡 Khuyến nghị theo dõi các số có tần suất cao.\n\n` +
           `⚠️ **Lưu ý:** Báo cáo cơ bản do dịch vụ AI tạm thời không khả dụng.`;
  }

  getFallbackReport(predictions, type) {
    // Fallback report when LLM is unavailable
    let report = `📊 **Báo Cáo Dự Đoán ${type.toUpperCase()}**\n\n`;

    report += `🎯 **Các số được dự đoán:**\n`;
    predictions.slice(0, 5).forEach((pred, index) => {
      report += `${index + 1}. **${pred.number}** - Độ tin cậy: ${pred.confidence}%\n`;
    });

    report += `\n💡 **Phân tích cơ bản:**\n`;
    report += `• Dự đoán dựa trên phân tích thống kê và mô hình học máy\n`;
    report += `• Độ tin cậy trung bình: ${Math.round(predictions.reduce((sum, p) => sum + p.confidence, 0) / predictions.length)}%\n`;
    report += `• Khuyến nghị: Chỉ tham khảo, không đảm bảo kết quả\n`;

    report += `\n⚠️ **Lưu ý:** Xổ số có tính ngẫu nhiên cao. Chơi có trách nhiệm!`;

    return report;
  }

  getFallbackTrendReport(trends, type) {
    // Fallback trend report
    let report = `📈 **Phân Tích Xu Hướng ${type.toUpperCase()}**\n\n`;

    if (trends.hot && trends.hot.length > 0) {
      report += `🔥 **Số nóng (xuất hiện nhiều):**\n`;
      trends.hot.forEach(num => {
        report += `• ${num.number}: ${num.frequency} lần\n`;
      });
    }

    if (trends.cold && trends.cold.length > 0) {
      report += `\n❄️ **Số lạnh (xuất hiện ít):**\n`;
      trends.cold.forEach(num => {
        report += `• ${num.number}: ${num.frequency} lần\n`;
      });
    }

    report += `\n💭 **Nhận xét:** Xu hướng dựa trên dữ liệu lịch sử gần đây.`;

    return report;
  }

  getFallbackNumberAnalysis(number, frequency) {
    // Fallback number analysis
    return `🔍 **Phân Tích Số ${number}**\n\n` +
           `📊 Tần suất: ${frequency.frequency} lần trong ${frequency.period} ngày\n` +
           `📅 Lần cuối: ${frequency.lastSeen ? new Date(frequency.lastSeen).toLocaleDateString('vi-VN') : 'Chưa có'}\n\n` +
           `💡 Đây là phân tích cơ bản dựa trên dữ liệu thống kê.`;
  }

  checkBudget() {
    // Check if within daily budget
    const today = new Date().toDateString();
    if (today !== this.lastResetDate) {
      this.dailyUsage = 0;
      this.lastResetDate = today;
    }

    return this.dailyUsage < this.options.dailyBudget;
  }

  trackUsage(usage) {
    // Track API usage and costs
    this.dailyUsage += usage.cost || 0;
  }

  getUsageStats() {
    const reportStats = this.reportGenerator ? this.reportGenerator.getCostStats('daily') : null;

    return {
      dailyUsage: this.dailyUsage,
      dailyBudget: this.options.dailyBudget,
      lastReset: this.lastResetDate,
      budgetRemaining: this.options.dailyBudget - this.dailyUsage,
      primaryProvider: this.primaryProvider?.getStatus() || null,
      fallbackProvider: this.fallbackProvider?.getStatus() || null,
      isInitialized: this.isInitialized,
      reportStats
    };
  }

  /**
   * Test LLM service functionality
   * @returns {Promise<Object>} Test results
   */
  async testService() {
    const results = {
      initialized: this.isInitialized,
      primaryProvider: null,
      fallbackProvider: null,
      canGenerateReports: false
    };

    try {
      if (this.primaryProvider) {
        results.primaryProvider = await this.primaryProvider.testConnection();
      }

      if (this.fallbackProvider) {
        results.fallbackProvider = await this.fallbackProvider.testConnection();
      }

      // Test report generation
      const testPredictions = [
        { number: '12', confidence: 75, method: 'test' }
      ];

      const report = await this.generatePredictionReport(testPredictions, [], 'lo');
      results.canGenerateReports = report && report.length > 0;

    } catch (error) {
      results.error = error.message;
    }

    return results;
  }

  /**
   * Reset daily usage (for testing or manual reset)
   */
  resetDailyUsage() {
    this.dailyUsage = 0;
    this.lastResetDate = new Date().toDateString();
  }

  /**
   * Set daily budget
   * @param {number} budget - New daily budget in USD
   */
  setDailyBudget(budget) {
    this.options.dailyBudget = budget;
  }

  /**
   * Generate comprehensive prediction report with metadata
   * @param {Array} predictions - Prediction data
   * @param {Array} historicalData - Historical data
   * @param {string} type - Type ('lo' or 'de')
   * @param {Object} options - Generation options
   * @returns {Promise<Object>} Report with content and metadata
   */
  async generateComprehensivePredictionReport(predictions, historicalData, type = 'lo', options = {}) {
    return await this.reportGenerator.generatePredictionReport(predictions, historicalData, type, options);
  }

  /**
   * Generate comprehensive trend report with metadata
   * @param {Object} trends - Trend data
   * @param {string} type - Type ('lo' or 'de')
   * @param {string} period - Analysis period
   * @param {Object} options - Generation options
   * @returns {Promise<Object>} Report with content and metadata
   */
  async generateComprehensiveTrendReport(trends, type = 'lo', period = '30 ngày', options = {}) {
    return await this.reportGenerator.generateTrendReport(trends, type, period, options);
  }

  /**
   * Generate comprehensive number analysis report with metadata
   * @param {string} number - Number to analyze
   * @param {Object} frequency - Frequency data
   * @param {Array} patterns - Detected patterns
   * @param {Object} correlations - Number correlations
   * @param {Object} options - Generation options
   * @returns {Promise<Object>} Report with content and metadata
   */
  async generateComprehensiveNumberAnalysis(number, frequency, patterns, correlations = {}, options = {}) {
    return await this.reportGenerator.generateNumberAnalysisReport(number, frequency, patterns, correlations, options);
  }

  /**
   * Generate comprehensive weekly report with metadata
   * @param {Array} weeklyData - Weekly data
   * @param {string} type - Type ('lo' or 'de')
   * @param {Object} options - Generation options
   * @returns {Promise<Object>} Report with content and metadata
   */
  async generateComprehensiveWeeklyReport(weeklyData, type = 'lo', options = {}) {
    return await this.reportGenerator.generateWeeklyReport(weeklyData, type, options);
  }

  /**
   * Get report generation statistics
   * @param {string} period - Period ('daily', 'monthly', 'all')
   * @returns {Object} Report statistics
   */
  getReportStats(period = 'daily') {
    return this.reportGenerator ? this.reportGenerator.getCostStats(period) : null;
  }

  /**
   * Reset report tracking data
   * @param {string} period - Period to reset ('daily', 'monthly', 'all')
   */
  resetReportTracking(period = 'daily') {
    if (this.reportGenerator) {
      this.reportGenerator.resetCostTracking(period);
    }
  }
}

module.exports = LLMService;