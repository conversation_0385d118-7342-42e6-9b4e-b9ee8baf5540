const OpenAI = require('openai');

/**
 * OpenAI API Provider for LLM Service
 * Handles OpenAI API integration with rate limiting and error handling
 */
class OpenAIProvider {
  constructor(options = {}) {
    this.apiKey = options.apiKey || process.env.OPENAI_API_KEY;
    this.model = options.model || process.env.OPENAI_MODEL || 'gpt-3.5-turbo';
    this.url = options.url || process.env.OPENAI_URL || 'https://api.openai.com/v1';
    this.maxTokens = options.maxTokens || parseInt(process.env.OPENAI_MAX_TOKENS) || 1000;
    this.temperature = options.temperature || 0.7;

    // Rate limiting
    this.requestsPerMinute = options.requestsPerMinute || 60;
    this.requestQueue = [];
    this.lastRequestTime = 0;

    // Circuit breaker
    this.failureCount = 0;
    this.maxFailures = options.maxFailures || 5;
    this.circuitBreakerTimeout = options.circuitBreakerTimeout || 300000; // 5 minutes
    this.circuitBreakerOpenTime = null;

    if (!this.apiKey) {
      throw new Error('OpenAI API key is required');
    }

    this.client = new OpenAI({
      apiKey: this.apiKey,
      baseURL: this.url,
      timeout: options.timeout || 30000,
      maxRetries: options.maxRetries || 3
    });
  }

  /**
   * Generate completion using OpenAI API
   * @param {string} prompt - The prompt to send to OpenAI
   * @param {Object} options - Additional options
   * @returns {Promise<Object>} Response with content and usage info
   */
  async generateCompletion(prompt, options = {}) {
    // Check circuit breaker
    if (this.isCircuitBreakerOpen()) {
      throw new Error('OpenAI service temporarily unavailable (circuit breaker open)');
    }

    // Apply rate limiting
    await this.applyRateLimit();

    try {
      const response = await this.client.chat.completions.create({
        model: options.model || this.model,
        messages: [
          {
            role: 'system',
            content: 'Bạn là chuyên gia phân tích xổ số miền Bắc với nhiều năm kinh nghiệm. Hãy cung cấp phân tích chuyên nghiệp, khách quan và dễ hiểu bằng tiếng Việt.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: options.maxTokens || this.maxTokens,
        temperature: options.temperature || this.temperature,
        top_p: options.topP || 1,
        frequency_penalty: options.frequencyPenalty || 0,
        presence_penalty: options.presencePenalty || 0
      });

      // Reset failure count on success
      this.failureCount = 0;
      this.circuitBreakerOpenTime = null;

      const usage = response.usage;
      const content = response.choices[0]?.message?.content || '';

      return {
        content,
        usage: {
          promptTokens: usage.prompt_tokens,
          completionTokens: usage.completion_tokens,
          totalTokens: usage.total_tokens,
          cost: this.calculateCost(usage.total_tokens, this.model)
        },
        model: response.model,
        finishReason: response.choices[0]?.finish_reason
      };

    } catch (error) {
      this.handleError(error);
      throw error;
    }
  }

  /**
   * Apply rate limiting to prevent API quota exhaustion
   */
  async applyRateLimit() {
    const now = Date.now();
    const timeSinceLastRequest = now - this.lastRequestTime;
    const minInterval = 60000 / this.requestsPerMinute; // ms between requests

    if (timeSinceLastRequest < minInterval) {
      const waitTime = minInterval - timeSinceLastRequest;
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }

    this.lastRequestTime = Date.now();
  }

  /**
   * Handle API errors and update circuit breaker state
   * @param {Error} error - The error that occurred
   */
  handleError(error) {
    this.failureCount++;

    // Log error details
    console.error('OpenAI API Error:', {
      message: error.message,
      status: error.status,
      code: error.code,
      type: error.type,
      failureCount: this.failureCount
    });

    // Open circuit breaker if too many failures
    if (this.failureCount >= this.maxFailures) {
      this.circuitBreakerOpenTime = Date.now();
      console.warn('OpenAI circuit breaker opened due to repeated failures');
    }

    // Enhance error message based on error type
    if (error.status === 429) {
      throw new Error('OpenAI API rate limit exceeded. Please try again later.');
    } else if (error.status === 401) {
      throw new Error('OpenAI API authentication failed. Check your API key.');
    } else if (error.status === 403) {
      throw new Error('OpenAI API access forbidden. Check your permissions.');
    } else if (error.status >= 500) {
      throw new Error('OpenAI API server error. Please try again later.');
    } else {
      throw error;
    }
  }

  /**
   * Check if circuit breaker is open
   * @returns {boolean} True if circuit breaker is open
   */
  isCircuitBreakerOpen() {
    if (!this.circuitBreakerOpenTime) {
      return false;
    }

    const timeSinceOpen = Date.now() - this.circuitBreakerOpenTime;
    if (timeSinceOpen > this.circuitBreakerTimeout) {
      // Reset circuit breaker
      this.circuitBreakerOpenTime = null;
      this.failureCount = 0;
      return false;
    }

    return true;
  }

  /**
   * Calculate cost based on token usage and model
   * @param {number} tokens - Total tokens used
   * @param {string} model - Model used
   * @returns {number} Cost in USD
   */
  calculateCost(tokens, model) {
    // Pricing as of 2024 (per 1K tokens)
    const pricing = {
      'gpt-3.5-turbo': 0.0015,
      'gpt-3.5-turbo-16k': 0.003,
      'gpt-4': 0.03,
      'gpt-4-32k': 0.06,
      'gpt-4-turbo': 0.01,
      'gpt-4o': 0.005
    };

    const pricePerToken = (pricing[model] || pricing['gpt-3.5-turbo']) / 1000;
    return tokens * pricePerToken;
  }

  /**
   * Get provider status and statistics
   * @returns {Object} Status information
   */
  getStatus() {
    return {
      provider: 'openai',
      model: this.model,
      circuitBreakerOpen: this.isCircuitBreakerOpen(),
      failureCount: this.failureCount,
      lastRequestTime: this.lastRequestTime,
      requestsPerMinute: this.requestsPerMinute
    };
  }

  /**
   * Test the connection to OpenAI API
   * @returns {Promise<boolean>} True if connection is successful
   */
  async testConnection() {
    try {
      const response = await this.generateCompletion('Test connection', {
        maxTokens: 10
      });
      return response.content !== null;
    } catch (error) {
      console.error('OpenAI connection test failed:', error.message);
      return false;
    }
  }
}

module.exports = OpenAIProvider;