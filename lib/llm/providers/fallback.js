/**
 * Fallback Provider for LLM Service
 * Provides basic template-based responses when OpenAI is unavailable
 */
class FallbackProvider {
  constructor(options = {}) {
    this.templates = {
      prediction: this.getPredictionTemplates(),
      trend: this.getTrendTemplates(),
      number: this.getNumberTemplates()
    };
  }

  /**
   * Generate fallback completion using templates
   * @param {string} prompt - The original prompt (used to determine type)
   * @param {Object} options - Additional options
   * @returns {Promise<Object>} Response with content and usage info
   */
  async generateCompletion(prompt, options = {}) {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 100));

    const type = this.detectPromptType(prompt);
    const template = this.selectTemplate(type);
    const content = this.fillTemplate(template, prompt);

    return {
      content,
      usage: {
        promptTokens: 0,
        completionTokens: content.length / 4, // Rough estimate
        totalTokens: content.length / 4,
        cost: 0
      },
      model: 'fallback',
      finishReason: 'stop'
    };
  }

  /**
   * Detect the type of prompt to select appropriate template
   * @param {string} prompt - The prompt to analyze
   * @returns {string} The detected type
   */
  detectPromptType(prompt) {
    const lowerPrompt = prompt.toLowerCase();

    if (lowerPrompt.includes('dự đoán') || lowerPrompt.includes('prediction')) {
      return 'prediction';
    } else if (lowerPrompt.includes('xu hướng') || lowerPrompt.includes('trend') ||
               lowerPrompt.includes('nóng') || lowerPrompt.includes('lạnh')) {
      return 'trend';
    } else if (lowerPrompt.includes('phân tích số') || lowerPrompt.includes('number')) {
      return 'number';
    }

    return 'prediction'; // Default
  }

  /**
   * Select a random template from the specified type
   * @param {string} type - Template type
   * @returns {string} Selected template
   */
  selectTemplate(type) {
    const templates = this.templates[type] || this.templates.prediction;
    return templates[Math.floor(Math.random() * templates.length)];
  }

  /**
   * Fill template with data extracted from prompt
   * @param {string} template - Template string
   * @param {string} prompt - Original prompt
   * @returns {string} Filled template
   */
  fillTemplate(template, prompt) {
    // Extract data from prompt (basic parsing)
    const data = this.extractDataFromPrompt(prompt);

    let filled = template;

    // Replace placeholders
    Object.keys(data).forEach(key => {
      const placeholder = `{${key}}`;
      filled = filled.replace(new RegExp(placeholder, 'g'), data[key]);
    });

    return filled;
  }

  /**
   * Extract relevant data from prompt
   * @param {string} prompt - The prompt to parse
   * @returns {Object} Extracted data
   */
  extractDataFromPrompt(prompt) {
    const data = {
      type: 'lô',
      confidence: '75',
      numbers: '12, 34, 56',
      hotNumbers: '12, 34',
      coldNumbers: '78, 90',
      frequency: '5',
      period: '30',
      lastSeen: '3 ngày trước'
    };

    // Try to extract actual numbers from prompt
    const numberMatches = prompt.match(/\d{2}/g);
    if (numberMatches && numberMatches.length > 0) {
      data.numbers = numberMatches.slice(0, 5).join(', ');
      data.hotNumbers = numberMatches.slice(0, 2).join(', ');
      data.coldNumbers = numberMatches.slice(-2).join(', ');
    }

    // Extract type (lô or đề)
    if (prompt.includes('đề')) {
      data.type = 'đề';
    }

    return data;
  }

  /**
   * Get prediction report templates
   * @returns {Array<string>} Array of templates
   */
  getPredictionTemplates() {
    return [
      `📊 **Báo Cáo Dự Đoán {type}**

🎯 **Phân tích dự đoán:**
Dựa trên phân tích thống kê và mô hình học máy, các số được dự đoán có độ tin cậy trung bình {confidence}%.

📈 **Xu hướng nhận định:**
• Các số {numbers} đang có xu hướng tích cực
• Dữ liệu lịch sử cho thấy mẫu tương tự đã xuất hiện
• Khuyến nghị theo dõi thêm các số liên quan

💡 **Lời khuyên:**
• Chỉ nên tham khảo, không đảm bảo kết quả
• Kết hợp với phân tích cá nhân
• Chơi có trách nhiệm và trong khả năng

⚠️ **Lưu ý:** Xổ số có tính ngẫu nhiên cao. Hãy cân nhắc kỹ trước khi quyết định.`,

      `🔍 **Phân Tích Dự Đoán {type}**

📊 **Đánh giá tổng quan:**
Hệ thống đã phân tích dữ liệu lịch sử và đưa ra dự đoán với độ tin cậy {confidence}%. Các số {numbers} được đánh giá có tiềm năng.

🎲 **Cơ sở dự đoán:**
• Phân tích tần suất xuất hiện
• Mô hình học máy LSTM
• Xu hướng thống kê gần đây

📋 **Khuyến nghị:**
• Theo dõi các số được dự đoán
• Kết hợp với kinh nghiệm cá nhân
• Không nên đầu tư quá mức

🚨 **Cảnh báo:** Kết quả chỉ mang tính tham khảo. Xổ số là trò chơi may rủi.`
    ];
  }

  /**
   * Get trend analysis templates
   * @returns {Array<string>} Array of templates
   */
  getTrendTemplates() {
    return [
      `📈 **Phân Tích Xu Hướng {type}**

🔥 **Số nóng (xuất hiện nhiều):**
Các số {hotNumbers} đang trong xu hướng "nóng", xuất hiện thường xuyên trong thời gian gần đây.

❄️ **Số lạnh (xuất hiện ít):**
Các số {coldNumbers} đang trong trạng thái "lạnh", ít xuất hiện hoặc chưa xuất hiện gần đây.

💭 **Nhận xét chuyên môn:**
• Số nóng có thể tiếp tục xu hướng hoặc "nguội" đi
• Số lạnh có thể sắp "nóng" lên theo quy luật cân bằng
• Cần theo dõi thêm để xác định xu hướng chính xác

⚖️ **Cân nhắc:** Xu hướng chỉ là tham khảo, không đảm bảo kết quả tương lai.`,

      `🌡️ **Báo Cáo Xu Hướng {type}**

📊 **Thống kê xu hướng:**
Dựa trên dữ liệu gần đây, số nóng {hotNumbers} và số lạnh {coldNumbers} cho thấy xu hướng rõ rệt.

🔄 **Chu kỳ phân tích:**
• Số nóng: Có thể duy trì hoặc chuyển sang trạng thái bình thường
• Số lạnh: Tiềm năng "bùng nổ" trong thời gian tới
• Cân bằng tự nhiên có thể xảy ra

💡 **Chiến lược:**
• Theo dõi sự thay đổi của số nóng
• Chú ý đến số lạnh có dấu hiệu "khởi sắc"
• Kết hợp cả hai nhóm trong lựa chọn

📝 **Kết luận:** Xu hướng là công cụ hỗ trợ, không thay thế cho quyết định cá nhân.`
    ];
  }

  /**
   * Get number analysis templates
   * @returns {Array<string>} Array of templates
   */
  getNumberTemplates() {
    return [
      `🔍 **Phân Tích Chi Tiết Số {numbers}**

📊 **Thống kê cơ bản:**
• Tần suất xuất hiện: {frequency} lần trong {period} ngày gần đây
• Lần xuất hiện cuối: {lastSeen}
• Trạng thái hiện tại: Đang được theo dõi

📈 **Đánh giá xu hướng:**
Số này có tần suất xuất hiện ở mức trung bình, cho thấy tính ổn định trong dữ liệu lịch sử.

🎯 **Khả năng tương lai:**
• Có tiềm năng xuất hiện dựa trên chu kỳ thống kê
• Cần theo dõi thêm để xác định xu hướng chính xác
• Kết hợp với các số liên quan để tăng hiệu quả

💭 **Nhận xét:** Đây là phân tích dựa trên dữ liệu thống kê, chỉ mang tính tham khảo.`,

      `📋 **Báo Cáo Số {numbers}**

🔢 **Thông tin số liệu:**
Số này đã xuất hiện {frequency} lần trong khoảng thời gian {period} ngày, với lần cuối cách đây {lastSeen}.

⚡ **Đặc điểm nổi bật:**
• Tần suất xuất hiện ổn định
• Có mối liên hệ với một số con số khác
• Thường xuất hiện trong các mẫu nhất định

🎲 **Dự báo khả năng:**
Dựa trên phân tích, số này có khả năng xuất hiện trong thời gian tới với xác suất trung bình.

⚠️ **Lưu ý quan trọng:** Phân tích chỉ dựa trên dữ liệu lịch sử, không đảm bảo kết quả chính xác.`
    ];
  }

  /**
   * Get provider status
   * @returns {Object} Status information
   */
  getStatus() {
    return {
      provider: 'fallback',
      model: 'template-based',
      circuitBreakerOpen: false,
      failureCount: 0,
      available: true
    };
  }

  /**
   * Test connection (always successful for fallback)
   * @returns {Promise<boolean>} Always true
   */
  async testConnection() {
    return true;
  }
}

module.exports = FallbackProvider;