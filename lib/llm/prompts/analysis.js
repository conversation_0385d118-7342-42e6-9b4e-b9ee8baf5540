/**
 * Analysis Prompt Templates
 * Templates for generating detailed number and pattern analysis prompts
 */

class AnalysisPrompts {
  /**
   * Generate number analysis prompt
   * @param {string} number - The number to analyze
   * @param {Object} frequency - Frequency data
   * @param {Array} patterns - Detected patterns
   * @param {Object} correlations - Number correlations
   * @returns {string} Formatted prompt
   */
  static generateNumberAnalysisPrompt(number, frequency, patterns, correlations = {}) {
    const frequencyText = this.formatFrequencyData(frequency);
    const patternsText = this.formatPatterns(patterns);
    const correlationsText = this.formatCorrelations(correlations);

    return `Bạn là chuyên gia phân tích xổ số miền Bắc. Hãy phân tích chi tiết số ${number} dựa trên dữ liệu thống kê.

**THÔNG TIN THỐNG KÊ:**
${frequencyText}

**CÁC MẪU PHÁT HIỆN:**
${patternsText}

**TƯƠNG QUAN VỚI SỐ KHÁC:**
${correlationsText}

**YÊU CẦU PHÂN TÍCH:**

1. **<PERSON><PERSON><PERSON> gi<PERSON> tần suất:**
   - <PERSON><PERSON> tích mức độ xuất hiện so với trung bình
   - Xu hướng tăng/giảm gần đây
   - So sánh với các số cùng nhóm

2. **Phân tích mẫu:**
   - Các mẫu xuất hiện đáng chú ý
   - Chu kỳ có thể có
   - Mối liên hệ với kết quả trước đó

3. **Dự báo khả năng:**
   - Khả năng xuất hiện trong tương lai
   - Các yếu tố ảnh hưởng
   - Thời điểm có thể xuất hiện

4. **Lời khuyên thực tế:**
   - Cách theo dõi số này
   - Kết hợp với số khác
   - Chiến lược chơi phù hợp

**ĐỊNH DẠNG:**
- Sử dụng emoji để làm nổi bật
- Viết ngắn gọn, dễ hiểu
- Đưa ra nhận xét khách quan
- Nhấn mạnh tính tham khảo

Hãy cung cấp phân tích chuyên sâu nhưng dễ hiểu cho người chơi.`;
  }

  /**
   * Generate pattern analysis prompt
   * @param {Array} patterns - Detected patterns
   * @param {string} timeframe - Analysis timeframe
   * @returns {string} Formatted prompt
   */
  static generatePatternAnalysisPrompt(patterns, timeframe = '30 ngày') {
    const patternsText = this.formatDetailedPatterns(patterns);

    return `Hãy phân tích các mẫu số được phát hiện trong xổ số miền Bắc trong ${timeframe} gần đây.

**CÁC MẪU PHÁT HIỆN:**
${patternsText}

**YÊU CẦU PHÂN TÍCH:**

1. **Đánh giá mẫu:**
   - Độ tin cậy của từng mẫu
   - Tần suất lặp lại
   - Ý nghĩa thống kê

2. **Xu hướng:**
   - Mẫu đang tăng/giảm
   - Chu kỳ xuất hiện
   - Dự báo tiếp theo

3. **Ứng dụng thực tế:**
   - Cách sử dụng mẫu trong dự đoán
   - Kết hợp các mẫu khác nhau
   - Giới hạn và rủi ro

4. **Khuyến nghị:**
   - Mẫu nào đáng theo dõi
   - Cách xác minh mẫu
   - Chiến lược dựa trên mẫu

Viết phân tích khoa học nhưng dễ hiểu, tránh đảm bảo chắc chắn.`;
  }

  /**
   * Generate correlation analysis prompt
   * @param {Object} correlations - Number correlation data
   * @param {Array} targetNumbers - Numbers to analyze correlations for
   * @returns {string} Formatted prompt
   */
  static generateCorrelationAnalysisPrompt(correlations, targetNumbers) {
    const correlationText = this.formatDetailedCorrelations(correlations, targetNumbers);

    return `Phân tích mối tương quan giữa các số trong xổ số miền Bắc.

**DỮ LIỆU TƯƠNG QUAN:**
${correlationText}

**YÊU CẦU PHÂN TÍCH:**

1. **Mối tương quan mạnh:**
   - Các cặp số có tương quan cao
   - Tần suất xuất hiện cùng nhau
   - Ý nghĩa thống kê

2. **Mối tương quan yếu:**
   - Các số ít khi xuất hiện cùng
   - Nguyên nhân có thể
   - Cơ hội trong tương lai

3. **Ứng dụng:**
   - Cách sử dụng tương quan trong dự đoán
   - Xây dựng bộ số dựa trên tương quan
   - Tránh các sai lầm thường gặp

4. **Cảnh báo:**
   - Giới hạn của phân tích tương quan
   - Tính ngẫu nhiên vẫn là yếu tố chính
   - Không nên phụ thuộc hoàn toàn

Viết phân tích cân bằng giữa khoa học và thực tế.`;
  }

  /**
   * Format frequency data for prompt
   * @param {Object} frequency - Frequency data object
   * @returns {string} Formatted frequency string
   */
  static formatFrequencyData(frequency) {
    if (!frequency) {
      return 'Không có dữ liệu tần suất';
    }

    return `- Tần suất: ${frequency.frequency || 0} lần trong ${frequency.period || 30} ngày
- Lần cuối xuất hiện: ${frequency.lastSeen ? new Date(frequency.lastSeen).toLocaleDateString('vi-VN') : 'Chưa rõ'}
- Trung bình: ${frequency.average || 'N/A'} lần/tháng
- Xu hướng: ${frequency.trend || 'Ổn định'}`;
  }

  /**
   * Format patterns for prompt
   * @param {Array} patterns - Array of pattern objects
   * @returns {string} Formatted patterns string
   */
  static formatPatterns(patterns) {
    if (!patterns || patterns.length === 0) {
      return 'Không phát hiện mẫu đặc biệt';
    }

    return patterns.map((pattern, index) => {
      return `${index + 1}. ${pattern.type || 'Mẫu'}: ${pattern.description || pattern.pattern || 'N/A'} (Độ tin cậy: ${pattern.confidence || 'N/A'}%)`;
    }).join('\n');
  }

  /**
   * Format correlations for prompt
   * @param {Object} correlations - Correlation data
   * @returns {string} Formatted correlations string
   */
  static formatCorrelations(correlations) {
    if (!correlations || Object.keys(correlations).length === 0) {
      return 'Không có dữ liệu tương quan';
    }

    const entries = Object.entries(correlations).slice(0, 5);
    return entries.map(([number, correlation]) => {
      return `- Với số ${number}: ${correlation.strength || 'N/A'}% (${correlation.frequency || 0} lần cùng xuất hiện)`;
    }).join('\n');
  }

  /**
   * Format detailed patterns for pattern analysis
   * @param {Array} patterns - Array of detailed pattern objects
   * @returns {string} Formatted detailed patterns string
   */
  static formatDetailedPatterns(patterns) {
    if (!patterns || patterns.length === 0) {
      return 'Không phát hiện mẫu nào';
    }

    return patterns.map((pattern, index) => {
      return `**Mẫu ${index + 1}: ${pattern.name || 'Không tên'}**
- Loại: ${pattern.type || 'N/A'}
- Mô tả: ${pattern.description || 'N/A'}
- Tần suất: ${pattern.frequency || 0} lần
- Độ tin cậy: ${pattern.confidence || 0}%
- Lần cuối: ${pattern.lastOccurrence ? new Date(pattern.lastOccurrence).toLocaleDateString('vi-VN') : 'N/A'}`;
    }).join('\n\n');
  }

  /**
   * Format detailed correlations
   * @param {Object} correlations - Correlation data
   * @param {Array} targetNumbers - Target numbers to focus on
   * @returns {string} Formatted detailed correlations string
   */
  static formatDetailedCorrelations(correlations, targetNumbers) {
    if (!correlations || Object.keys(correlations).length === 0) {
      return 'Không có dữ liệu tương quan chi tiết';
    }

    let result = '';

    if (targetNumbers && targetNumbers.length > 0) {
      targetNumbers.forEach(number => {
        if (correlations[number]) {
          result += `**Số ${number}:**\n`;
          const corr = correlations[number];
          result += `- Tương quan mạnh nhất: ${corr.strongest || 'N/A'}\n`;
          result += `- Tương quan yếu nhất: ${corr.weakest || 'N/A'}\n`;
          result += `- Trung bình: ${corr.average || 'N/A'}%\n\n`;
        }
      });
    } else {
      // Show top correlations
      const entries = Object.entries(correlations).slice(0, 3);
      entries.forEach(([number, data]) => {
        result += `**Số ${number}:** ${data.description || 'Tương quan với các số khác'}\n`;
      });
    }

    return result || 'Không có dữ liệu tương quan phù hợp';
  }
}

module.exports = AnalysisPrompts;