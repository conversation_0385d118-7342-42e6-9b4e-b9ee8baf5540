/**
 * Trends Prompt Templates
 * Templates for generating trend analysis prompts
 */

class TrendsPrompts {
  /**
   * Generate trend analysis prompt
   * @param {Object} trends - Trend data with hot and cold numbers
   * @param {string} type - Type of analysis ('lo' or 'de')
   * @param {string} period - Analysis period
   * @returns {string} Formatted prompt
   */
  static generateTrendAnalysisPrompt(trends, type = 'lo', period = '30 ngày') {
    const typeText = type === 'lo' ? 'số lô' : 'số đề';
    const hotNumbers = this.formatTrendNumbers(trends.hot, 'nóng');
    const coldNumbers = this.formatTrendNumbers(trends.cold, 'lạnh');
    const neutralNumbers = this.formatTrendNumbers(trends.neutral, 'trung bình');

    return `Bạn là chuyên gia phân tích xu hướng xổ số miền Bắc. Hãy phân tích xu hướng ${typeText} trong ${period} gần đây.

**SỐ NÓNG (xuất hiện nhiều):**
${hotNumbers}

**SỐ LẠNH (xuất hiện ít):**
${coldNumbers}

**SỐ TRUNG BÌNH:**
${neutralNumbers}

**YÊU CẦU PHÂN TÍCH:**

1. **Đánh giá xu hướng nóng:**
   - Phân tích tại sao các số này "nóng"
   - Khả năng duy trì xu hướng
   - Dấu hiệu có thể "nguội" đi

2. **Phân tích số lạnh:**
   - Nguyên nhân số lạnh
   - Tiềm năng "bùng nổ"
   - Thời điểm có thể chuyển biến

3. **Xu hướng tổng thể:**
   - Mẫu chung trong ${period}
   - So sánh với các giai đoạn trước
   - Dự báo xu hướng tiếp theo

4. **Chiến lược ứng dụng:**
   - Cách sử dụng số nóng
   - Khi nào chú ý số lạnh
   - Cân bằng trong lựa chọn

5. **Cảnh báo và lưu ý:**
   - Giới hạn của phân tích xu hướng
   - Tính ngẫu nhiên vẫn là yếu tố chính
   - Khuyến nghị chơi có trách nhiệm

**ĐỊNH DẠNG:**
- Sử dụng emoji phù hợp
- Viết ngắn gọn, dễ hiểu
- Cung cấp lời khuyên thực tế
- Nhấn mạnh tính tham khảo

Hãy viết phân tích chuyên nghiệp nhưng dễ tiếp cận với người chơi phổ thông.`;
  }

  /**
   * Generate weekly trend summary prompt
   * @param {Array} weeklyData - Weekly trend data
   * @param {string} type - Type ('lo' or 'de')
   * @returns {string} Formatted prompt
   */
  static generateWeeklyTrendPrompt(weeklyData, type = 'lo') {
    const typeText = type === 'lo' ? 'số lô' : 'số đề';
    const weeklyText = this.formatWeeklyData(weeklyData);

    return `Hãy tạo báo cáo xu hướng hàng tuần cho ${typeText} xổ số miền Bắc.

**DỮ LIỆU TUẦN:**
${weeklyText}

**YÊU CẦU BÁO CÁO:**

1. **Tóm tắt tuần:**
   - Số nổi bật nhất tuần
   - Xu hướng chính
   - Thay đổi so với tuần trước

2. **Phân tích chi tiết:**
   - Số tăng nhiệt
   - Số giảm nhiệt
   - Số ổn định

3. **Dự báo tuần tới:**
   - Số có thể tiếp tục nóng
   - Số có thể chuyển biến
   - Số cần theo dõi

4. **Khuyến nghị:**
   - Chiến lược cho tuần tới
   - Số nên chú ý
   - Cảnh báo rủi ro

Viết báo cáo ngắn gọn, súc tích và hữu ích cho người đọc.`;
  }

  /**
   * Generate monthly trend report prompt
   * @param {Object} monthlyData - Monthly trend statistics
   * @param {string} type - Type ('lo' or 'de')
   * @returns {string} Formatted prompt
   */
  static generateMonthlyTrendPrompt(monthlyData, type = 'lo') {
    const typeText = type === 'lo' ? 'số lô' : 'số đề';
    const monthlyText = this.formatMonthlyData(monthlyData);

    return `Tạo báo cáo xu hướng tháng cho ${typeText} xổ số miền Bắc.

**THỐNG KÊ THÁNG:**
${monthlyText}

**YÊU CẦU BÁO CÁO:**

1. **Tổng quan tháng:**
   - Xu hướng chủ đạo
   - Số thống trị tháng
   - Thay đổi lớn

2. **Phân tích sâu:**
   - Chu kỳ xuất hiện
   - Mẫu đặc biệt
   - Tương quan số

3. **So sánh lịch sử:**
   - So với tháng trước
   - So với cùng kỳ năm trước
   - Xu hướng dài hạn

4. **Dự báo tháng tới:**
   - Số có tiềm năng
   - Xu hướng có thể
   - Yếu tố cần chú ý

5. **Kết luận và khuyến nghị:**
   - Bài học từ tháng qua
   - Chiến lược tháng tới
   - Lời khuyên tổng quát

Viết báo cáo toàn diện nhưng dễ hiểu.`;
  }

  /**
   * Generate trend comparison prompt
   * @param {Object} currentTrends - Current period trends
   * @param {Object} previousTrends - Previous period trends
   * @param {string} type - Type ('lo' or 'de')
   * @returns {string} Formatted prompt
   */
  static generateTrendComparisonPrompt(currentTrends, previousTrends, type = 'lo') {
    const typeText = type === 'lo' ? 'số lô' : 'số đề';
    const currentText = this.formatTrendsForComparison(currentTrends, 'Hiện tại');
    const previousText = this.formatTrendsForComparison(previousTrends, 'Trước đó');

    return `So sánh xu hướng ${typeText} giữa hai giai đoạn.

**GIAI ĐOẠN HIỆN TẠI:**
${currentText}

**GIAI ĐOẠN TRƯỚC ĐÓ:**
${previousText}

**YÊU CẦU SO SÁNH:**

1. **Thay đổi xu hướng:**
   - Số chuyển từ lạnh sang nóng
   - Số chuyển từ nóng sang lạnh
   - Số duy trì xu hướng

2. **Phân tích nguyên nhân:**
   - Tại sao có sự thay đổi
   - Yếu tố ảnh hưởng
   - Tính chu kỳ

3. **Đánh giá ý nghĩa:**
   - Thay đổi có ý nghĩa thống kê không
   - Xu hướng ngắn hạn hay dài hạn
   - Khả năng dự đoán

4. **Dự báo tiếp theo:**
   - Xu hướng có thể tiếp tục
   - Số có thể đảo chiều
   - Cơ hội mới xuất hiện

Viết so sánh khách quan và có căn cứ.`;
  }

  /**
   * Format trend numbers for prompt
   * @param {Array} numbers - Array of number objects with frequency
   * @param {string} category - Category name (nóng, lạnh, etc.)
   * @returns {string} Formatted numbers string
   */
  static formatTrendNumbers(numbers, category) {
    if (!numbers || numbers.length === 0) {
      return `Không có số ${category}`;
    }

    return numbers.map((num, index) => {
      const frequency = num.frequency || num.count || 0;
      const percentage = num.percentage || 0;
      return `${index + 1}. Số ${num.number}: ${frequency} lần (${percentage.toFixed(1)}%)`;
    }).join('\n');
  }

  /**
   * Format weekly data for prompt
   * @param {Array} weeklyData - Weekly trend data
   * @returns {string} Formatted weekly data string
   */
  static formatWeeklyData(weeklyData) {
    if (!weeklyData || weeklyData.length === 0) {
      return 'Không có dữ liệu tuần';
    }

    return weeklyData.map(week => {
      const weekStart = new Date(week.startDate).toLocaleDateString('vi-VN');
      const weekEnd = new Date(week.endDate).toLocaleDateString('vi-VN');
      const topNumbers = week.topNumbers?.slice(0, 3).map(n => n.number).join(', ') || 'N/A';

      return `**Tuần ${weekStart} - ${weekEnd}:**
- Số nổi bật: ${topNumbers}
- Tổng lần quay: ${week.totalDraws || 0}
- Xu hướng: ${week.trend || 'Ổn định'}`;
    }).join('\n\n');
  }

  /**
   * Format monthly data for prompt
   * @param {Object} monthlyData - Monthly statistics
   * @returns {string} Formatted monthly data string
   */
  static formatMonthlyData(monthlyData) {
    if (!monthlyData) {
      return 'Không có dữ liệu tháng';
    }

    const topNumbers = monthlyData.topNumbers?.slice(0, 5).map(n => `${n.number} (${n.frequency} lần)`).join(', ') || 'N/A';
    const bottomNumbers = monthlyData.bottomNumbers?.slice(0, 5).map(n => `${n.number} (${n.frequency} lần)`).join(', ') || 'N/A';

    return `**Thống kê tháng ${monthlyData.month}/${monthlyData.year}:**
- Tổng lần quay: ${monthlyData.totalDraws || 0}
- Số xuất hiện nhiều nhất: ${topNumbers}
- Số xuất hiện ít nhất: ${bottomNumbers}
- Trung bình mỗi số: ${monthlyData.averagePerNumber || 0} lần
- Độ lệch chuẩn: ${monthlyData.standardDeviation || 0}`;
  }

  /**
   * Format trends for comparison
   * @param {Object} trends - Trend data
   * @param {string} label - Label for the period
   * @returns {string} Formatted trends string
   */
  static formatTrendsForComparison(trends, label) {
    if (!trends) {
      return `${label}: Không có dữ liệu`;
    }

    const hot = trends.hot?.slice(0, 3).map(n => n.number).join(', ') || 'Không có';
    const cold = trends.cold?.slice(0, 3).map(n => n.number).join(', ') || 'Không có';

    return `**${label}:**
- Số nóng: ${hot}
- Số lạnh: ${cold}
- Tổng lần quay: ${trends.totalDraws || 0}
- Giai đoạn: ${trends.period || 'N/A'}`;
  }
}

module.exports = TrendsPrompts;