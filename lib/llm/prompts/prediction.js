/**
 * Prediction Prompt Templates
 * Templates for generating prediction analysis prompts
 */

class PredictionPrompts {
  /**
   * Generate prediction analysis prompt
   * @param {Array} predictions - Array of prediction objects
   * @param {Array} historicalData - Historical lottery data
   * @param {string} type - Type of prediction ('lo' or 'de')
   * @returns {string} Formatted prompt
   */
  static generatePredictionPrompt(predictions, historicalData, type = 'lo') {
    const typeText = type === 'lo' ? 'số lô' : 'số đề';
    const recentResults = this.formatRecentResults(historicalData.slice(-5));
    const predictionList = this.formatPredictions(predictions);

    return `Bạn là chuyên gia phân tích xổ số miền Bắc với 20 năm kinh nghiệm. Hãy phân tích dự đoán ${typeText} sau đây một cách chuyên nghiệp và khách quan.

**DỮ LIỆU DỰ ĐOÁN:**
${predictionList}

**KẾT QUẢ GẦN ĐÂY (5 ngày):**
${recentResults}

**YÊU CẦU PHÂN TÍCH:**
Hãy viết báo cáo phân tích bằng tiếng Việt, bao gồm:

1. **Đánh giá độ tin cậy:** Phân tích mức độ tin cậy của từng dự đoán dựa trên:
   - Độ tin cậy từ mô hình ML
   - So sánh với xu hướng lịch sử
   - Tần suất xuất hiện gần đây

2. **Phân tích xu hướng:** Dựa trên dữ liệu lịch sử:
   - Các mẫu số đã xuất hiện tương tự
   - Chu kỳ xuất hiện của các số được dự đoán
   - Mối liên hệ giữa các số

3. **Lời khuyên thực tế:**
   - Chiến lược chơi dựa trên dự đoán
   - Cách kết hợp với kinh nghiệm cá nhân
   - Quản lý rủi ro và ngân sách

4. **Cảnh báo rủi ro:**
   - Tính ngẫu nhiên của xổ số
   - Giới hạn của dự đoán
   - Khuyến nghị chơi có trách nhiệm

**ĐỊNH DẠNG:**
- Sử dụng emoji phù hợp để làm nổi bật
- Viết ngắn gọn, dễ hiểu
- Tránh đảm bảo kết quả chắc chắn
- Nhấn mạnh tính tham khảo

Hãy viết báo cáo chuyên nghiệp nhưng dễ hiểu cho người chơi phổ thông.`;
  }

  /**
   * Generate comparison prompt for prediction accuracy
   * @param {Array} predictions - Previous predictions
   * @param {Object} actualResult - Actual lottery result
   * @param {string} type - Type ('lo' or 'de')
   * @returns {string} Formatted prompt
   */
  static generateAccuracyAnalysisPrompt(predictions, actualResult, type = 'lo') {
    const typeText = type === 'lo' ? 'số lô' : 'số đề';
    const predictionList = this.formatPredictions(predictions);
    const resultNumbers = type === 'lo' ? actualResult.numbers.lo : actualResult.numbers.de;

    return `Hãy phân tích độ chính xác của dự đoán ${typeText} so với kết quả thực tế.

**DỰ ĐOÁN TRƯỚC ĐÓ:**
${predictionList}

**KẾT QUẢ THỰC TẾ:**
${resultNumbers.join(', ')}

**YÊU CẦU PHÂN TÍCH:**
1. **Đánh giá độ chính xác:**
   - Số lượng dự đoán trúng
   - Tỷ lệ chính xác tổng thể
   - So sánh với độ tin cậy dự đoán

2. **Phân tích sai lệch:**
   - Nguyên nhân có thể gây sai lệch
   - Các yếu tố không dự đoán được
   - Bài học rút ra

3. **Cải thiện mô hình:**
   - Đề xuất điều chỉnh
   - Yếu tố cần chú ý thêm
   - Hướng phát triển

Viết phân tích khách quan và mang tính xây dựng.`;
  }

  /**
   * Format predictions for prompt
   * @param {Array} predictions - Array of prediction objects
   * @returns {string} Formatted prediction string
   */
  static formatPredictions(predictions) {
    if (!predictions || predictions.length === 0) {
      return 'Không có dự đoán';
    }

    return predictions.map((pred, index) => {
      return `${index + 1}. Số ${pred.number} - Độ tin cậy: ${pred.confidence}% (${pred.method || 'unknown'})`;
    }).join('\n');
  }

  /**
   * Format recent results for prompt
   * @param {Array} results - Array of recent lottery results
   * @returns {string} Formatted results string
   */
  static formatRecentResults(results) {
    if (!results || results.length === 0) {
      return 'Không có dữ liệu gần đây';
    }

    return results.map(result => {
      const date = new Date(result.date).toLocaleDateString('vi-VN');
      const loNumbers = result.numbers?.lo?.slice(0, 5).join(', ') || 'N/A';
      const deNumbers = result.numbers?.de?.slice(0, 3).join(', ') || 'N/A';
      return `${date}: Lô [${loNumbers}], Đề [${deNumbers}]`;
    }).join('\n');
  }

  /**
   * Generate prompt for prediction explanation
   * @param {Object} prediction - Single prediction object
   * @param {Array} supportingData - Supporting statistical data
   * @returns {string} Formatted prompt
   */
  static generateExplanationPrompt(prediction, supportingData) {
    return `Hãy giải thích tại sao số ${prediction.number} được dự đoán với độ tin cậy ${prediction.confidence}%.

**THÔNG TIN HỖ TRỢ:**
- Phương pháp: ${prediction.method || 'Không xác định'}
- Tần suất xuất hiện gần đây: ${supportingData.frequency || 'N/A'}
- Lần cuối xuất hiện: ${supportingData.lastSeen || 'N/A'}
- Xu hướng: ${supportingData.trend || 'N/A'}

**YÊU CẦU:**
Giải thích ngắn gọn (2-3 câu) lý do dự đoán này, sử dụng ngôn ngữ dễ hiểu cho người chơi phổ thông.

Tránh đảm bảo chắc chắn, nhấn mạnh tính tham khảo.`;
  }
}

module.exports = PredictionPrompts;