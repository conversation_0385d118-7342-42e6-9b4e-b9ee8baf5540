const redis = require('redis')
const logger = require('../../logger')('./logs')

// Load environment variables
require('dotenv').config()

let connections = {}

// Function to get Redis config from environment variables
function getRedisConfig() {
  return {
    connections: {
      default: {
        host: process.env.REDIS_DEFAULT_HOST || 'localhost',
        port: parseInt(process.env.REDIS_DEFAULT_PORT) || 6379,
        database: parseInt(process.env.REDIS_DEFAULT_DATABASE) || 0,
        password: process.env.REDIS_DEFAULT_PASSWORD || ''
      },
      master: {
        host: process.env.REDIS_MASTER_HOST || 'localhost',
        port: parseInt(process.env.REDIS_MASTER_PORT) || 6379,
        database: parseInt(process.env.REDIS_MASTER_DATABASE) || 0,
        password: process.env.REDIS_MASTER_PASSWORD || ''
      }
    }
  }
}

class RedisConnection {
  constructor(name, options) {
    this.connection = null
    this.options = options
    this.name = name

    this.init()
  }

  init() {
    this.connection = redis.createClient(this.options)

    this.connection.on('connect', () => {
      logger.logInfo(`[REDIS-${this.name}] - CONNECTED`)
    })

    this.connection.on('error', (err) => {
      logger.logError(`[REDIS-${this.name}]`, err)
    })
    this.connection.on('ready', (err) => {
      logger.logInfo(`[REDIS-${this.name}] - READY`)
      setInterval(() => {
        this.connection.ping((err,data) => {
          if (err) console.error('Redis keepalive error', err);
        });
      }, 30000);
    })
  }

  getConnection() {
    return this.connection
  }
}

function setUp() {
  // Get config from environment variables instead of config file
  const redisConfig = getRedisConfig().connections
  Object.keys(redisConfig).forEach((name) => {
    connections[name] = new RedisConnection(name, redisConfig[name])
  })
}

setUp()

module.exports = (name) => {
  return connections[name] || null
}
