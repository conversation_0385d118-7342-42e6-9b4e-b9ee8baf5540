const mongoose = require('mongoose')
const MailUtil = require('../../util/mail');
const logger = require('../../logger')('./logs');

// Load environment variables
require('dotenv').config()

let connections = {}

// Function to get MongoDB config from environment variables
function getMongoConfig() {
  return {
    connections: {
      default: {
        host: process.env.MONGO_DEFAULT_HOST || 'localhost',
        port: parseInt(process.env.MONGO_DEFAULT_PORT) || 27017,
        database: process.env.MONGO_DEFAULT_DATABASE || 'lottery_bot_dev',
        options: {
          useUnifiedTopology: process.env.MONGO_USE_UNIFIED_TOPOLOGY === 'true',
          useNewUrlParser: process.env.MONGO_USE_NEW_URL_PARSER === 'true',
          user: process.env.MONGO_DEFAULT_USER || '',
          pass: process.env.MONGO_DEFAULT_PASS || ''
        }
      },
      master: {
        host: process.env.MONGO_MASTER_HOST || 'localhost',
        port: parseInt(process.env.MONGO_MASTER_PORT) || 27017,
        database: process.env.MONGO_MASTER_DATABASE || 'lottery_bot_dev',
        options: {
          useUnifiedTopology: process.env.MONGO_USE_UNIFIED_TOPOLOGY === 'true',
          useNewUrlParser: process.env.MONGO_USE_NEW_URL_PARSER === 'true',
          user: process.env.MONGO_MASTER_USER || '',
          pass: process.env.MONGO_MASTER_PASS || ''
        }
      }
    }
  }
}

class MongoConnection {
  constructor(name, config) {
    this.connection = null
    this.config = config
    this.name = name

    this.init()
  }

  init() {
    const uri = generateUri(this.config.host, this.config.port, this.config.database, this.config.instances, this.config.mode)
    const options = this.config.options || {}

    this.connection = mongoose.createConnection(uri, options)

    this.connection.on('connected', () => {
      logger.logInfo(`[MONGO-${this.name}] - CONNECTED`)
    })

    this.connection.on('error', (err) => {
      MailUtil.sendMail(`[MONGO-${this.name}] - ${err}`);
      logger.logError([`[MONGO-${this.name}]`, err], __dirname)
    })

    this.connection.on('disconnected', () => {
      logger.logError([`[MONGO-${this.name}] - DISCONNECTED`])
      MailUtil.sendMail(`[MONGO-${this.name}] - DISCONNECTED`)
    })
  }

  getConnection() {
    return this.connection
  }
}

function setUp() {
  // Get config from environment variables instead of config file
  const mongoConfig = getMongoConfig().connections
  Object.keys(mongoConfig).forEach((name) => {
    connections[name] = new MongoConnection(name, mongoConfig[name])
  })
}

setUp()

function generateUri(host, port, database, instances, mode) {
  let link = `mongodb://${host}:${port}/${database}`;
  if(mode === 'replicaset' && instances && instances.length) {
    link = `mongodb://`;
    instances.forEach((instance) => {
      link += `${instance.host}:${instance.port},`;
    })
    link = link.slice(0, -1);

    link += `/${database}`;
  }

  return link;
}

module.exports = (name) => {
  const connection = connections[name]
  if(connection) {
    const mongooseConnection = connection.getConnection()
    // Return the connection object that managers expect
    return {
      db: mongooseConnection.db || mongooseConnection,
      connection: mongooseConnection
    }
  }

  return null
}