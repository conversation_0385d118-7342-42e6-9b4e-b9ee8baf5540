// Model Training and Evaluation System
// Handles automated retraining, accuracy tracking, and model versioning

const path = require('path');
const fs = require('fs').promises;
const LSTMPredictor = require('../models/lstm');
const StatisticalModel = require('../models/statistical');
const EnsembleModel = require('../models/ensemble');

class ModelTrainer {
  constructor(options = {}) {
    this.options = {
      modelTypes: options.modelTypes || ['lstm', 'statistical', 'ensemble'],
      retrainingThreshold: options.retrainingThreshold || 0.15, // 15% accuracy threshold
      evaluationWindow: options.evaluationWindow || 100, // Number of predictions to evaluate
      modelSaveDir: options.modelSaveDir || './models',
      maxModelVersions: options.maxModelVersions || 5,
      autoRetrain: options.autoRetrain !== false, // Default to true
      ...options
    };

    this.models = new Map();
    this.accuracyHistory = new Map();
    this.trainingQueue = [];
    this.isTraining = false;
    this.evaluationResults = new Map();
  }

  /**
   * Initialize all model types
   */
  async initialize() {
    try {
      // Ensure model directory exists
      await this.ensureModelDirectory();

      // Initialize models
      if (this.options.modelTypes.includes('lstm')) {
        this.models.set('lstm', new LSTMPredictor());
      }

      if (this.options.modelTypes.includes('statistical')) {
        this.models.set('statistical', new StatisticalModel());
      }

      if (this.options.modelTypes.includes('ensemble')) {
        const baseModels = Array.from(this.models.values());
        this.models.set('ensemble', new EnsembleModel({ models: baseModels }));
      }

      // Load existing models if available
      await this.loadExistingModels();

      console.log(`ModelTrainer initialized with ${this.models.size} model types`);
      return true;
    } catch (error) {
      console.error('Error initializing ModelTrainer:', error);
      throw new Error(`Failed to initialize ModelTrainer: ${error.message}`);
    }
  }

  /**
   * Train all models with provided data
   * @param {Array} trainingData - Historical lottery data
   * @param {Object} options - Training options
   */
  async trainModels(trainingData, options = {}) {
    if (this.isTraining) {
      throw new Error('Training already in progress');
    }

    this.isTraining = true;
    const trainingResults = new Map();

    try {
      console.log(`Starting training for ${this.models.size} models with ${trainingData.length} data points`);

      for (const [modelType, model] of this.models) {
        try {
          console.log(`Training ${modelType} model...`);
          const startTime = Date.now();

          const result = await model.train(trainingData, options);
          const trainingTime = Date.now() - startTime;

          const trainingResult = {
            modelType,
            success: true,
            trainingTime,
            timestamp: new Date(),
            ...result
          };

          trainingResults.set(modelType, trainingResult);

          // Save trained model
          await this.saveModel(modelType, model);

          console.log(`${modelType} model trained successfully in ${trainingTime}ms`);
        } catch (error) {
          console.error(`Error training ${modelType} model:`, error);
          trainingResults.set(modelType, {
            modelType,
            success: false,
            error: error.message,
            timestamp: new Date()
          });
        }
      }

      // Update training history
      await this.updateTrainingHistory(trainingResults);

      return trainingResults;
    } catch (error) {
      console.error('Error during model training:', error);
      throw error;
    } finally {
      this.isTraining = false;
    }
  }

  /**
   * Evaluate model accuracy against recent predictions
   * @param {Array} testData - Test data for evaluation
   * @param {Object} options - Evaluation options
   */
  async evaluateModels(testData, options = {}) {
    const evaluationResults = new Map();

    try {
      console.log(`Evaluating ${this.models.size} models with ${testData.length} test samples`);

      for (const [modelType, model] of this.models) {
        try {
          const evaluation = await this.evaluateModel(modelType, model, testData, options);
          evaluationResults.set(modelType, evaluation);

          // Update accuracy history
          this.updateAccuracyHistory(modelType, evaluation);

          console.log(`${modelType} model accuracy: ${evaluation.accuracy.toFixed(2)}%`);
        } catch (error) {
          console.error(`Error evaluating ${modelType} model:`, error);
          evaluationResults.set(modelType, {
            modelType,
            accuracy: 0,
            error: error.message,
            timestamp: new Date()
          });
        }
      }

      // Store evaluation results
      this.evaluationResults = evaluationResults;

      // Check if retraining is needed
      if (this.options.autoRetrain) {
        await this.checkRetrainingNeeded();
      }

      return evaluationResults;
    } catch (error) {
      console.error('Error during model evaluation:', error);
      throw error;
    }
  }

  /**
   * Evaluate a single model
   * @param {string} modelType - Type of model
   * @param {Object} model - Model instance
   * @param {Array} testData - Test data
   * @param {Object} options - Evaluation options
   */
  async evaluateModel(modelType, model, testData, options = {}) {
    const sampleSize = Math.min(options.sampleSize || this.options.evaluationWindow, testData.length);
    let correctPredictions = 0;
    let totalPredictions = 0;
    const predictions = [];

    for (let i = 0; i < sampleSize; i++) {
      try {
        const testSample = testData[i];

        // Create input sequence from historical data
        const inputSequence = this.createInputSequence(testData, i);
        if (!inputSequence || inputSequence.length === 0) continue;

        // Get model prediction
        const prediction = await model.predict(inputSequence);
        const predictedNumbers = this.extractPredictedNumbers(prediction);

        // Get actual numbers from test sample
        const actualNumbers = this.extractActualNumbers(testSample);

        // Calculate accuracy
        const accuracy = this.calculatePredictionAccuracy(predictedNumbers, actualNumbers);

        predictions.push({
          predicted: predictedNumbers,
          actual: actualNumbers,
          accuracy: accuracy,
          timestamp: testSample.date || new Date()
        });

        if (accuracy > 0) {
          correctPredictions += accuracy;
        }
        totalPredictions++;

      } catch (error) {
        console.warn(`Error evaluating sample ${i} for ${modelType}:`, error.message);
      }
    }

    const overallAccuracy = totalPredictions > 0 ? (correctPredictions / totalPredictions) * 100 : 0;

    return {
      modelType,
      accuracy: overallAccuracy,
      correctPredictions,
      totalPredictions,
      sampleSize,
      predictions: predictions.slice(0, 10), // Keep only first 10 for storage
      timestamp: new Date()
    };
  }

  /**
   * Check if models need retraining based on accuracy thresholds
   */
  async checkRetrainingNeeded() {
    const modelsNeedingRetraining = [];

    for (const [modelType, evaluation] of this.evaluationResults) {
      if (evaluation.accuracy < this.options.retrainingThreshold * 100) {
        modelsNeedingRetraining.push(modelType);
      }
    }

    if (modelsNeedingRetraining.length > 0) {
      console.log(`Models needing retraining: ${modelsNeedingRetraining.join(', ')}`);

      // Add to training queue
      this.trainingQueue.push({
        models: modelsNeedingRetraining,
        reason: 'accuracy_threshold',
        timestamp: new Date()
      });

      // Trigger retraining if not already in progress
      if (!this.isTraining && this.options.autoRetrain) {
        // Note: Actual retraining would be triggered by external scheduler
        console.log('Retraining queued for models:', modelsNeedingRetraining);
      }
    }

    return modelsNeedingRetraining;
  }

  /**
   * Get model versioning information
   * @param {string} modelType - Type of model
   */
  async getModelVersions(modelType) {
    try {
      const modelDir = path.join(this.options.modelSaveDir, modelType);
      const versionsFile = path.join(modelDir, 'versions.json');

      if (await this.fileExists(versionsFile)) {
        const content = await fs.readFile(versionsFile, 'utf8');
        return JSON.parse(content);
      }

      return { versions: [], current: null };
    } catch (error) {
      console.error(`Error getting model versions for ${modelType}:`, error);
      return { versions: [], current: null };
    }
  }

  /**
   * Save model with versioning
   * @param {string} modelType - Type of model
   * @param {Object} model - Model instance
   */
  async saveModel(modelType, model) {
    try {
      const modelDir = path.join(this.options.modelSaveDir, modelType);
      await fs.mkdir(modelDir, { recursive: true });

      // Generate version ID
      const versionId = `v${Date.now()}`;
      const versionDir = path.join(modelDir, versionId);

      // Save model
      await model.saveModel(versionDir);

      // Update versions file
      const versions = await this.getModelVersions(modelType);
      versions.versions.push({
        id: versionId,
        timestamp: new Date(),
        accuracy: this.evaluationResults.get(modelType)?.accuracy || 0,
        trainingData: 'historical_lottery_data'
      });

      // Keep only max versions
      if (versions.versions.length > this.options.maxModelVersions) {
        const oldVersions = versions.versions.splice(0, versions.versions.length - this.options.maxModelVersions);

        // Clean up old version directories
        for (const oldVersion of oldVersions) {
          try {
            const oldVersionDir = path.join(modelDir, oldVersion.id);
            await fs.rmdir(oldVersionDir, { recursive: true });
          } catch (error) {
            console.warn(`Could not remove old version ${oldVersion.id}:`, error.message);
          }
        }
      }

      versions.current = versionId;

      const versionsFile = path.join(modelDir, 'versions.json');
      await fs.writeFile(versionsFile, JSON.stringify(versions, null, 2));

      console.log(`Model ${modelType} saved as version ${versionId}`);
      return versionId;
    } catch (error) {
      console.error(`Error saving model ${modelType}:`, error);
      throw error;
    }
  }

  /**
   * Load existing models from disk
   */
  async loadExistingModels() {
    for (const [modelType, model] of this.models) {
      try {
        const versions = await this.getModelVersions(modelType);
        if (versions.current) {
          const modelPath = path.join(this.options.modelSaveDir, modelType, versions.current);
          await model.loadModel(modelPath);
          console.log(`Loaded ${modelType} model version ${versions.current}`);
        }
      } catch (error) {
        console.warn(`Could not load existing ${modelType} model:`, error.message);
      }
    }
  }

  /**
   * Update accuracy history for a model
   * @param {string} modelType - Type of model
   * @param {Object} evaluation - Evaluation results
   */
  updateAccuracyHistory(modelType, evaluation) {
    if (!this.accuracyHistory.has(modelType)) {
      this.accuracyHistory.set(modelType, []);
    }

    const history = this.accuracyHistory.get(modelType);
    history.push({
      accuracy: evaluation.accuracy,
      timestamp: evaluation.timestamp,
      sampleSize: evaluation.sampleSize
    });

    // Keep only recent history (last 100 evaluations)
    if (history.length > 100) {
      history.splice(0, history.length - 100);
    }
  }

  /**
   * Get comprehensive training and evaluation statistics
   */
  getTrainingStats() {
    const stats = {
      models: {},
      trainingQueue: this.trainingQueue,
      isTraining: this.isTraining,
      lastEvaluation: null
    };

    // Get stats for each model
    for (const [modelType, model] of this.models) {
      const evaluation = this.evaluationResults.get(modelType);
      const accuracyHistory = this.accuracyHistory.get(modelType) || [];

      stats.models[modelType] = {
        info: model.getModelInfo ? model.getModelInfo() : { type: modelType },
        currentAccuracy: evaluation?.accuracy || 0,
        accuracyHistory: accuracyHistory.slice(-10), // Last 10 evaluations
        needsRetraining: evaluation ? evaluation.accuracy < this.options.retrainingThreshold * 100 : false
      };
    }

    // Find most recent evaluation
    let mostRecent = null;
    for (const evaluation of this.evaluationResults.values()) {
      if (!mostRecent || evaluation.timestamp > mostRecent) {
        mostRecent = evaluation.timestamp;
      }
    }
    stats.lastEvaluation = mostRecent;

    return stats;
  }

  /**
   * Helper methods
   */
  async ensureModelDirectory() {
    await fs.mkdir(this.options.modelSaveDir, { recursive: true });
  }

  async fileExists(filePath) {
    try {
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }

  createInputSequence(data, currentIndex) {
    // Create input sequence from historical data
    const sequenceLength = 30; // Default sequence length
    if (currentIndex < sequenceLength) return null;

    const sequence = [];
    for (let i = currentIndex - sequenceLength; i < currentIndex; i++) {
      const sample = data[i];
      if (sample.numbers) {
        if (sample.numbers.lo) sequence.push(...sample.numbers.lo.map(n => parseInt(n, 10)));
        if (sample.numbers.de) sequence.push(...sample.numbers.de.map(n => parseInt(n, 10)));
      }
    }

    return sequence.slice(-sequenceLength); // Take last sequenceLength numbers
  }

  extractPredictedNumbers(prediction) {
    if (prediction.predictions && Array.isArray(prediction.predictions)) {
      return prediction.predictions.slice(0, 5).map(p => parseInt(p.number, 10));
    }
    return [];
  }

  extractActualNumbers(testSample) {
    const numbers = [];
    if (testSample.numbers) {
      if (testSample.numbers.lo) numbers.push(...testSample.numbers.lo.map(n => parseInt(n, 10)));
      if (testSample.numbers.de) numbers.push(...testSample.numbers.de.map(n => parseInt(n, 10)));
    }
    return numbers;
  }

  calculatePredictionAccuracy(predicted, actual) {
    if (!predicted.length || !actual.length) return 0;

    let matches = 0;
    for (const pred of predicted) {
      if (actual.includes(pred)) {
        matches++;
      }
    }

    return matches / predicted.length;
  }

  async updateTrainingHistory(trainingResults) {
    try {
      const historyFile = path.join(this.options.modelSaveDir, 'training_history.json');
      let history = [];

      if (await this.fileExists(historyFile)) {
        const content = await fs.readFile(historyFile, 'utf8');
        history = JSON.parse(content);
      }

      history.push({
        timestamp: new Date(),
        results: Object.fromEntries(trainingResults)
      });

      // Keep only recent history
      if (history.length > 50) {
        history = history.slice(-50);
      }

      await fs.writeFile(historyFile, JSON.stringify(history, null, 2));
    } catch (error) {
      console.error('Error updating training history:', error);
    }
  }

  /**
   * Dispose of all models and free memory
   */
  dispose() {
    for (const model of this.models.values()) {
      if (model.dispose) {
        model.dispose();
      }
    }
    this.models.clear();
    this.accuracyHistory.clear();
    this.evaluationResults.clear();
  }
}

module.exports = ModelTrainer;