// Statistical prediction model
// Uses enhanced statistical analysis engine

const StatisticalAnalyzer = require('../analyzers/statisticalAnalyzer');

class StatisticalPredictor {
  constructor(options = {}) {
    this.options = {
      lookbackPeriod: options.lookbackPeriod || 100,
      minFrequency: options.minFrequency || 3,
      ...options
    };

    this.analyzer = new StatisticalAnalyzer(this.options);
  }

  async predict(historicalData, type = 'lo') {
    // Use enhanced statistical analyzer for predictions
    const limitedData = historicalData.slice(-this.options.lookbackPeriod);

    return this.analyzer.generatePredictions(limitedData, type, {
      count: 10,
      includeReasons: true,
      weightHotNumbers: true,
      considerPatterns: true
    });
  }

  calculateFrequencies(data, type) {
    // Delegate to analyzer
    return this.analyzer.calculateFrequencies(data, type);
  }

  calculateProbabilities(frequencies) {
    // Delegate to analyzer
    return this.analyzer.calculateProbabilities(frequencies);
  }

  generatePredictions(probabilities) {
    // Legacy method - convert probabilities to old format
    const sorted = Array.from(probabilities.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10);

    return sorted.map(([number, prob]) => ({
      number,
      confidence: Math.round(prob * 100),
      reasoning: `Xuất hiện với tần suất ${(prob * 100).toFixed(1)}%`
    }));
  }

  calculateConfidence(probabilities) {
    // Legacy method - basic confidence calculation
    const values = Array.from(probabilities.values());
    if (values.length === 0) return 0;

    const max = Math.max(...values);
    const avg = values.reduce((sum, val) => sum + val, 0) / values.length;

    return Math.round((max / avg) * 10); // Normalize to 0-100 scale
  }

  analyzeHotColdNumbers(data, type, period = 30) {
    // Use enhanced analyzer
    const analysis = this.analyzer.analyzeHotColdNumbers(data, type, {
      shortPeriod: period,
      mediumPeriod: period * 2,
      longPeriod: period * 3
    });

    // Convert to legacy format for backward compatibility
    return {
      hot: analysis.hot.slice(0, 5).map(item => ({
        number: item.number,
        frequency: Math.round(item.probability * data.length)
      })),
      cold: analysis.cold.slice(0, 5).map(item => ({
        number: item.number,
        frequency: Math.round(item.probability * data.length)
      }))
    };
  }

  // New enhanced methods
  analyzeNumber(number, historicalData, type = 'lo') {
    return this.analyzer.analyzeNumber(number, historicalData, type);
  }

  detectPatterns(historicalData, type = 'lo', options = {}) {
    return this.analyzer.detectPatterns(historicalData, type, options);
  }

  getDetailedHotColdAnalysis(historicalData, type = 'lo', options = {}) {
    return this.analyzer.analyzeHotColdNumbers(historicalData, type, options);
  }
}

module.exports = StatisticalPredictor;