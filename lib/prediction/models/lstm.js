// LSTM Model for lottery prediction
// Uses TensorFlow.js for time series prediction

const tf = require('@tensorflow/tfjs-node');
const path = require('path');
const fs = require('fs').promises;

class LSTMPredictor {
  constructor(options = {}) {
    this.options = {
      sequenceLength: options.sequenceLength || 30,
      hiddenUnits: options.hiddenUnits || 50,
      epochs: options.epochs || 100,
      batchSize: options.batchSize || 32,
      learningRate: options.learningRate || 0.001,
      validationSplit: options.validationSplit || 0.2,
      patience: options.patience || 10,
      ...options
    };
    this.model = null;
    this.isTraining = false;
    this.scaler = null;
    this.trainingHistory = [];
    this.modelVersion = '1.0.0';
  }

  /**
   * Initialize TensorFlow.js model architecture
   */
  async initialize() {
    try {
      this.model = tf.sequential({
        layers: [
          // Input layer
          tf.layers.lstm({
            units: this.options.hiddenUnits,
            returnSequences: true,
            inputShape: [this.options.sequenceLength, 1]
          }),

          // Dropout for regularization
          tf.layers.dropout({ rate: 0.2 }),

          // Second LSTM layer
          tf.layers.lstm({
            units: this.options.hiddenUnits / 2,
            returnSequences: false
          }),

          // Dropout for regularization
          tf.layers.dropout({ rate: 0.2 }),

          // Dense output layer for number prediction (0-99 for lottery numbers)
          tf.layers.dense({ units: 100, activation: 'softmax' })
        ]
      });

      // Compile model
      this.model.compile({
        optimizer: tf.train.adam(this.options.learningRate),
        loss: 'categoricalCrossentropy',
        metrics: ['accuracy']
      });

      console.log('LSTM Model initialized successfully');
      return true;
    } catch (error) {
      console.error('Error initializing LSTM model:', error);
      throw new Error(`Failed to initialize LSTM model: ${error.message}`);
    }
  }

  /**
   * Preprocess lottery data for time series training
   * @param {Array} lotteryData - Array of lottery results
   * @returns {Object} Processed training data
   */
  preprocessData(lotteryData) {
    if (!Array.isArray(lotteryData) || lotteryData.length === 0) {
      throw new Error('Invalid lottery data provided');
    }

    // Extract numbers from lottery results
    const numbers = [];
    lotteryData.forEach(result => {
      if (result.numbers && result.numbers.lo) {
        result.numbers.lo.forEach(num => {
          const numValue = parseInt(num, 10);
          if (!isNaN(numValue) && numValue >= 0 && numValue <= 99) {
            numbers.push(numValue);
          }
        });
      }
      if (result.numbers && result.numbers.de) {
        result.numbers.de.forEach(num => {
          const numValue = parseInt(num, 10);
          if (!isNaN(numValue) && numValue >= 0 && numValue <= 99) {
            numbers.push(numValue);
          }
        });
      }
    });

    if (numbers.length < this.options.sequenceLength + 1) {
      throw new Error(`Insufficient data: need at least ${this.options.sequenceLength + 1} numbers`);
    }

    // Create sequences for training
    const sequences = [];
    const targets = [];

    for (let i = 0; i < numbers.length - this.options.sequenceLength; i++) {
      const sequence = numbers.slice(i, i + this.options.sequenceLength);
      const target = numbers[i + this.options.sequenceLength];

      sequences.push(sequence);
      targets.push(target);
    }

    // Normalize sequences (0-1 range)
    const normalizedSequences = sequences.map(seq =>
      seq.map(num => num / 99.0)
    );

    // Convert targets to one-hot encoding
    const oneHotTargets = targets.map(target => {
      const oneHot = new Array(100).fill(0);
      oneHot[target] = 1;
      return oneHot;
    });

    return {
      sequences: normalizedSequences,
      targets: oneHotTargets,
      originalNumbers: numbers,
      sequenceCount: sequences.length
    };
  }

  /**
   * Train LSTM model with historical lottery data
   * @param {Array} trainingData - Preprocessed training data
   * @returns {Object} Training results
   */
  async train(trainingData) {
    if (!this.model) {
      await this.initialize();
    }

    this.isTraining = true;

    try {
      console.log('Starting LSTM training with', trainingData.length, 'lottery results');

      // Preprocess data
      const processedData = this.preprocessData(trainingData);
      console.log(`Created ${processedData.sequenceCount} training sequences`);

      // Convert to tensors
      const xTrain = tf.tensor3d(processedData.sequences);
      const yTrain = tf.tensor2d(processedData.targets);

      // Early stopping callback
      const earlyStopping = tf.callbacks.earlyStopping({
        monitor: 'val_loss',
        patience: this.options.patience,
        restoreBestWeights: true
      });

      // Train the model
      const history = await this.model.fit(xTrain, yTrain, {
        epochs: this.options.epochs,
        batchSize: this.options.batchSize,
        validationSplit: this.options.validationSplit,
        callbacks: [earlyStopping],
        verbose: 1
      });

      // Store training history
      this.trainingHistory.push({
        timestamp: new Date(),
        epochs: history.epoch.length,
        finalLoss: history.history.loss[history.history.loss.length - 1],
        finalAccuracy: history.history.acc[history.history.acc.length - 1],
        finalValLoss: history.history.val_loss[history.history.val_loss.length - 1],
        finalValAccuracy: history.history.val_acc[history.history.val_acc.length - 1]
      });

      // Clean up tensors
      xTrain.dispose();
      yTrain.dispose();

      console.log('LSTM training completed successfully');

      return {
        success: true,
        epochs: history.epoch.length,
        finalLoss: history.history.loss[history.history.loss.length - 1],
        finalAccuracy: history.history.acc[history.history.acc.length - 1],
        trainingTime: Date.now()
      };

    } catch (error) {
      console.error('Error during LSTM training:', error);
      throw new Error(`Training failed: ${error.message}`);
    } finally {
      this.isTraining = false;
    }
  }

  /**
   * Make predictions using trained LSTM model
   * @param {Array} inputSequence - Sequence of recent lottery numbers
   * @returns {Object} Prediction results
   */
  async predict(inputSequence) {
    if (!this.model) {
      throw new Error('Model not trained yet');
    }

    if (!Array.isArray(inputSequence) || inputSequence.length !== this.options.sequenceLength) {
      throw new Error(`Input sequence must be exactly ${this.options.sequenceLength} numbers`);
    }

    try {
      // Normalize input sequence
      const normalizedSequence = inputSequence.map(num => {
        const numValue = parseInt(num, 10);
        if (isNaN(numValue) || numValue < 0 || numValue > 99) {
          throw new Error(`Invalid number in sequence: ${num}`);
        }
        return numValue / 99.0;
      });

      // Convert to tensor
      const inputTensor = tf.tensor3d([normalizedSequence], [1, this.options.sequenceLength, 1]);

      // Make prediction
      const prediction = this.model.predict(inputTensor);
      const predictionData = await prediction.data();

      // Clean up tensors
      inputTensor.dispose();
      prediction.dispose();

      // Get top predictions with confidence scores
      const predictions = [];
      const sortedIndices = Array.from(predictionData)
        .map((prob, index) => ({ index, probability: prob }))
        .sort((a, b) => b.probability - a.probability)
        .slice(0, 10); // Top 10 predictions

      sortedIndices.forEach(({ index, probability }) => {
        predictions.push({
          number: index.toString().padStart(2, '0'),
          confidence: Math.round(probability * 100),
          probability: probability
        });
      });

      return {
        predictions,
        modelVersion: this.modelVersion,
        timestamp: new Date(),
        inputSequence: inputSequence
      };

    } catch (error) {
      console.error('Error during prediction:', error);
      throw new Error(`Prediction failed: ${error.message}`);
    }
  }

  /**
   * Save trained model to disk
   * @param {string} modelPath - Path to save the model
   */
  async saveModel(modelPath) {
    if (!this.model) {
      throw new Error('No model to save');
    }

    try {
      const fullPath = path.resolve(modelPath);
      await this.model.save(`file://${fullPath}`);

      // Save additional metadata
      const metadata = {
        version: this.modelVersion,
        options: this.options,
        trainingHistory: this.trainingHistory,
        savedAt: new Date()
      };

      await fs.writeFile(
        path.join(fullPath, 'metadata.json'),
        JSON.stringify(metadata, null, 2)
      );

      console.log(`Model saved to ${fullPath}`);
      return true;
    } catch (error) {
      console.error('Error saving model:', error);
      throw new Error(`Failed to save model: ${error.message}`);
    }
  }

  /**
   * Load pre-trained model from disk
   * @param {string} modelPath - Path to load the model from
   */
  async loadModel(modelPath) {
    try {
      const fullPath = path.resolve(modelPath);
      this.model = await tf.loadLayersModel(`file://${fullPath}/model.json`);

      // Load metadata if available
      try {
        const metadataPath = path.join(fullPath, 'metadata.json');
        const metadataContent = await fs.readFile(metadataPath, 'utf8');
        const metadata = JSON.parse(metadataContent);

        this.modelVersion = metadata.version || this.modelVersion;
        this.trainingHistory = metadata.trainingHistory || [];

        console.log(`Model loaded from ${fullPath} (version: ${this.modelVersion})`);
      } catch (metaError) {
        console.warn('Could not load model metadata:', metaError.message);
      }

      return true;
    } catch (error) {
      console.error('Error loading model:', error);
      throw new Error(`Failed to load model: ${error.message}`);
    }
  }

  /**
   * Validate model input/output
   * @param {Array} testData - Test data for validation
   * @returns {Object} Validation results
   */
  async validateModel(testData) {
    if (!this.model) {
      throw new Error('Model not available for validation');
    }

    try {
      const processedData = this.preprocessData(testData);
      const sampleSize = Math.min(100, processedData.sequenceCount);

      let correctPredictions = 0;
      let totalPredictions = 0;

      for (let i = 0; i < sampleSize; i++) {
        const sequence = processedData.sequences[i];
        const actualTarget = processedData.targets[i].indexOf(1);

        const prediction = await this.predict(sequence.map(n => Math.round(n * 99)));
        const predictedNumber = parseInt(prediction.predictions[0].number, 10);

        if (predictedNumber === actualTarget) {
          correctPredictions++;
        }
        totalPredictions++;
      }

      const accuracy = (correctPredictions / totalPredictions) * 100;

      return {
        accuracy: accuracy,
        correctPredictions,
        totalPredictions,
        sampleSize,
        timestamp: new Date()
      };

    } catch (error) {
      console.error('Error during model validation:', error);
      throw new Error(`Validation failed: ${error.message}`);
    }
  }

  /**
   * Get comprehensive model information
   * @returns {Object} Model information
   */
  getModelInfo() {
    return {
      type: 'LSTM',
      version: this.modelVersion,
      options: this.options,
      trained: !!this.model,
      training: this.isTraining,
      trainingHistory: this.trainingHistory,
      architecture: this.model ? {
        layers: this.model.layers.length,
        parameters: this.model.countParams()
      } : null
    };
  }

  /**
   * Dispose of the model and free memory
   */
  dispose() {
    if (this.model) {
      this.model.dispose();
      this.model = null;
    }
  }
}

module.exports = LSTMPredictor;