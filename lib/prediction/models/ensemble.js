// Ensemble model combining multiple prediction methods
// Combines LSTM and statistical predictions for better accuracy

class EnsemblePredictor {
  constructor(models = []) {
    this.models = models;
    this.weights = new Map();
    this.accuracyHistory = new Map();
  }

  addModel(name, model, weight = 1.0) {
    this.models.push({ name, model });
    this.weights.set(name, weight);
    this.accuracyHistory.set(name, []);
  }

  async predict(inputData, type = 'lo') {
    // Combine predictions from all models
    const modelPredictions = [];

    for (const { name, model } of this.models) {
      try {
        const prediction = await model.predict(inputData, type);
        modelPredictions.push({
          name,
          prediction,
          weight: this.weights.get(name) || 1.0
        });
      } catch (error) {
        console.error(`Model ${name} prediction failed:`, error.message);
      }
    }

    return this.combinePredictions(modelPredictions);
  }

  combinePredictions(modelPredictions) {
    // Weighted combination of model predictions
    const combinedScores = new Map();
    let totalWeight = 0;

    modelPredictions.forEach(({ prediction, weight }) => {
      totalWeight += weight;

      prediction.predictions.forEach(pred => {
        const currentScore = combinedScores.get(pred.number) || 0;
        const weightedScore = (pred.confidence / 100) * weight;
        combinedScores.set(pred.number, currentScore + weightedScore);
      });
    });

    // Normalize and sort predictions
    const finalPredictions = Array.from(combinedScores.entries())
      .map(([number, score]) => ({
        number,
        confidence: Math.round((score / totalWeight) * 100),
        reasoning: `Kết hợp từ ${modelPredictions.length} mô hình`
      }))
      .sort((a, b) => b.confidence - a.confidence)
      .slice(0, 10);

    return {
      predictions: finalPredictions,
      confidence: this.calculateEnsembleConfidence(finalPredictions),
      method: 'ensemble',
      modelCount: modelPredictions.length
    };
  }

  calculateEnsembleConfidence(predictions) {
    if (predictions.length === 0) return 0;

    const avgConfidence = predictions.reduce((sum, pred) => sum + pred.confidence, 0) / predictions.length;
    const variance = predictions.reduce((sum, pred) => sum + Math.pow(pred.confidence - avgConfidence, 2), 0) / predictions.length;

    // Higher confidence when predictions are consistent (low variance)
    const consistencyBonus = Math.max(0, 20 - Math.sqrt(variance));

    return Math.min(100, Math.round(avgConfidence + consistencyBonus));
  }

  updateModelAccuracy(modelName, actualResults, predictions) {
    // Update accuracy tracking for dynamic weight adjustment
    const accuracy = this.calculateAccuracy(actualResults, predictions);
    const history = this.accuracyHistory.get(modelName) || [];

    history.push(accuracy);
    if (history.length > 50) {
      history.shift(); // Keep only recent accuracy data
    }

    this.accuracyHistory.set(modelName, history);
    this.adjustWeights();
  }

  adjustWeights() {
    // Dynamically adjust model weights based on recent accuracy
    this.accuracyHistory.forEach((history, modelName) => {
      if (history.length >= 5) {
        const recentAccuracy = history.slice(-10).reduce((sum, acc) => sum + acc, 0) / Math.min(10, history.length);
        this.weights.set(modelName, Math.max(0.1, recentAccuracy / 100));
      }
    });
  }

  calculateAccuracy(actualResults, predictions) {
    // Calculate prediction accuracy
    let correct = 0;
    const predictedNumbers = predictions.map(p => p.number);

    actualResults.forEach(result => {
      if (predictedNumbers.includes(result)) {
        correct++;
      }
    });

    return (correct / predictedNumbers.length) * 100;
  }

  getModelStats() {
    return {
      modelCount: this.models.length,
      weights: Object.fromEntries(this.weights),
      accuracyHistory: Object.fromEntries(this.accuracyHistory)
    };
  }
}

module.exports = EnsemblePredictor;