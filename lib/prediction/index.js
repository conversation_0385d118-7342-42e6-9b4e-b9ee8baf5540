// Prediction Engine - Main ML and statistical prediction system
// Coordinates between different prediction models and analyzers

const StatisticalPredictor = require('./models/statistical');
const LSTMPredictor = require('./models/lstm');
const EnsemblePredictor = require('./models/ensemble');
const StatisticalAnalyzer = require('./analyzers/statisticalAnalyzer');
const TrendAnalyzer = require('./analyzers/trendAnalyzer');
const NumberAnalyzer = require('./analyzers/numberAnalyzer');

class PredictionEngine {
  constructor(dataManager, options = {}) {
    this.dataManager = dataManager;
    this.options = {
      enableLSTM: options.enableLSTM || false,
      enableEnsemble: options.enableEnsemble || true,
      cacheEnabled: options.cacheEnabled !== false,
      cacheTTL: options.cacheTTL || 3600, // 1 hour default
      confidenceThreshold: options.confidenceThreshold || 15,
      maxPredictions: options.maxPredictions || 10,
      ...options
    };
    this.models = new Map();
    this.analyzers = new Map();
    this.cache = new Map();
    this.accuracyMetrics = new Map();
    this.initialized = false;
  }

  async initialize() {
    if (this.initialized) return;

    try {
      // Initialize prediction models
      this.models.set('statistical', new StatisticalPredictor(this.options));

      if (this.options.enableLSTM) {
        const lstmModel = new LSTMPredictor(this.options);
        await lstmModel.initialize();
        this.models.set('lstm', lstmModel);
      }

      // Initialize ensemble if enabled and multiple models available
      if (this.options.enableEnsemble && this.models.size > 1) {
        const ensemble = new EnsemblePredictor();
        this.models.forEach((model, name) => {
          ensemble.addModel(name, model, 1.0);
        });
        this.models.set('ensemble', ensemble);
      }

      // Initialize analyzers
      this.analyzers.set('statistical', new StatisticalAnalyzer(this.options));
      this.analyzers.set('trend', new TrendAnalyzer(this.options));
      this.analyzers.set('number', new NumberAnalyzer(this.options));

      // Initialize accuracy tracking
      this.models.forEach((model, name) => {
        this.accuracyMetrics.set(name, {
          totalPredictions: 0,
          correctPredictions: 0,
          accuracy: 0,
          lastUpdated: new Date()
        });
      });

      this.initialized = true;
      console.log(`Prediction Engine initialized with ${this.models.size} models and ${this.analyzers.size} analyzers`);
    } catch (error) {
      console.error('Failed to initialize Prediction Engine:', error);
      throw new Error(`Prediction Engine initialization failed: ${error.message}`);
    }
  }

  async predictLo(options = {}) {
    return this._predict('lo', options);
  }

  async predictDe(options = {}) {
    return this._predict('de', options);
  }

  async _predict(type, options = {}) {
    if (!this.initialized) {
      await this.initialize();
    }

    if (!this.dataManager) {
      return {
        predictions: [],
        confidence: 0,
        method: 'error',
        error: 'DataManager not available for predictions',
        type,
        timestamp: new Date()
      };
    }

    const cacheKey = this._getCacheKey(type, options);

    // Check cache first
    if (this.options.cacheEnabled && this.cache.has(cacheKey)) {
      const cached = this.cache.get(cacheKey);
      if (Date.now() - cached.timestamp < this.options.cacheTTL * 1000) {
        return { ...cached.result, cached: true };
      }
      this.cache.delete(cacheKey);
    }

    try {
      const historicalData = await this.dataManager.getLotteryHistory();

      // Get predictions from all available models
      const modelPredictions = await this._getModelPredictions(historicalData, type, options);

      // Combine and rank predictions
      const combinedResult = this._combineAndRankPredictions(modelPredictions, type);

      // Apply confidence scoring
      const finalResult = this._applyConfidenceScoring(combinedResult, modelPredictions);

      // Cache the result
      if (this.options.cacheEnabled) {
        this.cache.set(cacheKey, {
          result: finalResult,
          timestamp: Date.now()
        });
      }

      return finalResult;

    } catch (error) {
      console.error(`Prediction failed for type ${type}:`, error);
      return {
        predictions: [],
        confidence: 0,
        method: 'error',
        error: error.message,
        timestamp: new Date()
      };
    }
  }

  async _getModelPredictions(historicalData, type, options) {
    const predictions = [];
    const preferredModel = options.model || (this.models.has('ensemble') ? 'ensemble' : 'statistical');

    if (options.model && this.models.has(options.model)) {
      // Use specific model if requested
      const model = this.models.get(options.model);
      try {
        const result = await model.predict(historicalData, type);
        predictions.push({
          model: options.model,
          result,
          weight: 1.0
        });
      } catch (error) {
        console.error(`Model ${options.model} prediction failed:`, error);
      }
    } else {
      // Use preferred model or all models for ensemble
      if (this.models.has(preferredModel)) {
        const model = this.models.get(preferredModel);
        try {
          const result = await model.predict(historicalData, type);
          predictions.push({
            model: preferredModel,
            result,
            weight: 1.0
          });
        } catch (error) {
          console.error(`Primary model ${preferredModel} failed, falling back to statistical:`, error);
          // Fallback to statistical model
          if (this.models.has('statistical')) {
            const fallbackModel = this.models.get('statistical');
            const fallbackResult = await fallbackModel.predict(historicalData, type);
            predictions.push({
              model: 'statistical',
              result: fallbackResult,
              weight: 0.8
            });
          }
        }
      }
    }

    return predictions;
  }

  _combineAndRankPredictions(modelPredictions, type) {
    if (modelPredictions.length === 0) {
      return {
        predictions: [],
        confidence: 0,
        method: 'no_predictions',
        type
      };
    }

    if (modelPredictions.length === 1) {
      const result = modelPredictions[0].result;
      // Ensure we respect maxPredictions even for single model
      const predictions = result.predictions && Array.isArray(result.predictions)
        ? result.predictions.slice(0, this.options.maxPredictions)
        : [];

      return {
        ...result,
        predictions,
        method: modelPredictions[0].model,
        type
      };
    }

    // Combine multiple model predictions
    const numberScores = new Map();
    let totalWeight = 0;

    modelPredictions.forEach(({ result, weight }) => {
      totalWeight += weight;

      if (result.predictions && Array.isArray(result.predictions)) {
        result.predictions.forEach((pred, index) => {
          const number = pred.number;
          const score = (pred.confidence || 0) / 100;
          const positionWeight = 1 - (index * 0.05); // Slight preference for higher-ranked predictions
          const weightedScore = score * weight * positionWeight;

          const currentScore = numberScores.get(number) || { score: 0, count: 0, reasons: [] };
          numberScores.set(number, {
            score: currentScore.score + weightedScore,
            count: currentScore.count + 1,
            reasons: [...currentScore.reasons, pred.reasoning || `${result.method || 'unknown'} prediction`]
          });
        });
      }
    });

    // Rank and format final predictions
    const rankedPredictions = Array.from(numberScores.entries())
      .map(([number, data]) => ({
        number,
        confidence: Math.round((data.score / totalWeight) * 100),
        reasoning: data.reasons.join('; '),
        modelCount: data.count,
        normalizedScore: data.score / totalWeight
      }))
      .sort((a, b) => b.normalizedScore - a.normalizedScore)
      .slice(0, this.options.maxPredictions);

    return {
      predictions: rankedPredictions,
      method: 'combined',
      modelCount: modelPredictions.length,
      type
    };
  }

  _applyConfidenceScoring(result, modelPredictions) {
    if (!result.predictions || result.predictions.length === 0) {
      return { ...result, confidence: 0 };
    }

    // Calculate overall confidence based on multiple factors
    const predictions = result.predictions;
    const avgConfidence = predictions.reduce((sum, pred) => sum + pred.confidence, 0) / predictions.length;

    // Consistency bonus: higher confidence when top predictions are close in score
    const topScores = predictions.slice(0, 3).map(p => p.confidence);
    const scoreVariance = this._calculateVariance(topScores);
    const consistencyBonus = Math.max(0, 10 - scoreVariance / 10);

    // Model agreement bonus: higher confidence when multiple models agree
    const modelAgreementBonus = result.modelCount > 1 ? Math.min(15, result.modelCount * 5) : 0;

    // Historical accuracy bonus
    const accuracyBonus = this._getAccuracyBonus(result.method);

    const finalConfidence = Math.min(100, Math.round(
      avgConfidence + consistencyBonus + modelAgreementBonus + accuracyBonus
    ));

    return {
      ...result,
      confidence: finalConfidence,
      confidenceFactors: {
        baseConfidence: Math.round(avgConfidence),
        consistencyBonus: Math.round(consistencyBonus),
        modelAgreementBonus: Math.round(modelAgreementBonus),
        accuracyBonus: Math.round(accuracyBonus)
      },
      timestamp: new Date()
    };
  }

  _calculateVariance(values) {
    if (values.length === 0) return 0;
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    return values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
  }

  _getAccuracyBonus(method) {
    const metrics = this.accuracyMetrics.get(method);
    if (!metrics || metrics.totalPredictions < 10) return 0;

    // Bonus based on historical accuracy above threshold
    const accuracyAboveThreshold = Math.max(0, metrics.accuracy - this.options.confidenceThreshold);
    return Math.min(10, accuracyAboveThreshold / 2);
  }

  _getCacheKey(type, options) {
    const keyParts = [
      type,
      options.model || 'default',
      options.count || this.options.maxPredictions,
      new Date().toISOString().split('T')[0] // Date-based cache key
    ];
    return keyParts.join(':');
  }

  async analyzeNumber(number, type = 'lo') {
    if (!this.initialized) {
      await this.initialize();
    }

    if (!this.dataManager) {
      throw new Error('DataManager not available for analysis');
    }

    try {
      const historicalData = await this.dataManager.getLotteryHistory();
      const numberAnalyzer = this.analyzers.get('number');

      if (numberAnalyzer) {
        // Use the comprehensive NumberAnalyzer
        return numberAnalyzer.analyzeNumber(number, historicalData, type);
      }

      // Fallback to statistical analyzer
      const statisticalAnalyzer = this.analyzers.get('statistical');
      if (statisticalAnalyzer) {
        return statisticalAnalyzer.analyzeNumber(number, historicalData, type);
      }

      return {
        number,
        type,
        frequency: 0,
        lastSeen: null,
        trends: [],
        analysis: 'No analyzer available',
        error: 'Number analysis not available'
      };

    } catch (error) {
      console.error(`Number analysis failed for ${number}:`, error);
      return {
        number,
        type,
        error: error.message,
        analysis: 'Analysis failed'
      };
    }
  }

  async trackNumberPerformance(number, type = 'lo') {
    if (!this.initialized) {
      await this.initialize();
    }

    if (!this.dataManager) {
      throw new Error('DataManager not available for performance tracking');
    }

    try {
      const historicalData = await this.dataManager.getLotteryHistory();
      const numberAnalyzer = this.analyzers.get('number');

      if (numberAnalyzer) {
        return numberAnalyzer.trackHistoricalPerformance(number, historicalData, type);
      }

      throw new Error('Number analyzer not available');

    } catch (error) {
      console.error(`Performance tracking failed for ${number}:`, error);
      throw error;
    }
  }

  async getTrends(type = 'lo', period = 'weekly') {
    // Get hot/cold number trends
    if (!this.dataManager) {
      throw new Error('DataManager not available for trend analysis');
    }

    const historicalData = await this.dataManager.getLotteryHistory();
    const trendAnalyzer = this.analyzers.get('trend');

    if (trendAnalyzer) {
      const analysis = trendAnalyzer.analyzeTrends(historicalData, type);
      return {
        hot: analysis.periods.short?.trends?.find(t => t.type === 'most_frequent')?.numbers || [],
        cold: analysis.periods.short?.trends?.find(t => t.type === 'least_frequent')?.numbers || [],
        period,
        type,
        fullAnalysis: analysis
      };
    }

    return {
      hot: [],
      cold: [],
      period,
      type
    };
  }

  async updateModel(newData) {
    if (!this.initialized) {
      await this.initialize();
    }

    console.log('Model update triggered with new data');

    const updatedModels = [];
    const errors = [];

    try {
      // Update LSTM model if available
      if (this.models.has('lstm')) {
        try {
          const lstmModel = this.models.get('lstm');
          await lstmModel.train(newData);
          updatedModels.push('lstm');
        } catch (error) {
          errors.push(`LSTM update failed: ${error.message}`);
        }
      }

      // Update other models
      for (const [modelName, model] of this.models.entries()) {
        if (modelName !== 'lstm' && typeof model.train === 'function') {
          try {
            await model.train(newData);
            updatedModels.push(modelName);
          } catch (error) {
            errors.push(`${modelName} update failed: ${error.message}`);
          }
        } else if (modelName !== 'lstm') {
          updatedModels.push(modelName); // Models without train method are considered updated
        }
      }

      // Update ensemble model weights based on new accuracy data
      if (this.models.has('ensemble')) {
        const ensemble = this.models.get('ensemble');
        // This would require actual results to compare against predictions
        // For now, we'll just log the update
        console.log('Ensemble model weights updated');
      }

      // Clear cache to ensure fresh predictions
      this.cache.clear();

      if (errors.length > 0 && updatedModels.length === 0) {
        throw new Error(`All model updates failed: ${errors.join('; ')}`);
      }

      return {
        success: true,
        modelsUpdated: updatedModels,
        errors: errors.length > 0 ? errors : undefined,
        timestamp: new Date()
      };

    } catch (error) {
      console.error('Model update failed:', error);
      throw new Error(`Model update failed: ${error.message}`);
    }
  }

  async getAccuracy() {
    if (!this.initialized) {
      await this.initialize();
    }

    const accuracyData = {};
    let totalPredictions = 0;
    let totalCorrect = 0;

    this.accuracyMetrics.forEach((metrics, modelName) => {
      accuracyData[modelName] = {
        accuracy: metrics.accuracy,
        totalPredictions: metrics.totalPredictions,
        correctPredictions: metrics.correctPredictions,
        lastUpdated: metrics.lastUpdated
      };

      totalPredictions += metrics.totalPredictions;
      totalCorrect += metrics.correctPredictions;
    });

    const overallAccuracy = totalPredictions > 0 ? (totalCorrect / totalPredictions) * 100 : 0;

    return {
      overall: Math.round(overallAccuracy * 100) / 100,
      byModel: accuracyData,
      totalPredictions,
      totalCorrect,
      lastUpdated: new Date()
    };
  }

  // New methods for enhanced functionality

  async updateAccuracy(modelName, actualResults, predictions) {
    if (!this.accuracyMetrics.has(modelName)) {
      this.accuracyMetrics.set(modelName, {
        totalPredictions: 0,
        correctPredictions: 0,
        accuracy: 0,
        lastUpdated: new Date()
      });
    }

    const metrics = this.accuracyMetrics.get(modelName);
    const predictedNumbers = predictions.map(p => p.number);
    let correct = 0;

    actualResults.forEach(result => {
      if (predictedNumbers.includes(result)) {
        correct++;
      }
    });

    metrics.totalPredictions += predictions.length;
    metrics.correctPredictions += correct;
    metrics.accuracy = (metrics.correctPredictions / metrics.totalPredictions) * 100;
    metrics.lastUpdated = new Date();

    // Update ensemble model if available
    if (this.models.has('ensemble')) {
      const ensemble = this.models.get('ensemble');
      ensemble.updateModelAccuracy(modelName, actualResults, predictions);
    }

    return {
      modelName,
      accuracy: metrics.accuracy,
      correctInBatch: correct,
      totalInBatch: predictions.length
    };
  }

  clearCache() {
    this.cache.clear();
    console.log('Prediction cache cleared');
  }

  getCacheStats() {
    return {
      size: this.cache.size,
      enabled: this.options.cacheEnabled,
      ttl: this.options.cacheTTL
    };
  }

  getModelInfo() {
    const modelInfo = {};

    this.models.forEach((model, name) => {
      if (typeof model.getModelInfo === 'function') {
        modelInfo[name] = model.getModelInfo();
      } else {
        modelInfo[name] = {
          type: name,
          available: true
        };
      }
    });

    return {
      initialized: this.initialized,
      models: modelInfo,
      analyzers: Array.from(this.analyzers.keys()),
      options: this.options,
      cache: this.getCacheStats(),
      accuracy: this.accuracyMetrics.size > 0 ? Object.fromEntries(this.accuracyMetrics) : {}
    };
  }
}

module.exports = PredictionEngine;