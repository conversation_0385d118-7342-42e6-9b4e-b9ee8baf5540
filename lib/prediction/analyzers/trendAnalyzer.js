// Trend Analysis System for Lottery Prediction
// Implements trend detection, anomaly detection, and historical comparison

class TrendAnalyzer {
  constructor(options = {}) {
    this.options = {
      shortTermPeriod: options.shortTermPeriod || 30,
      mediumTermPeriod: options.mediumTermPeriod || 90,
      longTermPeriod: options.longTermPeriod || 180,
      anomalyThreshold: options.anomalyThreshold || 2.5, // Standard deviations
      trendMinimumPeriod: options.trendMinimumPeriod || 10,
      significanceLevel: options.significanceLevel || 0.05,
      movingAverageWindow: options.movingAverageWindow || 7,
      ...options
    };
  }

  /**
   * Analyze trends for lottery numbers (lo and de)
   * @param {Array} historicalData - Array of lottery results
   * @param {string} type - 'lo' or 'de'
   * @param {Object} options - Analysis options
   * @returns {Object} Comprehensive trend analysis
   */
  analyzeTrends(historicalData, type = 'lo', options = {}) {
    const {
      includeShortTerm = true,
      includeMediumTerm = true,
      includeLongTerm = true,
      detectAnomalies = true,
      calculateMomentum = true
    } = options;

    const analysis = {
      type,
      analysisDate: new Date(),
      dataPoints: historicalData.length,
      periods: {}
    };

    // Analyze different time periods
    if (includeShortTerm) {
      analysis.periods.short = this._analyzePeriodTrends(
        historicalData.slice(-this.options.shortTermPeriod),
        type,
        'short'
      );
    }

    if (includeMediumTerm) {
      analysis.periods.medium = this._analyzePeriodTrends(
        historicalData.slice(-this.options.mediumTermPeriod),
        type,
        'medium'
      );
    }

    if (includeLongTerm) {
      analysis.periods.long = this._analyzePeriodTrends(
        historicalData.slice(-this.options.longTermPeriod),
        type,
        'long'
      );
    }

    // Overall trend analysis
    analysis.overallTrends = this._calculateOverallTrends(historicalData, type);

    // Momentum analysis
    if (calculateMomentum) {
      analysis.momentum = this._calculateTrendMomentum(historicalData, type);
    }

    // Anomaly detection
    if (detectAnomalies) {
      analysis.anomalies = this.detectAnomalies(historicalData, type);
    } else {
      analysis.anomalies = { detectionDate: new Date(), type, anomalies: [] };
    }

    // Historical comparison
    analysis.historicalComparison = this._performHistoricalComparison(historicalData, type);

    // Trend scoring
    analysis.trendScores = this._calculateTrendScores(analysis);

    return analysis;
  }

  /**
   * Detect unusual patterns and anomalies in lottery data
   * @param {Array} historicalData - Array of lottery results
   * @param {string} type - 'lo' or 'de'
   * @param {Object} options - Detection options
   * @returns {Object} Detected anomalies
   */
  detectAnomalies(historicalData, type = 'lo', options = {}) {
    const {
      includeFrequencyAnomalies = true,
      includeSumAnomalies = true,
      includePatternAnomalies = true,
      includeSequenceAnomalies = true
    } = options;

    const anomalies = {
      detectionDate: new Date(),
      type,
      anomalies: []
    };

    if (includeFrequencyAnomalies) {
      anomalies.anomalies.push(...this._detectFrequencyAnomalies(historicalData, type));
    }

    if (includeSumAnomalies) {
      anomalies.anomalies.push(...this._detectSumAnomalies(historicalData, type));
    }

    if (includePatternAnomalies) {
      anomalies.anomalies.push(...this._detectPatternAnomalies(historicalData, type));
    }

    if (includeSequenceAnomalies) {
      anomalies.anomalies.push(...this._detectSequenceAnomalies(historicalData, type));
    }

    // Sort by severity
    anomalies.anomalies.sort((a, b) => b.severity - a.severity);

    return anomalies;
  }

  /**
   * Perform historical comparison analysis
   * @param {Array} historicalData - Array of lottery results
   * @param {string} type - 'lo' or 'de'
   * @param {Object} options - Comparison options
   * @returns {Object} Historical comparison results
   */
  performHistoricalComparison(historicalData, type = 'lo', options = {}) {
    const {
      compareYears = true,
      compareSeasons = true,
      compareMonths = true,
      compareDaysOfWeek = true
    } = options;

    const comparison = {
      type,
      comparisonDate: new Date()
    };

    if (compareYears) {
      comparison.yearly = this._compareByYear(historicalData, type);
    }

    if (compareSeasons) {
      comparison.seasonal = this._compareBySeason(historicalData, type);
    }

    if (compareMonths) {
      comparison.monthly = this._compareByMonth(historicalData, type);
    }

    if (compareDaysOfWeek) {
      comparison.dayOfWeek = this._compareByDayOfWeek(historicalData, type);
    }

    return comparison;
  }

  /**
   * Calculate trend scores for numbers
   * @param {Array} historicalData - Array of lottery results
   * @param {string} type - 'lo' or 'de'
   * @returns {Array} Numbers with trend scores
   */
  calculateTrendScores(historicalData, type = 'lo') {
    const numbers = this._extractUniqueNumbers(historicalData, type);

    return numbers.map(number => {
      const score = this._calculateNumberTrendScore(number, historicalData, type);
      return {
        number,
        trendScore: score.overall,
        components: score.components,
        recommendation: this._getTrendRecommendation(score.overall),
        confidence: score.confidence
      };
    }).sort((a, b) => b.trendScore - a.trendScore);
  }

  // Private helper methods

  _analyzePeriodTrends(periodData, type, periodName) {
    if (periodData.length === 0) {
      return { period: periodName, days: 0, trends: [], summary: 'No data' };
    }

    const frequencies = this._calculateFrequencies(periodData, type);
    const movingAverages = this._calculateMovingAverages(periodData, type);
    const volatility = this._calculateVolatility(periodData, type);

    return {
      period: periodName,
      days: periodData.length,
      frequencies,
      movingAverages,
      volatility,
      trends: this._identifyPeriodTrends(periodData, type),
      summary: this._generatePeriodSummary(periodData, type, periodName)
    };
  }

  _calculateOverallTrends(historicalData, type) {
    const recentData = historicalData.slice(-this.options.shortTermPeriod);
    const olderData = historicalData.slice(-this.options.longTermPeriod, -this.options.shortTermPeriod);

    const recentFreq = this._calculateFrequencies(recentData, type);
    const olderFreq = this._calculateFrequencies(olderData, type);

    const trends = [];
    const allNumbers = new Set([...recentFreq.keys(), ...olderFreq.keys()]);

    allNumbers.forEach(number => {
      const recentCount = recentFreq.get(number) || 0;
      const olderCount = olderFreq.get(number) || 0;

      const recentRate = recentData.length > 0 ? recentCount / recentData.length : 0;
      const olderRate = olderData.length > 0 ? olderCount / olderData.length : 0;

      const change = recentRate - olderRate;
      const percentChange = olderRate > 0 ? (change / olderRate) * 100 : 0;

      trends.push({
        number,
        recentFrequency: recentCount,
        olderFrequency: olderCount,
        change,
        percentChange,
        trend: this._classifyTrend(change, percentChange),
        significance: this._calculateTrendSignificance(recentCount, olderCount, recentData.length, olderData.length)
      });
    });

    return trends.sort((a, b) => Math.abs(b.percentChange) - Math.abs(a.percentChange));
  }

  _calculateTrendMomentum(historicalData, type) {
    const periods = [
      { name: 'recent', data: historicalData.slice(-7) },
      { name: 'short', data: historicalData.slice(-this.options.shortTermPeriod) },
      { name: 'medium', data: historicalData.slice(-this.options.mediumTermPeriod) }
    ];

    const momentum = {};

    periods.forEach(period => {
      if (period.data.length === 0) return;

      const frequencies = this._calculateFrequencies(period.data, type);
      const acceleration = this._calculateAcceleration(period.data, type);

      momentum[period.name] = {
        averageFrequency: this._calculateAverageFrequency(frequencies),
        acceleration,
        volatility: this._calculateVolatility(period.data, type),
        direction: this._calculateMomentumDirection(period.data, type)
      };
    });

    return momentum;
  }

  _detectFrequencyAnomalies(historicalData, type) {
    const frequencies = this._calculateFrequencies(historicalData, type);
    const values = Array.from(frequencies.values());

    if (values.length === 0) return [];

    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const stdDev = Math.sqrt(values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length);

    const anomalies = [];
    const threshold = this.options.anomalyThreshold;

    // Only detect anomalies if there's sufficient variance
    if (stdDev > 0) {
      frequencies.forEach((freq, number) => {
        const zScore = (freq - mean) / stdDev;

        if (Math.abs(zScore) > threshold) {
          anomalies.push({
            type: 'frequency',
            number,
            value: freq,
            expected: mean,
            zScore,
            severity: Math.abs(zScore),
            description: `Số ${number} xuất hiện ${freq > mean ? 'nhiều' : 'ít'} bất thường (${freq} lần vs trung bình ${mean.toFixed(1)})`
          });
        }
      });
    }

    return anomalies;
  }

  _detectSumAnomalies(historicalData, type) {
    const sums = historicalData.map(result => {
      if (!result.numbers || !result.numbers[type]) return 0;
      return result.numbers[type].reduce((sum, num) => sum + parseInt(num), 0);
    }).filter(sum => sum > 0);

    if (sums.length === 0) return [];

    const mean = sums.reduce((sum, val) => sum + val, 0) / sums.length;
    const stdDev = Math.sqrt(sums.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / sums.length);

    const anomalies = [];
    const threshold = this.options.anomalyThreshold;

    sums.forEach((sum, index) => {
      const zScore = stdDev > 0 ? (sum - mean) / stdDev : 0;

      if (Math.abs(zScore) > threshold) {
        anomalies.push({
          type: 'sum',
          index,
          value: sum,
          expected: mean,
          zScore,
          severity: Math.abs(zScore),
          description: `Tổng số ${sum > mean ? 'cao' : 'thấp'} bất thường: ${sum} (trung bình ${mean.toFixed(1)})`
        });
      }
    });

    return anomalies;
  }

  _detectPatternAnomalies(historicalData, type) {
    // Detect unusual patterns like consecutive numbers, repeated sequences, etc.
    const anomalies = [];

    historicalData.forEach((result, index) => {
      if (!result.numbers || !result.numbers[type]) return;

      const numbers = result.numbers[type].map(n => parseInt(n)).sort((a, b) => a - b);

      // Check for consecutive numbers
      let consecutiveCount = 1;
      for (let i = 1; i < numbers.length; i++) {
        if (numbers[i] === numbers[i - 1] + 1) {
          consecutiveCount++;
        } else {
          if (consecutiveCount >= 3) {
            anomalies.push({
              type: 'pattern',
              subtype: 'consecutive',
              index,
              value: consecutiveCount,
              numbers: numbers.slice(i - consecutiveCount, i),
              severity: consecutiveCount,
              description: `${consecutiveCount} số liên tiếp xuất hiện`
            });
          }
          consecutiveCount = 1;
        }
      }

      // Check for repeated digits
      const digitCounts = {};
      numbers.forEach(num => {
        const digits = num.toString().split('');
        digits.forEach(digit => {
          digitCounts[digit] = (digitCounts[digit] || 0) + 1;
        });
      });

      Object.entries(digitCounts).forEach(([digit, count]) => {
        if (count >= 4) {
          anomalies.push({
            type: 'pattern',
            subtype: 'repeated_digit',
            index,
            digit,
            count,
            severity: count,
            description: `Chữ số ${digit} xuất hiện ${count} lần`
          });
        }
      });
    });

    return anomalies;
  }

  _detectSequenceAnomalies(historicalData, type) {
    // Detect unusual sequences in the data
    const anomalies = [];
    const sequenceLength = 3;

    for (let i = 0; i <= historicalData.length - sequenceLength; i++) {
      const sequence = historicalData.slice(i, i + sequenceLength);
      const sequenceNumbers = sequence.map(result => {
        if (!result.numbers || !result.numbers[type]) return [];
        return result.numbers[type];
      });

      // Check for identical sequences
      const firstSet = new Set(sequenceNumbers[0]);
      let identical = true;

      for (let j = 1; j < sequenceNumbers.length; j++) {
        const currentSet = new Set(sequenceNumbers[j]);
        if (firstSet.size !== currentSet.size ||
            ![...firstSet].every(num => currentSet.has(num))) {
          identical = false;
          break;
        }
      }

      if (identical && sequenceNumbers[0].length > 0) {
        anomalies.push({
          type: 'sequence',
          subtype: 'identical',
          startIndex: i,
          length: sequenceLength,
          numbers: sequenceNumbers[0],
          severity: sequenceLength,
          description: `Chuỗi ${sequenceLength} kết quả giống hệt nhau`
        });
      }
    }

    return anomalies;
  }

  _performHistoricalComparison(historicalData, type) {
    return this.performHistoricalComparison(historicalData, type);
  }

  _compareByYear(historicalData, type) {
    const yearlyData = {};

    historicalData.forEach(result => {
      const year = new Date(result.date).getFullYear();
      if (!yearlyData[year]) yearlyData[year] = [];
      yearlyData[year].push(result);
    });

    const comparison = {};
    Object.entries(yearlyData).forEach(([year, data]) => {
      comparison[year] = {
        totalDraws: data.length,
        frequencies: this._calculateFrequencies(data, type),
        averageSum: this._calculateAverageSum(data, type),
        mostCommon: this._getMostCommonNumbers(data, type, 5),
        leastCommon: this._getLeastCommonNumbers(data, type, 5)
      };
    });

    return comparison;
  }

  _compareBySeason(historicalData, type) {
    const seasonalData = { spring: [], summer: [], autumn: [], winter: [] };

    historicalData.forEach(result => {
      const month = new Date(result.date).getMonth() + 1;
      let season;
      if (month >= 3 && month <= 5) season = 'spring';
      else if (month >= 6 && month <= 8) season = 'summer';
      else if (month >= 9 && month <= 11) season = 'autumn';
      else season = 'winter';

      seasonalData[season].push(result);
    });

    const comparison = {};
    Object.entries(seasonalData).forEach(([season, data]) => {
      comparison[season] = {
        totalDraws: data.length,
        frequencies: this._calculateFrequencies(data, type),
        averageSum: this._calculateAverageSum(data, type),
        mostCommon: this._getMostCommonNumbers(data, type, 3)
      };
    });

    return comparison;
  }

  _compareByMonth(historicalData, type) {
    const monthlyData = {};

    historicalData.forEach(result => {
      const month = new Date(result.date).getMonth() + 1;
      if (!monthlyData[month]) monthlyData[month] = [];
      monthlyData[month].push(result);
    });

    const comparison = {};
    Object.entries(monthlyData).forEach(([month, data]) => {
      comparison[month] = {
        totalDraws: data.length,
        frequencies: this._calculateFrequencies(data, type),
        averageSum: this._calculateAverageSum(data, type)
      };
    });

    return comparison;
  }

  _compareByDayOfWeek(historicalData, type) {
    const dayData = {};
    const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];

    historicalData.forEach(result => {
      const day = new Date(result.date).getDay();
      const dayName = dayNames[day];
      if (!dayData[dayName]) dayData[dayName] = [];
      dayData[dayName].push(result);
    });

    const comparison = {};
    Object.entries(dayData).forEach(([day, data]) => {
      comparison[day] = {
        totalDraws: data.length,
        frequencies: this._calculateFrequencies(data, type),
        averageSum: this._calculateAverageSum(data, type)
      };
    });

    return comparison;
  }

  _calculateTrendScores(analysis) {
    const scores = {};

    // Extract numbers from all periods
    const allNumbers = new Set();
    Object.values(analysis.periods).forEach(period => {
      if (period.frequencies) {
        period.frequencies.forEach((freq, number) => allNumbers.add(number));
      }
    });

    allNumbers.forEach(number => {
      let score = 0;
      let components = {};

      // Short term trend component
      if (analysis.periods.short && analysis.periods.short.frequencies) {
        const shortFreq = analysis.periods.short.frequencies.get(number) || 0;
        components.shortTerm = shortFreq / analysis.periods.short.days;
        score += components.shortTerm * 0.4;
      }

      // Medium term trend component
      if (analysis.periods.medium && analysis.periods.medium.frequencies) {
        const mediumFreq = analysis.periods.medium.frequencies.get(number) || 0;
        components.mediumTerm = mediumFreq / analysis.periods.medium.days;
        score += components.mediumTerm * 0.3;
      }

      // Long term trend component
      if (analysis.periods.long && analysis.periods.long.frequencies) {
        const longFreq = analysis.periods.long.frequencies.get(number) || 0;
        components.longTerm = longFreq / analysis.periods.long.days;
        score += components.longTerm * 0.2;
      }

      // Momentum component
      if (analysis.momentum) {
        components.momentum = this._calculateNumberMomentum(number, analysis.momentum);
        score += components.momentum * 0.1;
      }

      scores[number] = {
        overall: score,
        components,
        confidence: this._calculateScoreConfidence(components)
      };
    });

    return scores;
  }

  // Utility methods

  _calculateFrequencies(data, type) {
    const frequencies = new Map();

    data.forEach(result => {
      if (!result.numbers || !result.numbers[type]) return;

      result.numbers[type].forEach(num => {
        const normalizedNum = String(num).padStart(2, '0');
        frequencies.set(normalizedNum, (frequencies.get(normalizedNum) || 0) + 1);
      });
    });

    return frequencies;
  }

  _calculateMovingAverages(data, type) {
    const window = this.options.movingAverageWindow;
    const averages = [];

    for (let i = window - 1; i < data.length; i++) {
      const windowData = data.slice(i - window + 1, i + 1);
      const frequencies = this._calculateFrequencies(windowData, type);
      const avgFreq = Array.from(frequencies.values()).reduce((sum, freq) => sum + freq, 0) / frequencies.size || 0;
      averages.push(avgFreq);
    }

    return averages;
  }

  _calculateVolatility(data, type) {
    const frequencies = this._calculateFrequencies(data, type);
    const values = Array.from(frequencies.values());

    if (values.length === 0) return 0;

    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;

    return Math.sqrt(variance);
  }

  _identifyPeriodTrends(data, type) {
    // Identify specific trends within a period
    const trends = [];
    const frequencies = this._calculateFrequencies(data, type);

    // Sort by frequency to identify trends
    const sorted = Array.from(frequencies.entries()).sort((a, b) => b[1] - a[1]);

    if (sorted.length > 0) {
      trends.push({
        type: 'most_frequent',
        numbers: sorted.slice(0, 3).map(([num, freq]) => ({ number: num, frequency: freq }))
      });

      trends.push({
        type: 'least_frequent',
        numbers: sorted.slice(-3).map(([num, freq]) => ({ number: num, frequency: freq }))
      });
    }

    return trends;
  }

  _generatePeriodSummary(data, type, periodName) {
    if (data.length === 0) return `No data for ${periodName} period`;

    const frequencies = this._calculateFrequencies(data, type);
    const totalNumbers = Array.from(frequencies.values()).reduce((sum, freq) => sum + freq, 0);
    const uniqueNumbers = frequencies.size;

    return `${periodName} period: ${data.length} draws, ${totalNumbers} numbers, ${uniqueNumbers} unique numbers`;
  }

  _classifyTrend(change, percentChange) {
    if (Math.abs(percentChange) < 5) return 'stable';
    if (change > 0) return percentChange > 20 ? 'strongly_increasing' : 'increasing';
    return percentChange < -20 ? 'strongly_decreasing' : 'decreasing';
  }

  _calculateTrendSignificance(recentCount, olderCount, recentTotal, olderTotal) {
    // Simple chi-square test for significance
    const expected1 = (recentCount + olderCount) * recentTotal / (recentTotal + olderTotal);
    const expected2 = (recentCount + olderCount) * olderTotal / (recentTotal + olderTotal);

    if (expected1 === 0 || expected2 === 0) return 0;

    const chiSquare = Math.pow(recentCount - expected1, 2) / expected1 +
                     Math.pow(olderCount - expected2, 2) / expected2;

    return chiSquare > 3.84 ? 'significant' : 'not_significant'; // p < 0.05
  }

  _calculateAcceleration(data, type) {
    if (data.length < 3) return 0;

    const frequencies = [];
    const windowSize = Math.floor(data.length / 3);

    for (let i = 0; i < 3; i++) {
      const start = i * windowSize;
      const end = i === 2 ? data.length : (i + 1) * windowSize;
      const windowData = data.slice(start, end);
      const freq = this._calculateFrequencies(windowData, type);
      const avgFreq = Array.from(freq.values()).reduce((sum, f) => sum + f, 0) / freq.size || 0;
      frequencies.push(avgFreq);
    }

    // Calculate acceleration (second derivative)
    if (frequencies.length >= 3) {
      return frequencies[2] - 2 * frequencies[1] + frequencies[0];
    }

    return 0;
  }

  _calculateAverageFrequency(frequencies) {
    if (frequencies.size === 0) return 0;
    const values = Array.from(frequencies.values());
    return values.reduce((sum, freq) => sum + freq, 0) / values.length;
  }

  _calculateMomentumDirection(data, type) {
    if (data.length < 2) return 'neutral';

    const midpoint = Math.floor(data.length / 2);
    const firstHalf = data.slice(0, midpoint);
    const secondHalf = data.slice(midpoint);

    const firstFreq = this._calculateAverageFrequency(this._calculateFrequencies(firstHalf, type));
    const secondFreq = this._calculateAverageFrequency(this._calculateFrequencies(secondHalf, type));

    const change = secondFreq - firstFreq;
    if (Math.abs(change) < 0.1) return 'neutral';
    return change > 0 ? 'increasing' : 'decreasing';
  }

  _extractUniqueNumbers(data, type) {
    const numbers = new Set();
    data.forEach(result => {
      if (result.numbers && result.numbers[type]) {
        result.numbers[type].forEach(num => {
          numbers.add(String(num).padStart(2, '0'));
        });
      }
    });
    return Array.from(numbers);
  }

  _calculateNumberTrendScore(number, data, type) {
    const recentData = data.slice(-this.options.shortTermPeriod);
    const olderData = data.slice(-this.options.longTermPeriod, -this.options.shortTermPeriod);

    const recentFreq = this._calculateFrequencies(recentData, type);
    const olderFreq = this._calculateFrequencies(olderData, type);

    const recentCount = recentFreq.get(number) || 0;
    const olderCount = olderFreq.get(number) || 0;

    const recentRate = recentData.length > 0 ? recentCount / recentData.length : 0;
    const olderRate = olderData.length > 0 ? olderCount / olderData.length : 0;

    const momentum = recentRate - olderRate;
    const consistency = this._calculateNumberConsistency(number, data, type);

    return {
      overall: (recentRate * 0.4 + momentum * 0.4 + consistency * 0.2) * 100,
      components: {
        recentRate: recentRate * 100,
        momentum: momentum * 100,
        consistency: consistency * 100
      },
      confidence: Math.min(100, (recentData.length + olderData.length) / 50 * 100)
    };
  }

  _calculateNumberConsistency(number, data, type) {
    const windowSize = 10;
    const windows = [];

    for (let i = 0; i <= data.length - windowSize; i += windowSize) {
      const windowData = data.slice(i, i + windowSize);
      const freq = this._calculateFrequencies(windowData, type);
      const rate = (freq.get(number) || 0) / windowData.length;
      windows.push(rate);
    }

    if (windows.length < 2) return 0;

    const mean = windows.reduce((sum, rate) => sum + rate, 0) / windows.length;
    const variance = windows.reduce((sum, rate) => sum + Math.pow(rate - mean, 2), 0) / windows.length;

    // Higher consistency = lower variance
    return Math.max(0, 1 - Math.sqrt(variance));
  }

  _getTrendRecommendation(score) {
    if (score >= 80) return 'strong_buy';
    if (score >= 60) return 'buy';
    if (score >= 40) return 'hold';
    if (score >= 20) return 'weak_sell';
    return 'sell';
  }

  _calculateAverageSum(data, type) {
    const sums = data.map(result => {
      if (!result.numbers || !result.numbers[type]) return 0;
      return result.numbers[type].reduce((sum, num) => sum + parseInt(num), 0);
    }).filter(sum => sum > 0);

    return sums.length > 0 ? sums.reduce((sum, val) => sum + val, 0) / sums.length : 0;
  }

  _getMostCommonNumbers(data, type, count) {
    const frequencies = this._calculateFrequencies(data, type);
    return Array.from(frequencies.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, count)
      .map(([number, frequency]) => ({ number, frequency }));
  }

  _getLeastCommonNumbers(data, type, count) {
    const frequencies = this._calculateFrequencies(data, type);
    return Array.from(frequencies.entries())
      .sort((a, b) => a[1] - b[1])
      .slice(0, count)
      .map(([number, frequency]) => ({ number, frequency }));
  }

  _calculateNumberMomentum(number, momentum) {
    let score = 0;
    let count = 0;

    Object.values(momentum).forEach(period => {
      if (period.direction === 'increasing') score += 1;
      else if (period.direction === 'decreasing') score -= 1;
      count++;
    });

    return count > 0 ? score / count : 0;
  }

  _calculateScoreConfidence(components) {
    const values = Object.values(components).filter(v => typeof v === 'number');
    if (values.length === 0) return 0;

    const variance = values.reduce((sum, val) => {
      const mean = values.reduce((s, v) => s + v, 0) / values.length;
      return sum + Math.pow(val - mean, 2);
    }, 0) / values.length;

    // Lower variance = higher confidence
    return Math.max(0, Math.min(100, 100 - Math.sqrt(variance) * 10));
  }
}

module.exports = TrendAnalyzer;