// Number Analysis System - Detailed analysis for individual numbers
// Provides historical performance tracking and correlation analysis

class NumberAnalyzer {
  constructor(options = {}) {
    this.options = {
      minDataPoints: options.minDataPoints || 30,
      correlationThreshold: options.correlationThreshold || 0.3,
      trendPeriods: {
        short: options.shortPeriod || 7,
        medium: options.mediumPeriod || 30,
        long: options.longPeriod || 90
      },
      ...options
    };

    this.performanceCache = new Map();
    this.correlationCache = new Map();
  }

  /**
   * Analyze a specific number with comprehensive insights
   * @param {string} number - The number to analyze
   * @param {Array} historicalData - Historical lottery data
   * @param {string} type - 'lo' or 'de'
   * @returns {Object} Detailed analysis results
   */
  analyzeNumber(number, historicalData, type = 'lo') {
    if (!Array.isArray(historicalData) || historicalData.length === 0) {
      throw new Error('Historical data is required for number analysis');
    }

    const cacheKey = `${number}_${type}_${historicalData.length}`;
    if (this.performanceCache.has(cacheKey)) {
      return this.performanceCache.get(cacheKey);
    }

    const analysis = {
      number,
      type,
      dataPoints: historicalData.length,
      ...this._calculateBasicStats(number, historicalData, type),
      ...this._calculateTrendAnalysis(number, historicalData, type),
      ...this._calculatePerformanceMetrics(number, historicalData, type),
      ...this._calculatePatternAnalysis(number, historicalData, type),
      correlations: this._calculateCorrelations(number, historicalData, type),
      recommendations: [],
      lastUpdated: new Date()
    };

    // Generate recommendations based on analysis
    analysis.recommendations = this._generateRecommendations(analysis);

    // Cache the result
    this.performanceCache.set(cacheKey, analysis);

    return analysis;
  }

  /**
   * Track historical performance for a number over time
   * @param {string} number - The number to track
   * @param {Array} historicalData - Historical lottery data
   * @param {string} type - 'lo' or 'de'
   * @returns {Object} Performance tracking data
   */
  trackHistoricalPerformance(number, historicalData, type = 'lo') {
    const occurrences = this._findOccurrences(number, historicalData, type);
    const performanceData = [];

    // Group by time periods for trend analysis
    const periods = this._groupByTimePeriods(occurrences, historicalData);

    Object.entries(periods).forEach(([period, data]) => {
      const frequency = data.occurrences / data.totalDraws;
      const expectedFrequency = this._calculateExpectedFrequency(type);

      performanceData.push({
        period,
        totalDraws: data.totalDraws,
        occurrences: data.occurrences,
        frequency,
        expectedFrequency,
        performance: frequency / expectedFrequency,
        trend: this._calculateTrend(data.occurrences, data.dates),
        dates: data.dates
      });
    });

    return {
      number,
      type,
      overallPerformance: this._calculateOverallPerformance(performanceData),
      periodPerformance: performanceData,
      streaks: this._calculateStreaks(occurrences),
      gaps: this._calculateGaps(occurrences, historicalData),
      volatility: this._calculateVolatility(performanceData)
    };
  }

  /**
   * Calculate correlations between numbers
   * @param {string} targetNumber - The target number
   * @param {Array} historicalData - Historical lottery data
   * @param {string} type - 'lo' or 'de'
   * @returns {Array} Correlation analysis results
   */
  _calculateCorrelations(targetNumber, historicalData, type) {
    const cacheKey = `corr_${targetNumber}_${type}_${historicalData.length}`;
    if (this.correlationCache.has(cacheKey)) {
      return this.correlationCache.get(cacheKey);
    }

    const targetOccurrences = this._findOccurrences(targetNumber, historicalData, type);
    const allNumbers = this._getAllUniqueNumbers(historicalData, type);
    const correlations = [];

    allNumbers.forEach(otherNumber => {
      if (otherNumber === targetNumber) return;

      const otherOccurrences = this._findOccurrences(otherNumber, historicalData, type);
      const correlation = this._calculatePearsonCorrelation(
        targetOccurrences,
        otherOccurrences,
        historicalData.length
      );

      if (Math.abs(correlation) >= this.options.correlationThreshold) {
        correlations.push({
          number: otherNumber,
          correlation,
          strength: this._getCorrelationStrength(correlation),
          coOccurrences: this._calculateCoOccurrences(targetNumber, otherNumber, historicalData, type),
          significance: this._calculateSignificance(correlation, historicalData.length)
        });
      }
    });

    // Sort by correlation strength
    correlations.sort((a, b) => Math.abs(b.correlation) - Math.abs(a.correlation));

    // Cache the result
    this.correlationCache.set(cacheKey, correlations.slice(0, 10)); // Top 10 correlations

    return correlations.slice(0, 10);
  }

  /**
   * Calculate basic statistics for a number
   */
  _calculateBasicStats(number, historicalData, type) {
    const occurrences = this._findOccurrences(number, historicalData, type);
    const totalDraws = historicalData.length;
    const frequency = occurrences.length / totalDraws;
    const expectedFrequency = this._calculateExpectedFrequency(type);

    return {
      totalOccurrences: occurrences.length,
      totalDraws,
      frequency,
      expectedFrequency,
      relativeFrequency: frequency / expectedFrequency,
      lastSeen: occurrences.length > 0 ? occurrences[occurrences.length - 1].date : null,
      firstSeen: occurrences.length > 0 ? occurrences[0].date : null,
      daysSinceLastSeen: occurrences.length > 0
        ? Math.floor((new Date() - new Date(occurrences[occurrences.length - 1].date)) / (1000 * 60 * 60 * 24))
        : null
    };
  }

  /**
   * Calculate trend analysis for different time periods
   */
  _calculateTrendAnalysis(number, historicalData, type) {
    const trends = {};

    Object.entries(this.options.trendPeriods).forEach(([periodName, days]) => {
      const recentData = historicalData.slice(-days);
      if (recentData.length < this.options.minDataPoints) return;

      const occurrences = this._findOccurrences(number, recentData, type);
      const frequency = occurrences.length / recentData.length;
      const expectedFrequency = this._calculateExpectedFrequency(type);

      trends[periodName] = {
        period: days,
        occurrences: occurrences.length,
        frequency,
        trend: frequency > expectedFrequency ? 'hot' : frequency < expectedFrequency ? 'cold' : 'neutral',
        strength: Math.abs(frequency - expectedFrequency) / expectedFrequency,
        momentum: this._calculateMomentum(occurrences, recentData)
      };
    });

    return { trends };
  }

  /**
   * Calculate performance metrics
   */
  _calculatePerformanceMetrics(number, historicalData, type) {
    const occurrences = this._findOccurrences(number, historicalData, type);

    return {
      consistency: this._calculateConsistency(occurrences, historicalData),
      predictability: this._calculatePredictability(occurrences, historicalData),
      volatility: this._calculateNumberVolatility(occurrences, historicalData),
      momentum: this._calculateMomentum(occurrences, historicalData),
      seasonality: this._calculateSeasonality(occurrences)
    };
  }

  /**
   * Calculate pattern analysis
   */
  _calculatePatternAnalysis(number, historicalData, type) {
    const occurrences = this._findOccurrences(number, historicalData, type);

    return {
      patterns: {
        weekdayPreference: this._calculateWeekdayPreference(occurrences),
        monthlyDistribution: this._calculateMonthlyDistribution(occurrences),
        consecutiveAppearances: this._findConsecutiveAppearances(occurrences),
        cyclicPatterns: this._findCyclicPatterns(occurrences, historicalData)
      }
    };
  }

  /**
   * Generate recommendations based on analysis
   */
  _generateRecommendations(analysis) {
    const recommendations = [];

    // Hot/Cold recommendations
    if (analysis.trends.short && analysis.trends.short.trend === 'hot') {
      recommendations.push({
        type: 'trend',
        priority: 'high',
        message: `Số ${analysis.number} đang trong xu hướng nóng (${analysis.trends.short.occurrences} lần trong ${analysis.trends.short.period} kỳ gần đây)`,
        confidence: Math.min(95, 60 + analysis.trends.short.strength * 30)
      });
    }

    if (analysis.trends.short && analysis.trends.short.trend === 'cold') {
      recommendations.push({
        type: 'trend',
        priority: 'medium',
        message: `Số ${analysis.number} đang trong xu hướng lạnh, có thể sắp xuất hiện`,
        confidence: Math.min(80, 40 + analysis.daysSinceLastSeen * 2)
      });
    }

    // Correlation recommendations
    if (analysis.correlations.length > 0) {
      const strongCorrelation = analysis.correlations[0];
      recommendations.push({
        type: 'correlation',
        priority: 'medium',
        message: `Số ${analysis.number} có mối tương quan ${strongCorrelation.strength} với số ${strongCorrelation.number}`,
        confidence: Math.round(Math.abs(strongCorrelation.correlation) * 100)
      });
    }

    // Pattern recommendations
    if (analysis.patterns.cyclicPatterns.length > 0) {
      const pattern = analysis.patterns.cyclicPatterns[0];
      recommendations.push({
        type: 'pattern',
        priority: 'low',
        message: `Số ${analysis.number} có xu hướng xuất hiện theo chu kỳ ${pattern.cycle} kỳ`,
        confidence: pattern.confidence
      });
    }

    return recommendations.sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      return priorityOrder[b.priority] - priorityOrder[a.priority];
    });
  }

  // Helper methods

  _findOccurrences(number, historicalData, type) {
    const occurrences = [];

    historicalData.forEach((draw, index) => {
      if (draw.numbers && draw.numbers[type] && draw.numbers[type].includes(number)) {
        occurrences.push({
          date: draw.date,
          index,
          draw
        });
      }
    });

    return occurrences;
  }

  _getAllUniqueNumbers(historicalData, type) {
    const numbers = new Set();

    historicalData.forEach(draw => {
      if (draw.numbers && draw.numbers[type]) {
        draw.numbers[type].forEach(num => numbers.add(num));
      }
    });

    return Array.from(numbers);
  }

  _calculateExpectedFrequency(type) {
    // For Vietnamese lottery:
    // - 'lo': typically 27 numbers drawn from 100 possible (00-99)
    // - 'de': only 1 number drawn from 100 possible (00-99) - last 2 digits of special prize
    return type === 'lo' ? 0.27 : 0.01;
  }

  _calculatePearsonCorrelation(occurrences1, occurrences2, totalDraws) {
    // Create binary arrays for correlation calculation
    const array1 = new Array(totalDraws).fill(0);
    const array2 = new Array(totalDraws).fill(0);

    occurrences1.forEach(occ => array1[occ.index] = 1);
    occurrences2.forEach(occ => array2[occ.index] = 1);

    const n = totalDraws;
    const sum1 = array1.reduce((a, b) => a + b, 0);
    const sum2 = array2.reduce((a, b) => a + b, 0);
    const sum1Sq = array1.reduce((a, b) => a + b * b, 0);
    const sum2Sq = array2.reduce((a, b) => a + b * b, 0);
    const pSum = array1.reduce((sum, val, i) => sum + val * array2[i], 0);

    const num = pSum - (sum1 * sum2 / n);
    const den = Math.sqrt((sum1Sq - sum1 * sum1 / n) * (sum2Sq - sum2 * sum2 / n));

    return den === 0 ? 0 : num / den;
  }

  _getCorrelationStrength(correlation) {
    const abs = Math.abs(correlation);
    if (abs >= 0.7) return 'very strong';
    if (abs >= 0.5) return 'strong';
    if (abs >= 0.3) return 'moderate';
    if (abs >= 0.1) return 'weak';
    return 'very weak';
  }

  _calculateCoOccurrences(number1, number2, historicalData, type) {
    let coOccurrences = 0;

    historicalData.forEach(draw => {
      if (draw.numbers && draw.numbers[type]) {
        const numbers = draw.numbers[type];
        if (numbers.includes(number1) && numbers.includes(number2)) {
          coOccurrences++;
        }
      }
    });

    return coOccurrences;
  }

  _calculateSignificance(correlation, sampleSize) {
    // Simple significance test based on sample size
    const tStat = Math.abs(correlation) * Math.sqrt((sampleSize - 2) / (1 - correlation * correlation));

    if (tStat > 2.576) return 'very significant'; // p < 0.01
    if (tStat > 1.96) return 'significant'; // p < 0.05
    if (tStat > 1.645) return 'marginally significant'; // p < 0.1
    return 'not significant';
  }

  _groupByTimePeriods(occurrences, historicalData) {
    const now = new Date();
    const periods = {
      week: { occurrences: 0, totalDraws: 0, dates: [] },
      month: { occurrences: 0, totalDraws: 0, dates: [] },
      quarter: { occurrences: 0, totalDraws: 0, dates: [] },
      year: { occurrences: 0, totalDraws: 0, dates: [] }
    };

    const cutoffs = {
      week: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000),
      month: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000),
      quarter: new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000),
      year: new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000)
    };

    // Count total draws in each period
    historicalData.forEach(draw => {
      const drawDate = new Date(draw.date);
      Object.entries(cutoffs).forEach(([period, cutoff]) => {
        if (drawDate >= cutoff) {
          periods[period].totalDraws++;
        }
      });
    });

    // Count occurrences in each period
    occurrences.forEach(occ => {
      const occDate = new Date(occ.date);
      Object.entries(cutoffs).forEach(([period, cutoff]) => {
        if (occDate >= cutoff) {
          periods[period].occurrences++;
          periods[period].dates.push(occDate);
        }
      });
    });

    return periods;
  }

  _calculateOverallPerformance(performanceData) {
    if (performanceData.length === 0) return 0;

    const avgPerformance = performanceData.reduce((sum, p) => sum + p.performance, 0) / performanceData.length;
    return avgPerformance;
  }

  _calculateStreaks(occurrences) {
    if (occurrences.length < 2) return { longest: 0, current: 0, average: 0 };

    const streaks = [];
    let currentStreak = 1;

    for (let i = 1; i < occurrences.length; i++) {
      const prevIndex = occurrences[i - 1].index;
      const currIndex = occurrences[i].index;

      if (currIndex - prevIndex === 1) {
        currentStreak++;
      } else {
        if (currentStreak > 1) streaks.push(currentStreak);
        currentStreak = 1;
      }
    }

    if (currentStreak > 1) streaks.push(currentStreak);

    return {
      longest: streaks.length > 0 ? Math.max(...streaks) : 0,
      current: currentStreak > 1 ? currentStreak : 0,
      average: streaks.length > 0 ? streaks.reduce((a, b) => a + b, 0) / streaks.length : 0,
      total: streaks.length
    };
  }

  _calculateGaps(occurrences, historicalData) {
    if (occurrences.length < 2) return { longest: 0, current: 0, average: 0 };

    const gaps = [];

    for (let i = 1; i < occurrences.length; i++) {
      const gap = occurrences[i].index - occurrences[i - 1].index - 1;
      if (gap > 0) gaps.push(gap);
    }

    // Current gap (since last occurrence)
    const lastOccurrence = occurrences[occurrences.length - 1];
    const currentGap = historicalData.length - 1 - lastOccurrence.index;

    return {
      longest: gaps.length > 0 ? Math.max(...gaps) : 0,
      current: currentGap,
      average: gaps.length > 0 ? gaps.reduce((a, b) => a + b, 0) / gaps.length : 0,
      total: gaps.length
    };
  }

  _calculateVolatility(performanceData) {
    if (performanceData.length < 2) return 0;

    const frequencies = performanceData.map(p => p.frequency);
    const mean = frequencies.reduce((a, b) => a + b, 0) / frequencies.length;
    const variance = frequencies.reduce((sum, freq) => sum + Math.pow(freq - mean, 2), 0) / frequencies.length;

    return Math.sqrt(variance);
  }

  _calculateTrend(occurrences, dates) {
    if (occurrences.length < 2) return 'stable';

    // Simple trend calculation based on recent vs older occurrences
    const midpoint = Math.floor(dates.length / 2);
    const recentCount = dates.slice(midpoint).length;
    const olderCount = dates.slice(0, midpoint).length;

    const recentRate = recentCount / (dates.length - midpoint);
    const olderRate = olderCount / midpoint;

    if (recentRate > olderRate * 1.2) return 'increasing';
    if (recentRate < olderRate * 0.8) return 'decreasing';
    return 'stable';
  }

  _calculateConsistency(occurrences, historicalData) {
    if (occurrences.length < 3) return 0;

    const gaps = [];
    for (let i = 1; i < occurrences.length; i++) {
      gaps.push(occurrences[i].index - occurrences[i - 1].index);
    }

    const meanGap = gaps.reduce((a, b) => a + b, 0) / gaps.length;
    const variance = gaps.reduce((sum, gap) => sum + Math.pow(gap - meanGap, 2), 0) / gaps.length;
    const stdDev = Math.sqrt(variance);

    // Lower standard deviation = higher consistency
    return Math.max(0, 1 - (stdDev / meanGap));
  }

  _calculatePredictability(occurrences, historicalData) {
    // Based on pattern regularity and trend stability
    const consistency = this._calculateConsistency(occurrences, historicalData);
    const cyclicPatterns = this._findCyclicPatterns(occurrences, historicalData);

    let patternScore = 0;
    if (cyclicPatterns.length > 0) {
      patternScore = cyclicPatterns[0].confidence / 100;
    }

    return (consistency + patternScore) / 2;
  }

  _calculateNumberVolatility(occurrences, historicalData) {
    if (occurrences.length < 5) return 0;

    // Calculate frequency in sliding windows
    const windowSize = Math.max(10, Math.floor(historicalData.length / 10));
    const frequencies = [];

    for (let i = 0; i <= historicalData.length - windowSize; i += windowSize) {
      const window = historicalData.slice(i, i + windowSize);
      const windowOccurrences = occurrences.filter(occ =>
        occ.index >= i && occ.index < i + windowSize
      );
      frequencies.push(windowOccurrences.length / windowSize);
    }

    if (frequencies.length < 2) return 0;

    const mean = frequencies.reduce((a, b) => a + b, 0) / frequencies.length;
    const variance = frequencies.reduce((sum, freq) => sum + Math.pow(freq - mean, 2), 0) / frequencies.length;

    return Math.sqrt(variance);
  }

  _calculateMomentum(occurrences, historicalData) {
    if (occurrences.length < 3) return 0;

    const recentPeriod = Math.min(30, Math.floor(historicalData.length / 3));
    const recentData = historicalData.slice(-recentPeriod);
    const recentOccurrences = occurrences.filter(occ =>
      occ.index >= historicalData.length - recentPeriod
    );

    const recentFreq = recentOccurrences.length / recentData.length;
    const overallFreq = occurrences.length / historicalData.length;

    return recentFreq - overallFreq;
  }

  _calculateSeasonality(occurrences) {
    if (occurrences.length < 12) return {};

    const monthlyCount = new Array(12).fill(0);
    const weekdayCount = new Array(7).fill(0);

    occurrences.forEach(occ => {
      const date = new Date(occ.date);
      monthlyCount[date.getMonth()]++;
      weekdayCount[date.getDay()]++;
    });

    return {
      monthly: monthlyCount.map((count, month) => ({
        month: month + 1,
        count,
        frequency: count / occurrences.length
      })),
      weekday: weekdayCount.map((count, day) => ({
        day,
        count,
        frequency: count / occurrences.length
      }))
    };
  }

  _calculateWeekdayPreference(occurrences) {
    const weekdayCount = new Array(7).fill(0);
    const weekdays = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];

    occurrences.forEach(occ => {
      const date = new Date(occ.date);
      weekdayCount[date.getDay()]++;
    });

    const maxCount = Math.max(...weekdayCount);
    const preferredDay = weekdayCount.indexOf(maxCount);

    return {
      preferredDay: weekdays[preferredDay],
      distribution: weekdayCount.map((count, index) => ({
        day: weekdays[index],
        count,
        percentage: occurrences.length > 0 ? (count / occurrences.length) * 100 : 0
      }))
    };
  }

  _calculateMonthlyDistribution(occurrences) {
    const monthlyCount = new Array(12).fill(0);
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                   'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

    occurrences.forEach(occ => {
      const date = new Date(occ.date);
      monthlyCount[date.getMonth()]++;
    });

    return monthlyCount.map((count, index) => ({
      month: months[index],
      count,
      percentage: occurrences.length > 0 ? (count / occurrences.length) * 100 : 0
    }));
  }

  _findConsecutiveAppearances(occurrences) {
    const consecutive = [];
    let current = [];

    for (let i = 0; i < occurrences.length; i++) {
      if (i === 0 || occurrences[i].index - occurrences[i - 1].index === 1) {
        current.push(occurrences[i]);
      } else {
        if (current.length > 1) {
          consecutive.push([...current]);
        }
        current = [occurrences[i]];
      }
    }

    if (current.length > 1) {
      consecutive.push(current);
    }

    return consecutive.map(seq => ({
      length: seq.length,
      startDate: seq[0].date,
      endDate: seq[seq.length - 1].date,
      indices: seq.map(occ => occ.index)
    }));
  }

  _findCyclicPatterns(occurrences, historicalData) {
    if (occurrences.length < 6) return [];

    const patterns = [];
    const maxCycle = Math.min(50, Math.floor(historicalData.length / 4));

    // Test different cycle lengths
    for (let cycle = 3; cycle <= maxCycle; cycle++) {
      const matches = this._testCyclicPattern(occurrences, cycle);
      if (matches.confidence > 60) {
        patterns.push({
          cycle,
          confidence: matches.confidence,
          matches: matches.count,
          total: matches.total,
          description: `Xuất hiện theo chu kỳ ${cycle} kỳ với độ tin cậy ${matches.confidence}%`
        });
      }
    }

    return patterns.sort((a, b) => b.confidence - a.confidence);
  }

  _testCyclicPattern(occurrences, cycle) {
    let matches = 0;
    let total = 0;

    for (let i = 1; i < occurrences.length; i++) {
      const gap = occurrences[i].index - occurrences[i - 1].index;
      total++;

      if (Math.abs(gap - cycle) <= 1) { // Allow ±1 tolerance
        matches++;
      }
    }

    return {
      count: matches,
      total,
      confidence: total > 0 ? Math.round((matches / total) * 100) : 0
    };
  }

  /**
   * Clear caches to free memory
   */
  clearCache() {
    this.performanceCache.clear();
    this.correlationCache.clear();
  }

  /**
   * Get cache statistics
   */
  getCacheStats() {
    return {
      performanceCache: this.performanceCache.size,
      correlationCache: this.correlationCache.size
    };
  }
}

module.exports = NumberAnalyzer;