// Statistical Analysis Engine for Lottery Prediction
// Implements frequency analysis, probability calculations, and pattern detection

class StatisticalAnalyzer {
  constructor(options = {}) {
    this.options = {
      defaultLookbackPeriod: options.defaultLookbackPeriod || 100,
      minFrequency: options.minFrequency || 3,
      hotColdThreshold: options.hotColdThreshold || 0.15, // 15% above/below average
      patternMinLength: options.patternMinLength || 3,
      confidenceThreshold: options.confidenceThreshold || 0.6,
      ...options
    };
  }

  /**
   * Calculate frequency distribution for numbers
   * @param {Array} historicalData - Array of lottery results
   * @param {string} type - 'lo' or 'de'
   * @param {number} period - Number of recent results to analyze
   * @returns {Map} Number frequencies
   */
  calculateFrequencies(historicalData, type = 'lo', period = null) {
    const dataToAnalyze = period ? historicalData.slice(-period) : historicalData;
    const frequencies = new Map();

    dataToAnalyze.forEach(result => {
      if (!result.numbers || !result.numbers[type]) return;

      result.numbers[type].forEach(num => {
        const normalizedNum = this._normalizeNumber(num);
        frequencies.set(normalizedNum, (frequencies.get(normalizedNum) || 0) + 1);
      });
    });

    return frequencies;
  }

  /**
   * Calculate probability distribution from frequencies
   * @param {Map} frequencies - Number frequencies
   * @returns {Map} Number probabilities
   */
  calculateProbabilities(frequencies) {
    const total = Array.from(frequencies.values()).reduce((sum, freq) => sum + freq, 0);
    const probabilities = new Map();

    if (total === 0) return probabilities;

    frequencies.forEach((freq, number) => {
      probabilities.set(number, freq / total);
    });

    return probabilities;
  }

  /**
   * Analyze hot and cold numbers with configurable time periods
   * @param {Array} historicalData - Array of lottery results
   * @param {string} type - 'lo' or 'de'
   * @param {Object} options - Analysis options
   * @returns {Object} Hot and cold number analysis
   */
  analyzeHotColdNumbers(historicalData, type = 'lo', options = {}) {
    const {
      shortPeriod = 30,
      mediumPeriod = 90,
      longPeriod = 180
    } = options;

    const shortTermFreq = this.calculateFrequencies(historicalData, type, shortPeriod);
    const mediumTermFreq = this.calculateFrequencies(historicalData, type, mediumPeriod);
    const longTermFreq = this.calculateFrequencies(historicalData, type, longPeriod);

    const shortTermProb = this.calculateProbabilities(shortTermFreq);
    const mediumTermProb = this.calculateProbabilities(mediumTermFreq);
    const longTermProb = this.calculateProbabilities(longTermFreq);

    // Calculate average probabilities for baseline
    const avgShort = this._calculateAverageProbability(shortTermProb);
    const avgMedium = this._calculateAverageProbability(mediumTermProb);
    const avgLong = this._calculateAverageProbability(longTermProb);

    const analysis = {
      periods: {
        short: { days: shortPeriod, average: avgShort },
        medium: { days: mediumPeriod, average: avgMedium },
        long: { days: longPeriod, average: avgLong }
      },
      hot: this._identifyHotNumbers(shortTermProb, mediumTermProb, longTermProb),
      cold: this._identifyColdNumbers(shortTermProb, mediumTermProb, longTermProb),
      trending: this._identifyTrendingNumbers(shortTermProb, mediumTermProb, longTermProb)
    };

    return analysis;
  }

  /**
   * Detect patterns in number sequences
   * @param {Array} historicalData - Array of lottery results
   * @param {string} type - 'lo' or 'de'
   * @param {Object} options - Pattern detection options
   * @returns {Object} Detected patterns
   */
  detectPatterns(historicalData, type = 'lo', options = {}) {
    const {
      minLength = this.options.patternMinLength,
      maxGap = 5,
      minOccurrences = 3
    } = options;

    const sequences = this._extractNumberSequences(historicalData, type);

    return {
      consecutivePatterns: this._findConsecutivePatterns(sequences, minLength, minOccurrences),
      gapPatterns: this._findGapPatterns(sequences, maxGap, minOccurrences),
      cyclicPatterns: this._findCyclicPatterns(sequences, minOccurrences),
      sumPatterns: this._findSumPatterns(historicalData, type, minOccurrences)
    };
  }

  /**
   * Calculate comprehensive statistics for a specific number
   * @param {string} number - Number to analyze
   * @param {Array} historicalData - Array of lottery results
   * @param {string} type - 'lo' or 'de'
   * @returns {Object} Detailed number statistics
   */
  analyzeNumber(number, historicalData, type = 'lo') {
    const normalizedNum = this._normalizeNumber(number);
    const frequencies = this.calculateFrequencies(historicalData, type);
    const probabilities = this.calculateProbabilities(frequencies);

    const occurrences = this._findNumberOccurrences(normalizedNum, historicalData, type);
    const gaps = this._calculateGapsBetweenOccurrences(occurrences);

    return {
      number: normalizedNum,
      totalOccurrences: frequencies.get(normalizedNum) || 0,
      probability: probabilities.get(normalizedNum) || 0,
      lastSeen: occurrences.length > 0 ? occurrences[occurrences.length - 1].date : null,
      averageGap: gaps.length > 0 ? gaps.reduce((sum, gap) => sum + gap, 0) / gaps.length : null,
      minGap: gaps.length > 0 ? Math.min(...gaps) : null,
      maxGap: gaps.length > 0 ? Math.max(...gaps) : null,
      currentGap: this._calculateCurrentGap(normalizedNum, historicalData, type),
      trend: this._calculateNumberTrend(normalizedNum, historicalData, type),
      hotColdStatus: this._getHotColdStatus(normalizedNum, historicalData, type)
    };
  }

  /**
   * Generate predictions based on statistical analysis
   * @param {Array} historicalData - Array of lottery results
   * @param {string} type - 'lo' or 'de'
   * @param {Object} options - Prediction options
   * @returns {Object} Statistical predictions
   */
  generatePredictions(historicalData, type = 'lo', options = {}) {
    const {
      count = 10,
      includeReasons = true,
      weightHotNumbers = true,
      considerPatterns = true
    } = options;

    const frequencies = this.calculateFrequencies(historicalData, type);
    const probabilities = this.calculateProbabilities(frequencies);
    const hotColdAnalysis = this.analyzeHotColdNumbers(historicalData, type);

    let predictions = Array.from(probabilities.entries())
      .map(([number, probability]) => {
        const analysis = this.analyzeNumber(number, historicalData, type);
        let score = probability;

        // Weight adjustments
        if (weightHotNumbers && hotColdAnalysis.hot.some(h => h.number === number)) {
          score *= 1.2; // Boost hot numbers
        }
        if (hotColdAnalysis.cold.some(c => c.number === number)) {
          score *= 0.8; // Reduce cold numbers
        }

        // Pattern consideration
        if (considerPatterns) {
          const patterns = this.detectPatterns(historicalData, type);
          if (this._numberInPatterns(number, patterns)) {
            score *= 1.1; // Slight boost for pattern numbers
          }
        }

        return {
          number,
          confidence: Math.round(score * 100),
          probability: probability,
          reasoning: includeReasons ? this._generateReasoning(analysis, hotColdAnalysis) : null,
          metadata: {
            frequency: frequencies.get(number) || 0,
            trend: analysis.trend,
            hotColdStatus: analysis.hotColdStatus
          }
        };
      })
      .sort((a, b) => b.confidence - a.confidence)
      .slice(0, count);

    return {
      predictions,
      confidence: this._calculateOverallConfidence(predictions),
      method: 'statistical',
      analysisDate: new Date(),
      dataPoints: historicalData.length
    };
  }

  // Private helper methods

  _normalizeNumber(number) {
    return String(number).padStart(2, '0');
  }

  _calculateAverageProbability(probabilities) {
    if (probabilities.size === 0) return 0;
    const values = Array.from(probabilities.values());
    return values.reduce((sum, prob) => sum + prob, 0) / values.length;
  }

  _identifyHotNumbers(shortProb, mediumProb, longProb) {
    const avgShort = this._calculateAverageProbability(shortProb);
    const threshold = avgShort * (1 + this.options.hotColdThreshold);

    return Array.from(shortProb.entries())
      .filter(([number, prob]) => prob > threshold)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .map(([number, probability]) => ({
        number,
        probability,
        intensity: probability / avgShort,
        periods: {
          short: shortProb.get(number) || 0,
          medium: mediumProb.get(number) || 0,
          long: longProb.get(number) || 0
        }
      }));
  }

  _identifyColdNumbers(shortProb, mediumProb, longProb) {
    const avgShort = this._calculateAverageProbability(shortProb);
    const threshold = avgShort * (1 - this.options.hotColdThreshold);

    return Array.from(shortProb.entries())
      .filter(([number, prob]) => prob < threshold)
      .sort((a, b) => a[1] - b[1])
      .slice(0, 10)
      .map(([number, probability]) => ({
        number,
        probability,
        intensity: avgShort > 0 ? probability / avgShort : 0,
        periods: {
          short: shortProb.get(number) || 0,
          medium: mediumProb.get(number) || 0,
          long: longProb.get(number) || 0
        }
      }));
  }

  _identifyTrendingNumbers(shortProb, mediumProb, longProb) {
    const trending = [];

    shortProb.forEach((shortP, number) => {
      const mediumP = mediumProb.get(number) || 0;
      const longP = longProb.get(number) || 0;

      if (shortP > mediumP && mediumP > longP && shortP > 0) {
        trending.push({
          number,
          trend: 'up',
          momentum: shortP - longP,
          probabilities: { short: shortP, medium: mediumP, long: longP }
        });
      } else if (shortP < mediumP && mediumP < longP && longP > 0) {
        trending.push({
          number,
          trend: 'down',
          momentum: longP - shortP,
          probabilities: { short: shortP, medium: mediumP, long: longP }
        });
      }
    });

    return trending.sort((a, b) => b.momentum - a.momentum);
  }

  _extractNumberSequences(historicalData, type) {
    return historicalData.map(result => {
      if (!result.numbers || !result.numbers[type]) return [];
      return result.numbers[type].map(num => this._normalizeNumber(num));
    }).filter(seq => seq.length > 0);
  }

  _findConsecutivePatterns(sequences, minLength, minOccurrences) {
    const patterns = new Map();

    sequences.forEach(sequence => {
      for (let i = 0; i <= sequence.length - minLength; i++) {
        const pattern = sequence.slice(i, i + minLength).join(',');
        patterns.set(pattern, (patterns.get(pattern) || 0) + 1);
      }
    });

    return Array.from(patterns.entries())
      .filter(([pattern, count]) => count >= minOccurrences)
      .sort((a, b) => b[1] - a[1])
      .map(([pattern, count]) => ({
        pattern: pattern.split(','),
        occurrences: count,
        probability: count / sequences.length
      }));
  }

  _findGapPatterns(sequences, maxGap, minOccurrences) {
    // Implementation for gap pattern detection
    // This would analyze gaps between specific numbers
    return [];
  }

  _findCyclicPatterns(sequences, minOccurrences) {
    // Implementation for cyclic pattern detection
    // This would look for repeating cycles in the data
    return [];
  }

  _findSumPatterns(historicalData, type, minOccurrences) {
    const sums = historicalData.map(result => {
      if (!result.numbers || !result.numbers[type]) return 0;
      return result.numbers[type].reduce((sum, num) => sum + parseInt(num), 0);
    });

    const sumFreq = new Map();
    sums.forEach(sum => {
      sumFreq.set(sum, (sumFreq.get(sum) || 0) + 1);
    });

    return Array.from(sumFreq.entries())
      .filter(([sum, count]) => count >= minOccurrences)
      .sort((a, b) => b[1] - a[1])
      .map(([sum, count]) => ({
        sum,
        occurrences: count,
        probability: count / sums.length
      }));
  }

  _findNumberOccurrences(number, historicalData, type) {
    const occurrences = [];

    historicalData.forEach((result, index) => {
      if (!result.numbers || !result.numbers[type]) return;

      if (result.numbers[type].some(num => this._normalizeNumber(num) === number)) {
        occurrences.push({
          index,
          date: result.date,
          position: result.numbers[type].findIndex(num => this._normalizeNumber(num) === number)
        });
      }
    });

    return occurrences;
  }

  _calculateGapsBetweenOccurrences(occurrences) {
    const gaps = [];
    for (let i = 1; i < occurrences.length; i++) {
      gaps.push(occurrences[i].index - occurrences[i - 1].index);
    }
    return gaps;
  }

  _calculateCurrentGap(number, historicalData, type) {
    const occurrences = this._findNumberOccurrences(number, historicalData, type);
    if (occurrences.length === 0) return historicalData.length;

    const lastOccurrence = occurrences[occurrences.length - 1];
    return historicalData.length - 1 - lastOccurrence.index;
  }

  _calculateNumberTrend(number, historicalData, type) {
    const recentPeriod = 30;
    const olderPeriod = 60;

    const recentFreq = this.calculateFrequencies(historicalData.slice(-recentPeriod), type);
    const olderFreq = this.calculateFrequencies(historicalData.slice(-olderPeriod, -recentPeriod), type);

    const recentCount = recentFreq.get(number) || 0;
    const olderCount = olderFreq.get(number) || 0;

    if (recentCount > olderCount) return 'increasing';
    if (recentCount < olderCount) return 'decreasing';
    return 'stable';
  }

  _getHotColdStatus(number, historicalData, type) {
    const analysis = this.analyzeHotColdNumbers(historicalData, type);

    if (analysis.hot.some(h => h.number === number)) return 'hot';
    if (analysis.cold.some(c => c.number === number)) return 'cold';
    return 'neutral';
  }

  _numberInPatterns(number, patterns) {
    return patterns.consecutivePatterns.some(p => p.pattern.includes(number)) ||
           patterns.gapPatterns.some(p => p.pattern && p.pattern.includes(number)) ||
           patterns.cyclicPatterns.some(p => p.pattern && p.pattern.includes(number));
  }

  _generateReasoning(analysis, hotColdAnalysis) {
    const reasons = [];

    if (analysis.totalOccurrences > 0) {
      reasons.push(`Xuất hiện ${analysis.totalOccurrences} lần (${(analysis.probability * 100).toFixed(1)}%)`);
    }

    if (analysis.hotColdStatus === 'hot') {
      reasons.push('Số nóng trong thời gian gần đây');
    } else if (analysis.hotColdStatus === 'cold') {
      reasons.push('Số lạnh, có thể sắp xuất hiện');
    }

    if (analysis.trend === 'increasing') {
      reasons.push('Xu hướng tăng');
    } else if (analysis.trend === 'decreasing') {
      reasons.push('Xu hướng giảm');
    }

    if (analysis.currentGap && analysis.averageGap && analysis.currentGap > analysis.averageGap) {
      reasons.push('Đã lâu không xuất hiện');
    }

    return reasons.join(', ') || 'Phân tích thống kê cơ bản';
  }

  _calculateOverallConfidence(predictions) {
    if (predictions.length === 0) return 0;

    const avgConfidence = predictions.reduce((sum, p) => sum + p.confidence, 0) / predictions.length;
    const topConfidence = predictions[0].confidence;
    const distribution = this._calculateConfidenceDistribution(predictions);

    // Confidence based on prediction quality and distribution
    return Math.round((avgConfidence * 0.4 + topConfidence * 0.4 + distribution * 0.2));
  }

  _calculateConfidenceDistribution(predictions) {
    if (predictions.length < 2) return 50;

    const confidences = predictions.map(p => p.confidence);
    const max = Math.max(...confidences);
    const min = Math.min(...confidences);
    const range = max - min;

    // Higher range indicates better discrimination
    return Math.min(100, (range / max) * 100);
  }
}

module.exports = StatisticalAnalyzer;