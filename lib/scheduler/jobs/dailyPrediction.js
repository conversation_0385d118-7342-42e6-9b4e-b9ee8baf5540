// Daily Prediction Job
// Generates and sends daily lottery predictions to subscribed groups

class DailyPredictionJob {
  constructor(botHandler, predictionEngine, dataManager, llmService) {
    this.botHandler = botHandler;
    this.predictionEngine = predictionEngine;
    this.dataManager = dataManager;
    this.llmService = llmService;
  }

  async execute() {
    // Execute daily prediction job
    console.log('Starting daily prediction job...');

    try {
      // Generate predictions for both lo and de
      const loPredictions = await this.predictionEngine.predictLo();
      const dePredictions = await this.predictionEngine.predictDe();

      // Generate AI reports
      const loReport = await this.llmService.generatePredictionReport(
        loPredictions.predictions,
        await this.getRecentHistory(),
        'lo'
      );

      const deReport = await this.llmService.generatePredictionReport(
        dePredictions.predictions,
        await this.getRecentHistory(),
        'de'
      );

      // Get subscribed groups
      const subscribedGroups = await this.getSubscribedGroups();

      // Send predictions to each group
      for (const group of subscribedGroups) {
        await this.sendDailyPrediction(group, {
          lo: { predictions: loPredictions, report: loReport },
          de: { predictions: dePredictions, report: deReport }
        });
      }

      console.log(`Daily predictions sent to ${subscribedGroups.length} groups`);

    } catch (error) {
      console.error('Daily prediction job failed:', error.message);
      throw error;
    }
  }

  async getRecentHistory() {
    // Get recent lottery history for context
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 30);

    return await this.dataManager.getLotteryHistory(
      startDate.toISOString().split('T')[0],
      endDate.toISOString().split('T')[0]
    );
  }

  async getSubscribedGroups() {
    // Get groups that have daily predictions enabled
    // This will be implemented when group repository is ready
    return [
      // Placeholder for subscribed groups
      // { groupId: -123456789, settings: { predictionTime: '08:00' } }
    ];
  }

  async sendDailyPrediction(group, predictions) {
    // Send formatted daily prediction to group
    const message = this.formatDailyMessage(predictions);

    try {
      await this.botHandler.sendMessage(group.groupId, message, {
        parse_mode: 'Markdown',
        disable_web_page_preview: true
      });

      // Log successful delivery
      console.log(`Daily prediction sent to group ${group.groupId}`);

    } catch (error) {
      console.error(`Failed to send daily prediction to group ${group.groupId}:`, error.message);
      // Continue with other groups even if one fails
    }
  }

  formatDailyMessage(predictions) {
    // Format daily prediction message
    const now = new Date();
    const dateStr = now.toLocaleDateString('vi-VN');

    let message = `🌅 **DỰ ĐOÁN HÀNG NGÀY - ${dateStr}**\n\n`;

    // Lo predictions
    message += `🎯 **DỰ ĐOÁN LÔ:**\n`;
    predictions.lo.predictions.predictions.slice(0, 5).forEach((pred, index) => {
      message += `${index + 1}. **${pred.number}** (${pred.confidence}%)\n`;
    });

    message += `\n🎯 **DỰ ĐOÁN ĐỀ:**\n`;
    predictions.de.predictions.predictions.slice(0, 5).forEach((pred, index) => {
      message += `${index + 1}. **${pred.number}** (${pred.confidence}%)\n`;
    });

    // Add AI analysis if available
    if (predictions.lo.report && predictions.lo.report !== 'LLM response placeholder - integration pending') {
      message += `\n📊 **PHÂN TÍCH AI:**\n${predictions.lo.report}\n`;
    }

    message += `\n⏰ Tạo lúc: ${now.toLocaleTimeString('vi-VN')}`;
    message += `\n🤖 Dự đoán tự động bởi AI`;
    message += `\n⚠️ Chỉ tham khảo - Chơi có trách nhiệm!`;

    return message;
  }

  getJobConfig() {
    return {
      name: 'dailyPrediction',
      schedule: '0 8 * * *', // Daily at 8:00 AM
      handler: () => this.execute(),
      enabled: true,
      description: 'Send daily lottery predictions to subscribed groups'
    };
  }
}

module.exports = DailyPredictionJob;