// Scheduler - Manages cron jobs and automated tasks
// Handles daily predictions, data collection, and notifications

class Scheduler {
  constructor(options = {}) {
    this.options = options;
    this.jobs = new Map();
    this.isRunning = false;
  }

  async initialize() {
    // Initialize scheduler and load jobs
    console.log('Scheduler initialized - cron jobs pending');
  }

  async start() {
    // Start all scheduled jobs
    this.isRunning = true;
    console.log('Scheduler started');

    // Start all registered jobs
    for (const [name, job] of this.jobs) {
      if (job.enabled) {
        await this.startJob(name);
      }
    }
  }

  async stop() {
    // Stop all scheduled jobs
    this.isRunning = false;
    console.log('Scheduler stopped');

    for (const [name, job] of this.jobs) {
      if (job.cronJob) {
        job.cronJob.stop();
      }
    }
  }

  registerJob(name, jobConfig) {
    // Register a new scheduled job
    this.jobs.set(name, {
      name,
      schedule: jobConfig.schedule,
      handler: jobConfig.handler,
      enabled: jobConfig.enabled !== false,
      lastRun: null,
      nextRun: null,
      cronJob: null,
      ...jobConfig
    });
  }

  async startJob(name) {
    // Start specific job
    const job = this.jobs.get(name);
    if (!job) {
      throw new Error(`Job ${name} not found`);
    }

    console.log(`Starting job: ${name} with schedule: ${job.schedule}`);
    // Cron job implementation will be added in later tasks
  }

  async stopJob(name) {
    // Stop specific job
    const job = this.jobs.get(name);
    if (job && job.cronJob) {
      job.cronJob.stop();
      job.cronJob = null;
    }
  }

  async runJobNow(name) {
    // Run job immediately (for testing)
    const job = this.jobs.get(name);
    if (!job) {
      throw new Error(`Job ${name} not found`);
    }

    try {
      console.log(`Running job immediately: ${name}`);
      await job.handler();
      job.lastRun = new Date();
    } catch (error) {
      console.error(`Job ${name} failed:`, error.message);
      throw error;
    }
  }

  getJobStatus(name = null) {
    // Get status of specific job or all jobs
    if (name) {
      const job = this.jobs.get(name);
      return job ? {
        name: job.name,
        schedule: job.schedule,
        enabled: job.enabled,
        lastRun: job.lastRun,
        nextRun: job.nextRun,
        running: !!job.cronJob
      } : null;
    }

    // Return all jobs status
    const status = {};
    for (const [jobName, job] of this.jobs) {
      status[jobName] = {
        schedule: job.schedule,
        enabled: job.enabled,
        lastRun: job.lastRun,
        nextRun: job.nextRun,
        running: !!job.cronJob
      };
    }

    return status;
  }

  enableJob(name) {
    // Enable job
    const job = this.jobs.get(name);
    if (job) {
      job.enabled = true;
      if (this.isRunning) {
        this.startJob(name);
      }
    }
  }

  disableJob(name) {
    // Disable job
    const job = this.jobs.get(name);
    if (job) {
      job.enabled = false;
      this.stopJob(name);
    }
  }

  updateJobSchedule(name, newSchedule) {
    // Update job schedule
    const job = this.jobs.get(name);
    if (job) {
      job.schedule = newSchedule;
      if (job.cronJob) {
        this.stopJob(name);
        this.startJob(name);
      }
    }
  }

  getSchedulerStats() {
    return {
      isRunning: this.isRunning,
      totalJobs: this.jobs.size,
      enabledJobs: Array.from(this.jobs.values()).filter(job => job.enabled).length,
      runningJobs: Array.from(this.jobs.values()).filter(job => job.cronJob).length
    };
  }
}

module.exports = Scheduler;