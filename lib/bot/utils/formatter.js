const logger = require('../../logger')('logs');

// Message formatting utilities for Telegram bot
// Handles text formatting, escaping, and message structure

class MessageFormatter {
  static escapeHtml(text) {
    if (!text) return '';
    return text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;');
  }

  static bold(text) {
    return `<b>${this.escapeHtml(text)}</b>`;
  }

  static italic(text) {
    return `<i>${this.escapeHtml(text)}</i>`;
  }

  static code(text) {
    return `<code>${this.escapeHtml(text)}</code>`;
  }

  static pre(text) {
    return `<pre>${this.escapeHtml(text)}</pre>`;
  }

  static link(text, url) {
    return `<a href="${url}">${this.escapeHtml(text)}</a>`;
  }

  static formatNumber(number) {
    return this.code(String(number).padStart(2, '0'));
  }

  static formatConfidence(confidence) {
    const percentage = Math.round(confidence);
    let emoji = '🔴'; // Low confidence

    if (percentage >= 70) emoji = '🟢'; // High confidence
    else if (percentage >= 50) emoji = '🟡'; // Medium confidence

    return `${emoji} ${percentage}%`;
  }

  static formatPrediction(prediction) {
    try {
      if (!prediction || !prediction.predictions) {
        return 'Không có dự đoán khả dụng.';
      }

      const { type, predictions, date, modelVersion } = prediction;
      const typeText = type === 'lo' ? 'Số Lô' : 'Số Đề';

      let message = `🎯 ${this.bold(`Dự Đoán ${typeText}`)}\n`;
      message += `📅 Ngày: ${this.code(new Date(date).toLocaleDateString('vi-VN'))}\n\n`;

      if (predictions.length === 0) {
        message += 'Không có dự đoán cho hôm nay.';
        return message;
      }

      message += `${this.bold('Top Dự Đoán:')}\n`;

      predictions.slice(0, 10).forEach((pred, index) => {
        const rank = index + 1;
        const number = this.formatNumber(pred.number);
        const confidence = this.formatConfidence(pred.confidence);

        message += `${rank}. ${number} - ${confidence}\n`;
      });

      if (modelVersion) {
        message += `\n🤖 Model: ${this.code(modelVersion)}`;
      }

      message += '\n\n💡 Lưu ý: Đây chỉ là dự đoán dựa trên phân tích dữ liệu lịch sử.';

      return message;
    } catch (error) {
      logger.logError('Error formatting prediction:', error);
      return 'Lỗi khi định dạng dự đoán.';
    }
  }

  static formatHistory(history, limit = 5) {
    try {
      if (!history || !Array.isArray(history) || history.length === 0) {
        return 'Không có dữ liệu lịch sử.';
      }

      let message = `📊 ${this.bold('Lịch Sử Kết Quả')}\n\n`;

      history.slice(0, limit).forEach((result, index) => {
        const date = new Date(result.date).toLocaleDateString('vi-VN');
        message += `📅 ${this.bold(date)}\n`;

        if (result.prizes?.special) {
          message += `🏆 Đặc biệt: ${this.formatNumber(result.prizes.special)}\n`;
        }

        if (result.numbers?.lo && result.numbers.lo.length > 0) {
          const loNumbers = result.numbers.lo.slice(0, 10)
            .map(num => this.formatNumber(num))
            .join(' ');
          message += `🎲 Lô: ${loNumbers}\n`;
        }

        if (result.numbers?.de && result.numbers.de.length > 0) {
          const deNumbers = result.numbers.de.slice(0, 5)
            .map(num => this.formatNumber(num))
            .join(' ');
          message += `🎯 Đề: ${deNumbers}\n`;
        }

        if (index < history.length - 1 && index < limit - 1) {
          message += '\n';
        }
      });

      if (history.length > limit) {
        message += `\n... và ${history.length - limit} kết quả khác`;
      }

      return message;
    } catch (error) {
      logger.logError('Error formatting history:', error);
      return 'Lỗi khi định dạng lịch sử.';
    }
  }

  static formatTrends(trends) {
    try {
      if (!trends) {
        return 'Không có dữ liệu xu hướng.';
      }

      const { type, hot, cold, period } = trends;
      const typeText = type === 'lo' ? 'Số Lô' : 'Số Đề';

      let message = `📈 ${this.bold(`Xu Hướng ${typeText}`)}\n`;
      message += `⏰ Thời gian: ${period || 'Tuần qua'}\n\n`;

      if (hot && hot.length > 0) {
        message += `🔥 ${this.bold('Số Nóng (xuất hiện nhiều):')}\n`;
        hot.slice(0, 10).forEach((item, index) => {
          const number = this.formatNumber(item.number);
          const count = item.count || item.frequency;
          message += `${index + 1}. ${number} (${count} lần)\n`;
        });
        message += '\n';
      }

      if (cold && cold.length > 0) {
        message += `❄️ ${this.bold('Số Lạnh (ít xuất hiện):')}\n`;
        cold.slice(0, 10).forEach((item, index) => {
          const number = this.formatNumber(item.number);
          const count = item.count || item.frequency;
          message += `${index + 1}. ${number} (${count} lần)\n`;
        });
      }

      message += '\n💡 Xu hướng dựa trên dữ liệu lịch sử và có thể thay đổi.';

      return message;
    } catch (error) {
      logger.logError('Error formatting trends:', error);
      return 'Lỗi khi định dạng xu hướng.';
    }
  }

  static formatNumberAnalysis(analysis) {
    try {
      if (!analysis) {
        return 'Không có dữ liệu phân tích.';
      }

      const { number, statistics, trends, predictions } = analysis;

      let message = `🔍 ${this.bold(`Phân Tích Số ${this.formatNumber(number)}`)}\n\n`;

      if (statistics) {
        message += `📊 ${this.bold('Thống Kê:')}\n`;
        if (statistics.totalAppearances !== undefined) {
          message += `• Tổng lần xuất hiện: ${this.code(statistics.totalAppearances)}\n`;
        }
        if (statistics.lastAppearance) {
          const lastDate = new Date(statistics.lastAppearance).toLocaleDateString('vi-VN');
          message += `• Lần cuối xuất hiện: ${this.code(lastDate)}\n`;
        }
        if (statistics.frequency !== undefined) {
          message += `• Tần suất: ${this.code(statistics.frequency.toFixed(2))}%\n`;
        }
        message += '\n';
      }

      if (trends) {
        message += `📈 ${this.bold('Xu Hướng:')}\n`;
        if (trends.isHot !== undefined) {
          const status = trends.isHot ? '🔥 Nóng' : '❄️ Lạnh';
          message += `• Trạng thái: ${status}\n`;
        }
        if (trends.streak !== undefined) {
          message += `• Chuỗi: ${this.code(trends.streak)} ngày\n`;
        }
        message += '\n';
      }

      if (predictions) {
        message += `🎯 ${this.bold('Dự Đoán:')}\n`;
        if (predictions.nextAppearance !== undefined) {
          const confidence = this.formatConfidence(predictions.nextAppearance);
          message += `• Khả năng xuất hiện: ${confidence}\n`;
        }
      }

      return message;
    } catch (error) {
      logger.logError('Error formatting number analysis:', error);
      return 'Lỗi khi định dạng phân tích số.';
    }
  }

  static formatError(error, context = '') {
    const contextText = context ? ` (${context})` : '';
    return `❌ Đã xảy ra lỗi${contextText}. Vui lòng thử lại sau.`;
  }

  static formatHelp(commands) {
    try {
      let message = `🤖 ${this.bold('Hướng Dẫn Sử Dụng Bot')}\n\n`;
      message += `${this.bold('Lệnh Dự Đoán:')}\n`;
      message += `• /dukienlo - Dự đoán số lô\n`;
      message += `• /dukiende - Dự đoán số đề\n\n`;

      message += `${this.bold('Lệnh Lịch Sử:')}\n`;
      message += `• /lichsu - Xem kết quả gần đây\n\n`;

      message += `${this.bold('Lệnh Xu Hướng:')}\n`;
      message += `• /xuhuonglo - Xu hướng số lô\n`;
      message += `• /xuhuongde - Xu hướng số đề\n\n`;

      message += `${this.bold('Lệnh Phân Tích:')}\n`;
      message += `• /number [số] - Phân tích số cụ thể\n`;
      message += `  Ví dụ: /number 25\n\n`;

      message += `${this.bold('Lệnh Khác:')}\n`;
      message += `• /help - Hiển thị hướng dẫn này\n\n`;

      message += `💡 ${this.italic('Lưu ý: Tất cả dự đoán chỉ mang tính chất tham khảo.')}`;

      return message;
    } catch (error) {
      logger.logError('Error formatting help:', error);
      return 'Lỗi khi tạo hướng dẫn.';
    }
  }

  static truncateMessage(message, maxLength = 4096) {
    if (!message || message.length <= maxLength) {
      return message;
    }

    return message.substring(0, maxLength - 3) + '...';
  }

  static splitLongMessage(message, maxLength = 4096) {
    if (!message || message.length <= maxLength) {
      return [message];
    }

    const parts = [];
    let currentPart = '';
    const lines = message.split('\n');

    for (const line of lines) {
      if ((currentPart + line + '\n').length > maxLength) {
        if (currentPart) {
          parts.push(currentPart.trim());
          currentPart = '';
        }

        // If single line is too long, split it
        if (line.length > maxLength) {
          let remainingLine = line;
          while (remainingLine.length > maxLength) {
            parts.push(remainingLine.substring(0, maxLength));
            remainingLine = remainingLine.substring(maxLength);
          }
          currentPart = remainingLine + '\n';
        } else {
          currentPart = line + '\n';
        }
      } else {
        currentPart += line + '\n';
      }
    }

    if (currentPart.trim()) {
      parts.push(currentPart.trim());
    }

    return parts;
  }
}

module.exports = MessageFormatter;