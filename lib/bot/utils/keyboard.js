// Inline keyboard utilities for Telegram bot
// Creates interactive keyboards for bot responses

class KeyboardBuilder {
  constructor() {
    this.keyboard = [];
    this.currentRow = [];
  }

  addButton(text, callbackData) {
    if (!text || !callbackData) {
      throw new Error('Button text and callback data are required');
    }

    this.currentRow.push({
      text: String(text),
      callback_data: String(callbackData)
    });
    return this;
  }

  addUrlButton(text, url) {
    if (!text || !url) {
      throw new Error('Button text and URL are required');
    }

    this.currentRow.push({
      text: String(text),
      url: String(url)
    });
    return this;
  }

  addSwitchInlineButton(text, query = '') {
    if (!text) {
      throw new Error('Button text is required');
    }

    this.currentRow.push({
      text: String(text),
      switch_inline_query: String(query)
    });
    return this;
  }

  newRow() {
    if (this.currentRow.length > 0) {
      this.keyboard.push([...this.currentRow]);
      this.currentRow = [];
    }
    return this;
  }

  build() {
    this.newRow(); // Add any remaining buttons
    return {
      reply_markup: {
        inline_keyboard: this.keyboard
      }
    };
  }

  static createPaginationKeyboard(currentPage, totalPages, prefix = 'page') {
    if (currentPage < 1 || totalPages < 1 || currentPage > totalPages) {
      throw new Error('Invalid pagination parameters');
    }

    const builder = new KeyboardBuilder();

    // Previous button
    if (currentPage > 1) {
      builder.addButton('⬅️ Trước', `${prefix}_${currentPage - 1}`);
    }

    // Current page indicator
    builder.addButton(`${currentPage}/${totalPages}`, 'noop');

    // Next button
    if (currentPage < totalPages) {
      builder.addButton('Sau ➡️', `${prefix}_${currentPage + 1}`);
    }

    return builder.build();
  }

  static createNumberKeyboard(startNum = 0, endNum = 99, buttonsPerRow = 5) {
    const builder = new KeyboardBuilder();

    for (let i = startNum; i <= endNum; i++) {
      const num = String(i).padStart(2, '0');
      builder.addButton(num, `number_${num}`);

      // Start new row after specified number of buttons
      if ((i - startNum + 1) % buttonsPerRow === 0) {
        builder.newRow();
      }
    }

    return builder.build();
  }

  static createQuickActionsKeyboard() {
    const builder = new KeyboardBuilder();

    builder
      .addButton('🎯 Dự đoán Lô', 'cmd_dukienlo')
      .addButton('🎲 Dự đoán Đề', 'cmd_dukiende')
      .newRow()
      .addButton('📊 Lịch sử', 'cmd_lichsu')
      .addButton('📈 Xu hướng', 'cmd_trends')
      .newRow()
      .addButton('❓ Trợ giúp', 'cmd_help');

    return builder.build();
  }

  static createTrendTypeKeyboard() {
    const builder = new KeyboardBuilder();

    builder
      .addButton('📈 Xu hướng Lô', 'trend_lo')
      .addButton('📉 Xu hướng Đề', 'trend_de')
      .newRow()
      .addButton('🔙 Quay lại', 'back_main');

    return builder.build();
  }

  static createHistoryPeriodKeyboard() {
    const builder = new KeyboardBuilder();

    builder
      .addButton('📅 Hôm nay', 'history_today')
      .addButton('📅 Tuần này', 'history_week')
      .newRow()
      .addButton('📅 Tháng này', 'history_month')
      .addButton('📅 Tùy chỉnh', 'history_custom')
      .newRow()
      .addButton('🔙 Quay lại', 'back_main');

    return builder.build();
  }

  static createConfirmationKeyboard(action, data = '') {
    const builder = new KeyboardBuilder();

    builder
      .addButton('✅ Xác nhận', `confirm_${action}_${data}`)
      .addButton('❌ Hủy bỏ', `cancel_${action}`)
      .newRow()
      .addButton('🔙 Quay lại', 'back_main');

    return builder.build();
  }

  static createSettingsKeyboard() {
    const builder = new KeyboardBuilder();

    builder
      .addButton('🔔 Thông báo', 'settings_notifications')
      .addButton('🌐 Ngôn ngữ', 'settings_language')
      .newRow()
      .addButton('⏰ Lịch trình', 'settings_schedule')
      .addButton('📊 Thống kê', 'settings_stats')
      .newRow()
      .addButton('🔙 Quay lại', 'back_main');

    return builder.build();
  }

  static createBackButton(action = 'main') {
    const builder = new KeyboardBuilder();
    builder.addButton('🔙 Quay lại', `back_${action}`);
    return builder.build();
  }

  static createEmptyKeyboard() {
    return {
      reply_markup: {
        inline_keyboard: []
      }
    };
  }

  // Helper method to create a simple yes/no keyboard
  static createYesNoKeyboard(yesAction, noAction) {
    const builder = new KeyboardBuilder();

    builder
      .addButton('✅ Có', yesAction)
      .addButton('❌ Không', noAction);

    return builder.build();
  }

  // Helper method to create a keyboard from an array of options
  static createOptionsKeyboard(options, prefix = 'option', buttonsPerRow = 2) {
    const builder = new KeyboardBuilder();

    options.forEach((option, index) => {
      const callbackData = `${prefix}_${index}`;
      builder.addButton(option.text || option, callbackData);

      if ((index + 1) % buttonsPerRow === 0) {
        builder.newRow();
      }
    });

    return builder.build();
  }

  // Legacy static methods for backward compatibility
  static createMainMenu() {
    return {
      reply_markup: {
        inline_keyboard: [
          [
            { text: '🎯 Dự đoán Lô', callback_data: 'predict_lo' },
            { text: '🎯 Dự đoán Đề', callback_data: 'predict_de' }
          ],
          [
            { text: '📊 Xu hướng Lô', callback_data: 'trends_lo' },
            { text: '📊 Xu hướng Đề', callback_data: 'trends_de' }
          ],
          [
            { text: '📋 Lịch sử', callback_data: 'history' },
            { text: '❓ Trợ giúp', callback_data: 'help' }
          ]
        ]
      }
    };
  }

  static createPredictionOptions() {
    return {
      reply_markup: {
        inline_keyboard: [
          [
            { text: '🔥 Số nóng', callback_data: 'hot_numbers' },
            { text: '❄️ Số lạnh', callback_data: 'cold_numbers' }
          ],
          [
            { text: '📈 Phân tích AI', callback_data: 'ai_analysis' },
            { text: '📊 Thống kê', callback_data: 'statistics' }
          ],
          [
            { text: '🔙 Quay lại', callback_data: 'main_menu' }
          ]
        ]
      }
    };
  }

  static createNumberAnalysisKeyboard(number) {
    return {
      reply_markup: {
        inline_keyboard: [
          [
            { text: '📊 Tần suất', callback_data: `freq_${number}` },
            { text: '📈 Xu hướng', callback_data: `trend_${number}` }
          ],
          [
            { text: '🔍 Chi tiết', callback_data: `detail_${number}` },
            { text: '🔙 Quay lại', callback_data: 'main_menu' }
          ]
        ]
      }
    };
  }
}

module.exports = KeyboardBuilder;