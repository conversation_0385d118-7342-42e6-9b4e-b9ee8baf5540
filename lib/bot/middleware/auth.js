const logger = require('../../logger')('logs');

// Authentication middleware for bot commands
// Handles user verification and permission checks
async function authMiddleware(ctx) {
  try {
    // Skip auth for non-command messages
    if (!ctx.isCommand) {
      return;
    }

    // Basic user validation
    if (!ctx.userId) {
      logger.logError('Message received without user ID');
      return;
    }

    // Add user info to context
    ctx.user = {
      id: ctx.userId,
      username: ctx.message.from?.username,
      firstName: ctx.message.from?.first_name,
      lastName: ctx.message.from?.last_name,
      isBot: ctx.message.from?.is_bot || false
    };

    // Block bot users
    if (ctx.user.isBot) {
      logger.logError(`Bot user attempted to use command: ${ctx.user.id}`);
      throw new Error('Bot users are not allowed');
    }

    // Add chat info to context
    ctx.chat = {
      id: ctx.chatId,
      type: ctx.message.chat?.type,
      title: ctx.message.chat?.title
    };

    // Log user activity
    logger.logInfo(`User ${ctx.user.id} (${ctx.user.username || 'no username'}) used command /${ctx.command} in chat ${ctx.chatId}`);

  } catch (error) {
    logger.logError('Auth middleware error:', error);
    throw error;
  }
}

module.exports = authMiddleware;