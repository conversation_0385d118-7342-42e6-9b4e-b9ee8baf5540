const logger = require('../../logger')('logs');

// Logging middleware for bot interactions
// Records user activities and system events
async function loggingMiddleware(ctx) {
  try {
    const timestamp = new Date().toISOString();

    if (ctx.message) {
      // Log message details
      const logData = {
        timestamp,
        type: 'message',
        userId: ctx.userId,
        chatId: ctx.chatId,
        chatType: ctx.message.chat?.type,
        username: ctx.message.from?.username,
        isCommand: ctx.isCommand,
        command: ctx.command,
        messageLength: ctx.text?.length || 0
      };

      // Don't log sensitive message content, just metadata
      logger.logInfo('Bot interaction:', logData);

      // Log command usage specifically
      if (ctx.isCommand && ctx.command) {
        logger.logInfo(`Command usage: /${ctx.command} by user ${ctx.userId} in chat ${ctx.chatId}`);
      }
    }

    if (ctx.callbackQuery) {
      // Log callback query details
      const logData = {
        timestamp,
        type: 'callback_query',
        userId: ctx.userId,
        chatId: ctx.chatId,
        data: ctx.data
      };

      logger.logInfo('Callback query:', logData);
    }

  } catch (error) {
    // Don't throw errors from logging middleware
    logger.logError('Logging middleware error:', error);
  }
}

module.exports = loggingMiddleware;