const logger = require('../../logger')('logs');

// Rate limiting middleware to prevent spam
// Tracks user request frequency and blocks excessive usage
class RateLimiter {
  constructor() {
    this.userRequests = new Map();
    this.windowMs = 60 * 1000; // 1 minute window
    this.maxRequests = 20; // Max requests per window
    this.blockDuration = 5 * 60 * 1000; // 5 minutes block

    // Clean up old entries every 5 minutes
    setInterval(() => this.cleanup(), 5 * 60 * 1000);
  }

  isRateLimited(userId) {
    const now = Date.now();
    const userRecord = this.userRequests.get(userId);

    if (!userRecord) {
      // First request from this user
      this.userRequests.set(userId, {
        requests: [now],
        blockedUntil: null
      });
      return false;
    }

    // Check if user is currently blocked
    if (userRecord.blockedUntil && now < userRecord.blockedUntil) {
      return true;
    }

    // Remove old requests outside the window
    userRecord.requests = userRecord.requests.filter(
      timestamp => now - timestamp < this.windowMs
    );

    // Add current request
    userRecord.requests.push(now);

    // Check if user exceeded rate limit
    if (userRecord.requests.length > this.maxRequests) {
      userRecord.blockedUntil = now + this.blockDuration;
      logger.logError(`User ${userId} rate limited for ${this.blockDuration / 1000} seconds`);
      return true;
    }

    return false;
  }

  cleanup() {
    const now = Date.now();
    const cutoff = now - this.windowMs;

    for (const [userId, record] of this.userRequests.entries()) {
      // Remove expired blocks and old requests
      if (record.blockedUntil && now > record.blockedUntil) {
        record.blockedUntil = null;
      }

      record.requests = record.requests.filter(timestamp => timestamp > cutoff);

      // Remove users with no recent activity
      if (record.requests.length === 0 && !record.blockedUntil) {
        this.userRequests.delete(userId);
      }
    }
  }

  getRemainingTime(userId) {
    const userRecord = this.userRequests.get(userId);
    if (!userRecord || !userRecord.blockedUntil) {
      return 0;
    }

    const remaining = userRecord.blockedUntil - Date.now();
    return Math.max(0, Math.ceil(remaining / 1000));
  }
}

const rateLimiter = new RateLimiter();

async function rateLimitMiddleware(ctx) {
  try {
    // Skip rate limiting for non-command messages
    if (!ctx.isCommand || !ctx.userId) {
      return;
    }

    if (rateLimiter.isRateLimited(ctx.userId)) {
      const remainingTime = rateLimiter.getRemainingTime(ctx.userId);

      const message = remainingTime > 0
        ? `Bạn đang gửi lệnh quá nhanh. Vui lòng chờ ${remainingTime} giây trước khi thử lại.`
        : 'Bạn đang gửi lệnh quá nhanh. Vui lòng chờ một chút.';

      await ctx.bot.sendMessage(ctx.chatId, message);
      throw new Error('Rate limit exceeded');
    }

  } catch (error) {
    if (error.message === 'Rate limit exceeded') {
      throw error;
    }
    logger.logError('Rate limit middleware error:', error);
    throw error;
  }
}

module.exports = rateLimitMiddleware;