// History command handler
// Handles /lichsu command

const MessageFormatter = require('../utils/formatter');
const logger = require('../../logger')('logs');

class HistoryHandler {
  constructor(dataManager) {
    this.dataManager = dataManager;
    this.defaultLimit = 5;
    this.maxLimit = 20;
  }

  async handleLichSu(ctx) {
    try {
      // Parse command parameters
      const params = this._parseHistoryParams(ctx.message.text);
      const { limit, page, region } = params;

      // Get historical data
      const history = await this.dataManager.getLotteryHistory({
        region,
        limit: limit * (page + 1), // Get more data for pagination
        sortBy: 'date',
        sortOrder: 'desc'
      });

      if (!history || history.length === 0) {
        await ctx.reply('Không có dữ liệu lịch sử xổ số.');
        return;
      }

      // Apply pagination
      const startIndex = page * limit;
      const endIndex = startIndex + limit;
      const paginatedHistory = history.slice(startIndex, endIndex);

      // Format the response
      const formattedMessage = MessageFormatter.formatHistory(paginatedHistory, limit);

      // Add pagination info if needed
      let finalMessage = formattedMessage;
      if (history.length > limit) {
        const totalPages = Math.ceil(history.length / limit);
        const currentPage = page + 1;
        finalMessage += `\n\n📄 Trang ${currentPage}/${totalPages}`;

        if (currentPage < totalPages) {
          finalMessage += `\n💡 Dùng /lichsu ${limit} ${currentPage + 1} để xem trang tiếp theo`;
        }
      }

      // Create inline keyboard for navigation
      const keyboard = this._createHistoryKeyboard(page, Math.ceil(history.length / limit), limit);

      await ctx.reply(finalMessage, {
        parse_mode: 'HTML',
        reply_markup: keyboard
      });

      // Log the interaction
      logger.logInfo('History command executed', {
        userId: ctx.from.id,
        chatId: ctx.chat.id,
        limit,
        page,
        region,
        resultsCount: paginatedHistory.length
      });

    } catch (error) {
      logger.logError('History command failed:', error);
      const errorMessage = MessageFormatter.formatError(error, 'lịch sử');
      await ctx.reply(errorMessage);
    }
  }

  _parseHistoryParams(text) {
    // Parse command: /lichsu [limit] [page] [region]
    // Examples: /lichsu, /lichsu 10, /lichsu 5 2, /lichsu 10 1 north
    const parts = text.trim().split(/\s+/);

    let limit = this.defaultLimit;
    let page = 0; // 0-based indexing
    let region = 'north';

    if (parts.length > 1) {
      const limitParam = parseInt(parts[1]);
      if (!isNaN(limitParam) && limitParam > 0) {
        limit = Math.min(limitParam, this.maxLimit);
      }
    }

    if (parts.length > 2) {
      const pageParam = parseInt(parts[2]);
      if (!isNaN(pageParam) && pageParam > 0) {
        page = pageParam - 1; // Convert to 0-based
      }
    }

    if (parts.length > 3) {
      const regionParam = parts[3].toLowerCase();
      if (['north', 'central', 'south'].includes(regionParam)) {
        region = regionParam;
      }
    }

    return { limit, page, region };
  }

  _createHistoryKeyboard(currentPage, totalPages, limit) {
    const keyboard = [];
    const row = [];

    // Previous page button
    if (currentPage > 0) {
      row.push({
        text: '⬅️ Trang trước',
        callback_data: `history:${limit}:${currentPage - 1}`
      });
    }

    // Page indicator
    if (totalPages > 1) {
      row.push({
        text: `${currentPage + 1}/${totalPages}`,
        callback_data: 'noop'
      });
    }

    // Next page button
    if (currentPage < totalPages - 1) {
      row.push({
        text: 'Trang sau ➡️',
        callback_data: `history:${limit}:${currentPage + 1}`
      });
    }

    if (row.length > 0) {
      keyboard.push(row);
    }

    // Quick access buttons
    const quickRow = [];
    quickRow.push({
      text: '🔄 Làm mới',
      callback_data: `history:${limit}:0`
    });

    if (limit !== 10) {
      quickRow.push({
        text: '📊 10 kết quả',
        callback_data: 'history:10:0'
      });
    }

    if (quickRow.length > 0) {
      keyboard.push(quickRow);
    }

    return keyboard.length > 0 ? { inline_keyboard: keyboard } : undefined;
  }

  async handleHistoryCallback(ctx) {
    try {
      const [, limit, page] = ctx.callbackQuery.data.split(':');
      const limitNum = parseInt(limit) || this.defaultLimit;
      const pageNum = parseInt(page) || 0;

      // Get historical data
      const history = await this.dataManager.getLotteryHistory({
        region: 'north',
        limit: limitNum * (pageNum + 2), // Get extra data for pagination
        sortBy: 'date',
        sortOrder: 'desc'
      });

      if (!history || history.length === 0) {
        await ctx.answerCbQuery('Không có dữ liệu lịch sử.');
        return;
      }

      // Apply pagination
      const startIndex = pageNum * limitNum;
      const endIndex = startIndex + limitNum;
      const paginatedHistory = history.slice(startIndex, endIndex);

      // Format the response
      const formattedMessage = MessageFormatter.formatHistory(paginatedHistory, limitNum);

      // Add pagination info
      let finalMessage = formattedMessage;
      if (history.length > limitNum) {
        const totalPages = Math.ceil(history.length / limitNum);
        const currentPage = pageNum + 1;
        finalMessage += `\n\n📄 Trang ${currentPage}/${totalPages}`;
      }

      // Create updated keyboard
      const keyboard = this._createHistoryKeyboard(pageNum, Math.ceil(history.length / limitNum), limitNum);

      await ctx.editMessageText(finalMessage, {
        parse_mode: 'HTML',
        reply_markup: keyboard
      });

      await ctx.answerCbQuery();

    } catch (error) {
      logger.logError('History callback failed:', error);
      await ctx.answerCbQuery('Lỗi khi tải dữ liệu lịch sử.');
    }
  }

  // Method to get detailed history for a specific date
  async getHistoryByDate(date, region = 'north') {
    try {
      const result = await this.dataManager.getLotteryResultByDate(date, region);

      if (!result) {
        return null;
      }

      return {
        date: result.date,
        region: result.region,
        prizes: result.prizes,
        numbers: result.numbers,
        formatted: MessageFormatter.formatHistory([result], 1)
      };

    } catch (error) {
      logger.logError('Get history by date failed:', error);
      throw error;
    }
  }

  // Method to get history statistics
  async getHistoryStats(days = 30, region = 'north') {
    try {
      const fromDate = new Date();
      fromDate.setDate(fromDate.getDate() - days);

      const history = await this.dataManager.getLotteryHistory({
        region,
        fromDate,
        toDate: new Date()
      });

      if (!history || history.length === 0) {
        return {
          totalResults: 0,
          period: days,
          region
        };
      }

      // Calculate statistics
      const stats = {
        totalResults: history.length,
        period: days,
        region,
        dateRange: {
          from: history[history.length - 1].date,
          to: history[0].date
        },
        numberFrequency: this._calculateNumberFrequency(history),
        prizeDistribution: this._calculatePrizeDistribution(history)
      };

      return stats;

    } catch (error) {
      logger.logError('Get history stats failed:', error);
      throw error;
    }
  }

  _calculateNumberFrequency(history) {
    const loFreq = new Map();
    const deFreq = new Map();

    history.forEach(result => {
      if (result.numbers?.lo) {
        result.numbers.lo.forEach(num => {
          loFreq.set(num, (loFreq.get(num) || 0) + 1);
        });
      }

      if (result.numbers?.de) {
        result.numbers.de.forEach(num => {
          deFreq.set(num, (deFreq.get(num) || 0) + 1);
        });
      }
    });

    return {
      lo: Array.from(loFreq.entries())
        .sort((a, b) => b[1] - a[1])
        .slice(0, 10)
        .map(([number, count]) => ({ number, count })),
      de: Array.from(deFreq.entries())
        .sort((a, b) => b[1] - a[1])
        .slice(0, 10)
        .map(([number, count]) => ({ number, count }))
    };
  }

  _calculatePrizeDistribution(history) {
    const distribution = {
      special: new Map(),
      first: new Map(),
      second: new Map(),
      third: new Map()
    };

    history.forEach(result => {
      if (result.prizes?.special) {
        const lastTwo = result.prizes.special.slice(-2);
        distribution.special.set(lastTwo, (distribution.special.get(lastTwo) || 0) + 1);
      }

      if (result.prizes?.first) {
        const lastTwo = result.prizes.first.slice(-2);
        distribution.first.set(lastTwo, (distribution.first.get(lastTwo) || 0) + 1);
      }

      // Add more prize analysis as needed
    });

    return {
      special: Array.from(distribution.special.entries())
        .sort((a, b) => b[1] - a[1])
        .slice(0, 5),
      first: Array.from(distribution.first.entries())
        .sort((a, b) => b[1] - a[1])
        .slice(0, 5)
    };
  }
}

module.exports = HistoryHandler;