// Trends command handlers
// Handles /xuhuonglo and /xuhuong<PERSON> commands

const MessageFormatter = require('../utils/formatter');
const logger = require('../../logger')('logs');

class TrendsHandler {
  constructor(predictionEngine, dataManager) {
    this.predictionEngine = predictionEngine;
    this.dataManager = dataManager;
    this.defaultPeriod = 'weekly';
    this.supportedPeriods = ['daily', 'weekly', 'monthly'];
  }

  async handleXuHuongLo(ctx) {
    try {
      // Parse command parameters
      const params = this._parseTrendParams(ctx.message.text);
      const { period, limit } = params;

      // Get trend data for 'lo' numbers
      const trends = await this.predictionEngine.getTrends('lo', period);

      if (!trends || (!trends.hot?.length && !trends.cold?.length)) {
        await ctx.reply('Không có dữ liệu xu hướng số lô.');
        return;
      }

      // Enhance trends with additional analysis
      const enhancedTrends = await this._enhanceTrendsData(trends, 'lo', period);

      // Format the response
      const formattedMessage = MessageFormatter.formatTrends({
        ...enhancedTrends,
        type: 'lo',
        period: this._getPeriodDisplayName(period)
      });

      // Create inline keyboard for different periods
      const keyboard = this._createTrendsKeyboard('lo', period);

      await ctx.reply(formattedMessage, {
        parse_mode: 'HTML',
        reply_markup: keyboard
      });

      // Log the interaction
      logger.logInfo('Lo trends command executed', {
        userId: ctx.from.id,
        chatId: ctx.chat.id,
        period,
        hotCount: trends.hot?.length || 0,
        coldCount: trends.cold?.length || 0
      });

    } catch (error) {
      logger.logError('Lo trends command failed:', error);
      const errorMessage = MessageFormatter.formatError(error, 'xu hướng số lô');
      await ctx.reply(errorMessage);
    }
  }

  async handleXuHuongDe(ctx) {
    try {
      // Parse command parameters
      const params = this._parseTrendParams(ctx.message.text);
      const { period, limit } = params;

      // Get trend data for 'de' numbers
      const trends = await this.predictionEngine.getTrends('de', period);

      if (!trends || (!trends.hot?.length && !trends.cold?.length)) {
        await ctx.reply('Không có dữ liệu xu hướng số đề.');
        return;
      }

      // Enhance trends with additional analysis
      const enhancedTrends = await this._enhanceTrendsData(trends, 'de', period);

      // Format the response
      const formattedMessage = MessageFormatter.formatTrends({
        ...enhancedTrends,
        type: 'de',
        period: this._getPeriodDisplayName(period)
      });

      // Create inline keyboard for different periods
      const keyboard = this._createTrendsKeyboard('de', period);

      await ctx.reply(formattedMessage, {
        parse_mode: 'HTML',
        reply_markup: keyboard
      });

      // Log the interaction
      logger.logInfo('De trends command executed', {
        userId: ctx.from.id,
        chatId: ctx.chat.id,
        period,
        hotCount: trends.hot?.length || 0,
        coldCount: trends.cold?.length || 0
      });

    } catch (error) {
      logger.logError('De trends command failed:', error);
      const errorMessage = MessageFormatter.formatError(error, 'xu hướng số đề');
      await ctx.reply(errorMessage);
    }
  }

  _parseTrendParams(text) {
    // Parse command: /xuhuonglo [period] [limit]
    // Examples: /xuhuonglo, /xuhuonglo weekly, /xuhuonglo monthly 15
    const parts = text.trim().split(/\s+/);

    let period = this.defaultPeriod;
    let limit = 10;

    if (parts.length > 1) {
      const periodParam = parts[1].toLowerCase();
      if (this.supportedPeriods.includes(periodParam)) {
        period = periodParam;
      }
    }

    if (parts.length > 2) {
      const limitParam = parseInt(parts[2]);
      if (!isNaN(limitParam) && limitParam > 0) {
        limit = Math.min(limitParam, 20); // Max 20 numbers
      }
    }

    return { period, limit };
  }

  async _enhanceTrendsData(trends, type, period) {
    try {
      // Get additional statistics from data manager
      const days = this._getPeriodDays(period);
      const stats = await this.dataManager.getNumberStatistics(type, days);

      // Enhance hot numbers with additional data
      const enhancedHot = trends.hot?.map(item => {
        const stat = stats?.find(s => s.number === item.number);
        return {
          ...item,
          frequency: item.frequency || item.count,
          percentage: stat ? ((stat.count / stats.length) * 100).toFixed(1) : '0.0',
          lastSeen: stat?.lastSeen,
          streak: this._calculateStreak(item.number, type)
        };
      }) || [];

      // Enhance cold numbers with additional data
      const enhancedCold = trends.cold?.map(item => {
        const stat = stats?.find(s => s.number === item.number);
        return {
          ...item,
          frequency: item.frequency || item.count,
          percentage: stat ? ((stat.count / stats.length) * 100).toFixed(1) : '0.0',
          lastSeen: stat?.lastSeen,
          daysSinceLastSeen: stat?.lastSeen ?
            Math.floor((Date.now() - new Date(stat.lastSeen)) / (1000 * 60 * 60 * 24)) : null
        };
      }) || [];

      return {
        hot: enhancedHot,
        cold: enhancedCold,
        period,
        type,
        totalNumbers: (enhancedHot.length + enhancedCold.length),
        analysisDate: new Date()
      };

    } catch (error) {
      logger.logError('Failed to enhance trends data:', error);
      // Return original trends if enhancement fails
      return trends;
    }
  }

  _getPeriodDays(period) {
    switch (period) {
      case 'daily': return 1;
      case 'weekly': return 7;
      case 'monthly': return 30;
      default: return 7;
    }
  }

  _getPeriodDisplayName(period) {
    switch (period) {
      case 'daily': return 'Hôm nay';
      case 'weekly': return 'Tuần qua';
      case 'monthly': return 'Tháng qua';
      default: return 'Tuần qua';
    }
  }

  async _calculateStreak(number, type) {
    try {
      // Get recent results to calculate streak
      const recentResults = await this.dataManager.getLotteryHistory({
        limit: 30,
        sortBy: 'date',
        sortOrder: 'desc'
      });

      if (!recentResults || recentResults.length === 0) {
        return 0;
      }

      let streak = 0;
      let found = false;

      for (const result of recentResults) {
        const numbers = type === 'lo' ? result.numbers?.lo : result.numbers?.de;

        if (numbers && numbers.includes(number)) {
          if (!found) {
            found = true;
          }
          streak++;
        } else if (found) {
          // Streak broken
          break;
        }
      }

      return streak;

    } catch (error) {
      logger.logError('Failed to calculate streak:', error);
      return 0;
    }
  }

  _createTrendsKeyboard(type, currentPeriod) {
    const keyboard = [];

    // Period selection row
    const periodRow = [];
    this.supportedPeriods.forEach(period => {
      if (period !== currentPeriod) {
        periodRow.push({
          text: this._getPeriodDisplayName(period),
          callback_data: `trends:${type}:${period}`
        });
      }
    });

    if (periodRow.length > 0) {
      keyboard.push(periodRow);
    }

    // Action buttons row
    const actionRow = [];
    actionRow.push({
      text: '🔄 Làm mới',
      callback_data: `trends:${type}:${currentPeriod}`
    });

    // Switch between lo and de
    const otherType = type === 'lo' ? 'de' : 'lo';
    const otherTypeText = type === 'lo' ? 'Xu hướng Đề' : 'Xu hướng Lô';
    actionRow.push({
      text: otherTypeText,
      callback_data: `trends:${otherType}:${currentPeriod}`
    });

    keyboard.push(actionRow);

    return keyboard.length > 0 ? { inline_keyboard: keyboard } : undefined;
  }

  async handleTrendsCallback(ctx) {
    try {
      const [, type, period] = ctx.callbackQuery.data.split(':');

      if (!['lo', 'de'].includes(type) || !this.supportedPeriods.includes(period)) {
        await ctx.answerCbQuery('Tham số không hợp lệ.');
        return;
      }

      // Get trend data
      const trends = await this.predictionEngine.getTrends(type, period);

      if (!trends || (!trends.hot?.length && !trends.cold?.length)) {
        await ctx.answerCbQuery(`Không có dữ liệu xu hướng số ${type}.`);
        return;
      }

      // Enhance trends with additional analysis
      const enhancedTrends = await this._enhanceTrendsData(trends, type, period);

      // Format the response
      const formattedMessage = MessageFormatter.formatTrends({
        ...enhancedTrends,
        type,
        period: this._getPeriodDisplayName(period)
      });

      // Create updated keyboard
      const keyboard = this._createTrendsKeyboard(type, period);

      await ctx.editMessageText(formattedMessage, {
        parse_mode: 'HTML',
        reply_markup: keyboard
      });

      await ctx.answerCbQuery();

    } catch (error) {
      logger.logError('Trends callback failed:', error);
      await ctx.answerCbQuery('Lỗi khi tải dữ liệu xu hướng.');
    }
  }

  // Method to get comprehensive trend analysis
  async getComprehensiveTrends(type = 'lo', options = {}) {
    try {
      const {
        periods = ['daily', 'weekly', 'monthly'],
        includePatterns = true,
        includeCorrelations = false
      } = options;

      const analysis = {};

      // Get trends for each period
      for (const period of periods) {
        const trends = await this.predictionEngine.getTrends(type, period);
        const enhanced = await this._enhanceTrendsData(trends, type, period);
        analysis[period] = enhanced;
      }

      // Add pattern analysis if requested
      if (includePatterns) {
        analysis.patterns = await this._analyzeNumberPatterns(type);
      }

      // Add correlation analysis if requested
      if (includeCorrelations) {
        analysis.correlations = await this._analyzeNumberCorrelations(type);
      }

      return {
        type,
        analysis,
        generatedAt: new Date(),
        summary: this._generateTrendsSummary(analysis)
      };

    } catch (error) {
      logger.logError('Comprehensive trends analysis failed:', error);
      throw error;
    }
  }

  async _analyzeNumberPatterns(type) {
    try {
      // Get recent historical data
      const history = await this.dataManager.getLotteryHistory({
        limit: 100,
        sortBy: 'date',
        sortOrder: 'desc'
      });

      if (!history || history.length === 0) {
        return { patterns: [], analysis: 'Insufficient data' };
      }

      // Analyze patterns (consecutive numbers, even/odd distribution, etc.)
      const patterns = {
        consecutive: this._findConsecutivePatterns(history, type),
        evenOdd: this._analyzeEvenOddDistribution(history, type),
        sumRanges: this._analyzeSumRanges(history, type),
        digitPatterns: this._analyzeDigitPatterns(history, type)
      };

      return patterns;

    } catch (error) {
      logger.logError('Pattern analysis failed:', error);
      return { patterns: [], analysis: 'Pattern analysis failed' };
    }
  }

  async _analyzeNumberCorrelations(type) {
    try {
      // This would require more complex statistical analysis
      // For now, return a placeholder
      return {
        correlations: [],
        analysis: 'Correlation analysis not yet implemented'
      };

    } catch (error) {
      logger.logError('Correlation analysis failed:', error);
      return { correlations: [], analysis: 'Correlation analysis failed' };
    }
  }

  _findConsecutivePatterns(history, type) {
    const consecutiveCount = new Map();

    history.forEach(result => {
      const numbers = type === 'lo' ? result.numbers?.lo : result.numbers?.de;
      if (!numbers) return;

      const sortedNumbers = numbers.map(n => parseInt(n)).sort((a, b) => a - b);

      for (let i = 0; i < sortedNumbers.length - 1; i++) {
        if (sortedNumbers[i + 1] === sortedNumbers[i] + 1) {
          const pair = `${sortedNumbers[i]}-${sortedNumbers[i + 1]}`;
          consecutiveCount.set(pair, (consecutiveCount.get(pair) || 0) + 1);
        }
      }
    });

    return Array.from(consecutiveCount.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .map(([pair, count]) => ({ pair, count }));
  }

  _analyzeEvenOddDistribution(history, type) {
    let evenCount = 0;
    let oddCount = 0;
    let totalNumbers = 0;

    history.forEach(result => {
      const numbers = type === 'lo' ? result.numbers?.lo : result.numbers?.de;
      if (!numbers) return;

      numbers.forEach(num => {
        const n = parseInt(num);
        if (n % 2 === 0) {
          evenCount++;
        } else {
          oddCount++;
        }
        totalNumbers++;
      });
    });

    return {
      even: { count: evenCount, percentage: ((evenCount / totalNumbers) * 100).toFixed(1) },
      odd: { count: oddCount, percentage: ((oddCount / totalNumbers) * 100).toFixed(1) },
      total: totalNumbers
    };
  }

  _analyzeSumRanges(history, type) {
    const sumRanges = new Map();

    history.forEach(result => {
      const numbers = type === 'lo' ? result.numbers?.lo : result.numbers?.de;
      if (!numbers) return;

      const sum = numbers.reduce((acc, num) => acc + parseInt(num), 0);
      const range = Math.floor(sum / 50) * 50; // Group by 50s
      const rangeKey = `${range}-${range + 49}`;

      sumRanges.set(rangeKey, (sumRanges.get(rangeKey) || 0) + 1);
    });

    return Array.from(sumRanges.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(([range, count]) => ({ range, count }));
  }

  _analyzeDigitPatterns(history, type) {
    const digitFreq = new Map();

    history.forEach(result => {
      const numbers = type === 'lo' ? result.numbers?.lo : result.numbers?.de;
      if (!numbers) return;

      numbers.forEach(num => {
        const digits = num.split('');
        digits.forEach(digit => {
          digitFreq.set(digit, (digitFreq.get(digit) || 0) + 1);
        });
      });
    });

    return Array.from(digitFreq.entries())
      .sort((a, b) => b[1] - a[1])
      .map(([digit, count]) => ({ digit, count }));
  }

  _generateTrendsSummary(analysis) {
    const summary = {
      hotNumbers: [],
      coldNumbers: [],
      recommendations: [],
      insights: []
    };

    // Extract hot and cold numbers from weekly analysis
    if (analysis.weekly) {
      summary.hotNumbers = analysis.weekly.hot?.slice(0, 5) || [];
      summary.coldNumbers = analysis.weekly.cold?.slice(0, 5) || [];
    }

    // Generate basic recommendations
    if (summary.hotNumbers.length > 0) {
      summary.recommendations.push('Các số nóng có xu hướng xuất hiện thường xuyên');
    }

    if (summary.coldNumbers.length > 0) {
      summary.recommendations.push('Các số lạnh có thể sắp "nóng" trở lại');
    }

    return summary;
  }
}

module.exports = TrendsHandler;