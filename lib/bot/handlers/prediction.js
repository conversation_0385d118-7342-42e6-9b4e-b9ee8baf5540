const MessageFormatter = require('../utils/formatter');
const logger = require('../../logger')('logs');

// Prediction command handlers
// Handles /dukienlo and /dukiende commands

class PredictionHandler {
  constructor(predictionEngine, llmService, dataManager) {
    this.predictionEngine = predictionEngine;
    this.llmService = llmService;
    this.dataManager = dataManager;

    // Bind methods to preserve context
    this.handleDukienLo = this.handleDukienLo.bind(this);
    this.handleDukienDe = this.handleDukienDe.bind(this);
  }

  async handleDukienLo(ctx) {
    const startTime = Date.now();

    try {
      logger.logInfo(`User ${ctx.userId} requested lo prediction from chat ${ctx.chatId}`);

      // Send initial "processing" message
      const processingMsg = await ctx.bot.sendMessage(
        ctx.chatId,
        '🔄 Đang phân tích dữ liệu và tạo dự đoán số lô...',
        { parse_mode: 'HTML' }
      );

      // Get prediction from engine with enhanced error handling
      const predictionOptions = {
        type: 'lo',
        maxPredictions: 10,
        includeConfidence: true
      };

      const prediction = await this._handlePredictionWithFallback('lo', predictionOptions);

      // Validate prediction data
      this._validatePrediction(prediction, 'lo');

      // Format the prediction response
      let responseMessage = this._formatPredictionResponse(prediction, 'lo');

      // Add fallback notice if applicable
      if (prediction.fallback) {
        responseMessage += '\n\n⚠️ ' + MessageFormatter.italic('Lưu ý: Sử dụng mô hình dự phòng do lỗi hệ thống chính.');
      }

      // Generate LLM report if available
      if (this.llmService && prediction.predictions && prediction.predictions.length > 0) {
        try {
          const llmReport = await this._generateLLMReport(prediction, 'lo');
          if (llmReport) {
            responseMessage += '\n\n' + llmReport;
          }
        } catch (llmError) {
          logger.logError('LLM report generation failed:', llmError);
          // Continue without LLM report
        }
      }

      // Update the processing message with results
      await ctx.bot.editMessageText(responseMessage, {
        chat_id: ctx.chatId,
        message_id: processingMsg.message_id,
        parse_mode: 'HTML',
        disable_web_page_preview: true
      });

      // Log user interaction
      if (this.dataManager) {
        try {
          await this.dataManager.logUserInteraction(ctx.userId, 'dukienlo', {
            chatId: ctx.chatId,
            predictionCount: prediction.predictions?.length || 0,
            confidence: prediction.confidence || 0,
            responseTime: Date.now() - startTime
          });
        } catch (logError) {
          logger.logError('Failed to log user interaction:', logError);
        }
      }

      const responseTime = Date.now() - startTime;
      logger.logInfo(`Lo prediction completed for user ${ctx.userId} in ${responseTime}ms`);

    } catch (error) {
      logger.logError('Error in handleDukienLo:', error);

      const errorMessage = this._formatErrorResponse(error, 'dự đoán số lô');

      try {
        await ctx.bot.sendMessage(ctx.chatId, errorMessage, { parse_mode: 'HTML' });
      } catch (sendError) {
        logger.logError('Failed to send error message:', sendError);
      }
    }
  }

  async handleDukienDe(ctx) {
    const startTime = Date.now();

    try {
      logger.logInfo(`User ${ctx.userId} requested de prediction from chat ${ctx.chatId}`);

      // Send initial "processing" message
      const processingMsg = await ctx.bot.sendMessage(
        ctx.chatId,
        '🔄 Đang phân tích dữ liệu và tạo dự đoán số đề...',
        { parse_mode: 'HTML' }
      );

      // Get prediction from engine with enhanced error handling
      const predictionOptions = {
        type: 'de',
        maxPredictions: 8,
        includeConfidence: true
      };

      const prediction = await this._handlePredictionWithFallback('de', predictionOptions);

      // Validate prediction data
      this._validatePrediction(prediction, 'de');

      // Format the prediction response
      let responseMessage = this._formatPredictionResponse(prediction, 'de');

      // Add fallback notice if applicable
      if (prediction.fallback) {
        responseMessage += '\n\n⚠️ ' + MessageFormatter.italic('Lưu ý: Sử dụng mô hình dự phòng do lỗi hệ thống chính.');
      }

      // Generate LLM report if available
      if (this.llmService && prediction.predictions && prediction.predictions.length > 0) {
        try {
          const llmReport = await this._generateLLMReport(prediction, 'de');
          if (llmReport) {
            responseMessage += '\n\n' + llmReport;
          }
        } catch (llmError) {
          logger.logError('LLM report generation failed:', llmError);
          // Continue without LLM report
        }
      }

      // Update the processing message with results
      await ctx.bot.editMessageText(responseMessage, {
        chat_id: ctx.chatId,
        message_id: processingMsg.message_id,
        parse_mode: 'HTML',
        disable_web_page_preview: true
      });

      // Log user interaction
      if (this.dataManager) {
        try {
          await this.dataManager.logUserInteraction(ctx.userId, 'dukiende', {
            chatId: ctx.chatId,
            predictionCount: prediction.predictions?.length || 0,
            confidence: prediction.confidence || 0,
            responseTime: Date.now() - startTime
          });
        } catch (logError) {
          logger.logError('Failed to log user interaction:', logError);
        }
      }

      const responseTime = Date.now() - startTime;
      logger.logInfo(`De prediction completed for user ${ctx.userId} in ${responseTime}ms`);

    } catch (error) {
      logger.logError('Error in handleDukienDe:', error);

      const errorMessage = this._formatErrorResponse(error, 'dự đoán số đề');

      try {
        await ctx.bot.sendMessage(ctx.chatId, errorMessage, { parse_mode: 'HTML' });
      } catch (sendError) {
        logger.logError('Failed to send error message:', sendError);
      }
    }
  }

  _formatPredictionResponse(prediction, type) {
    try {
      const typeText = type === 'lo' ? 'Số Lô' : 'Số Đề';
      const emoji = type === 'lo' ? '🎲' : '🎯';

      let message = `${emoji} ${MessageFormatter.bold(`Dự Đoán ${typeText}`)}\n`;
      message += `📅 ${MessageFormatter.code(new Date().toLocaleDateString('vi-VN'))}\n`;

      if (prediction.confidence) {
        message += `📊 Độ tin cậy: ${MessageFormatter.formatConfidence(prediction.confidence)}\n`;
      }

      if (prediction.method) {
        const methodText = this._getMethodDisplayName(prediction.method);
        message += `🤖 Phương pháp: ${MessageFormatter.code(methodText)}\n`;
      }

      message += '\n';

      if (!prediction.predictions || prediction.predictions.length === 0) {
        message += '❌ Không có dự đoán khả dụng cho hôm nay.\n';
        message += 'Vui lòng thử lại sau hoặc kiểm tra dữ liệu lịch sử.';
        return message;
      }

      message += `${MessageFormatter.bold('🏆 Top Dự Đoán:')}\n`;

      prediction.predictions.forEach((pred, index) => {
        const rank = index + 1;
        const number = MessageFormatter.formatNumber(pred.number);
        const confidence = MessageFormatter.formatConfidence(pred.confidence || 0);

        let line = `${rank}. ${number} - ${confidence}`;

        // Add reasoning if available and not too long
        if (pred.reasoning && pred.reasoning.length < 50) {
          line += ` ${MessageFormatter.italic(`(${pred.reasoning})`)}`;
        }

        message += line + '\n';
      });

      // Add performance info if available
      if (prediction.confidenceFactors) {
        message += '\n' + MessageFormatter.bold('📈 Chi tiết độ tin cậy:') + '\n';
        const factors = prediction.confidenceFactors;

        if (factors.baseConfidence) {
          message += `• Cơ bản: ${factors.baseConfidence}%\n`;
        }
        if (factors.consistencyBonus) {
          message += `• Tính nhất quán: +${factors.consistencyBonus}%\n`;
        }
        if (factors.modelAgreementBonus) {
          message += `• Đồng thuận mô hình: +${factors.modelAgreementBonus}%\n`;
        }
        if (factors.accuracyBonus) {
          message += `• Độ chính xác lịch sử: +${factors.accuracyBonus}%\n`;
        }
      }

      // Add timestamp and additional info
      message += '\n' + MessageFormatter.bold('ℹ️ Thông tin bổ sung:') + '\n';
      message += `• Thời gian tạo: ${MessageFormatter.code(new Date().toLocaleTimeString('vi-VN'))}\n`;

      if (prediction.dataPoints) {
        message += `• Dữ liệu phân tích: ${MessageFormatter.code(prediction.dataPoints + ' mẫu')}\n`;
      }

      if (prediction.processingTime) {
        message += `• Thời gian xử lý: ${MessageFormatter.code(prediction.processingTime + 'ms')}\n`;
      }

      message += '\n💡 ' + MessageFormatter.italic('Lưu ý quan trọng:') + '\n';
      message += MessageFormatter.italic('• Đây chỉ là dự đoán dựa trên phân tích dữ liệu lịch sử\n');
      message += MessageFormatter.italic('• Kết quả xổ số có tính ngẫu nhiên cao\n');
      message += MessageFormatter.italic('• Chỉ chơi với số tiền bạn có thể chấp nhận mất\n');
      message += MessageFormatter.italic('• Sử dụng dự đoán một cách có trách nhiệm');

      return message;
    } catch (error) {
      logger.logError('Error formatting prediction response:', error);
      return MessageFormatter.formatError(error, 'định dạng dự đoán');
    }
  }

  async _generateLLMReport(prediction, type) {
    try {
      if (!this.llmService || !prediction.predictions || prediction.predictions.length === 0) {
        return null;
      }

      // Use the correct method signature from LLMService
      const predictions = prediction.predictions.slice(0, 5); // Top 5 for LLM analysis
      const historicalData = []; // We don't have historical data in this context

      const report = await this.llmService.generatePredictionReport(predictions, historicalData, type);

      if (report && report.length > 0) {
        return `${MessageFormatter.bold('🧠 Phân Tích AI:')}\n${report}`;
      }

      return null;
    } catch (error) {
      logger.logError('Error generating LLM report:', error);
      return null;
    }
  }

  _formatErrorResponse(error, context) {
    const contextText = context ? ` ${context}` : '';

    // Check for specific error types with more detailed responses
    if (error.message?.includes('DataManager not available')) {
      return `❌ Dịch vụ dữ liệu hiện không khả dụng.\n` +
             `🔧 Hệ thống đang bảo trì hoặc gặp sự cố kỹ thuật.\n` +
             `⏰ Vui lòng thử lại sau 5-10 phút.\n\n` +
             `💡 Nếu lỗi tiếp tục, hãy liên hệ quản trị viên.`;
    }

    if (error.message?.includes('insufficient data')) {
      return `❌ Không đủ dữ liệu lịch sử để tạo${contextText}.\n` +
             `📊 Hệ thống cần ít nhất 30 ngày dữ liệu để hoạt động tốt.\n` +
             `🔄 Chúng tôi đang thu thập thêm dữ liệu, vui lòng thử lại sau.\n\n` +
             `💡 Bạn có thể thử lệnh khác trong thời gian chờ đợi.`;
    }

    if (error.message?.includes('model not available')) {
      return `❌ Mô hình dự đoán hiện không khả dụng.\n` +
             `🤖 Hệ thống AI đang được cập nhật hoặc bảo trì.\n` +
             `⏰ Vui lòng thử lại sau 10-15 phút.\n\n` +
             `🔄 Chúng tôi sẽ sớm khôi phục dịch vụ.`;
    }

    if (error.message?.includes('rate limit')) {
      return `❌ Bạn đã gửi quá nhiều yêu cầu.\n` +
             `⏰ Vui lòng chờ một chút trước khi thử lại.\n` +
             `🚦 Giới hạn: 10 lệnh mỗi phút để đảm bảo chất lượng dịch vụ.\n\n` +
             `💡 Hãy kiên nhẫn, chất lượng dự đoán cần thời gian!`;
    }

    if (error.message?.includes('network') || error.message?.includes('timeout')) {
      return `❌ Lỗi kết nối mạng khi tạo${contextText}.\n` +
             `🌐 Vui lòng kiểm tra kết nối internet và thử lại.\n` +
             `⏰ Nếu vẫn lỗi, hãy chờ vài phút rồi thử lại.\n\n` +
             `🔧 Hệ thống có thể đang quá tải.`;
    }

    // Generic error message with more helpful information
    const errorCode = error.code || error.name || 'UNKNOWN';
    return `❌ Đã xảy ra lỗi khi tạo${contextText}.\n` +
           `🔧 Lỗi hệ thống: ${MessageFormatter.code(errorCode)}\n` +
           `⏰ Vui lòng thử lại sau 2-3 phút.\n\n` +
           `💡 Nếu lỗi tiếp tục xảy ra, hãy:\n` +
           `• Thử lệnh khác trước\n` +
           `• Chờ 5-10 phút rồi thử lại\n` +
           `• Liên hệ quản trị viên nếu cần thiết`;
  }

  _getMethodDisplayName(method) {
    const methodNames = {
      'statistical': 'Thống kê',
      'lstm': 'LSTM Neural Network',
      'ensemble': 'Kết hợp nhiều mô hình',
      'combined': 'Tổng hợp',
      'error': 'Lỗi',
      'no_predictions': 'Không có dự đoán'
    };

    return methodNames[method] || method;
  }

  // Utility method to validate prediction data
  _validatePrediction(prediction, type) {
    if (!prediction) {
      throw new Error('Prediction data is null or undefined');
    }

    if (prediction.error) {
      throw new Error(prediction.error);
    }

    if (!prediction.predictions || !Array.isArray(prediction.predictions)) {
      throw new Error('Invalid prediction format: predictions array missing');
    }

    // Validate individual predictions
    for (const pred of prediction.predictions) {
      if (!pred.number || isNaN(pred.number)) {
        throw new Error('Invalid prediction: number is missing or not a number');
      }

      if (pred.confidence !== undefined && (isNaN(pred.confidence) || pred.confidence < 0 || pred.confidence > 100)) {
        throw new Error('Invalid prediction: confidence must be between 0 and 100');
      }
    }

    return true;
  }

  // Enhanced prediction with timeout and retry logic
  async _getPredictionWithTimeout(predictionMethod, options, timeoutMs = 10000) {
    return new Promise(async (resolve, reject) => {
      const timeoutId = setTimeout(() => {
        reject(new Error('Prediction timeout - request took too long'));
      }, timeoutMs);

      try {
        const result = await predictionMethod(options);
        clearTimeout(timeoutId);
        resolve(result);
      } catch (error) {
        clearTimeout(timeoutId);
        reject(error);
      }
    });
  }

  // Enhanced error recovery with fallback options
  async _handlePredictionWithFallback(type, options) {
    const maxRetries = 2;
    let lastError;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        logger.logInfo(`Prediction attempt ${attempt} for type ${type}`);

        const predictionMethod = type === 'lo'
          ? this.predictionEngine.predictLo.bind(this.predictionEngine)
          : this.predictionEngine.predictDe.bind(this.predictionEngine);

        const result = await this._getPredictionWithTimeout(predictionMethod, options, 8000);

        if (result && !result.error) {
          return result;
        }

        throw new Error(result?.error || 'Invalid prediction result');

      } catch (error) {
        lastError = error;
        logger.logError(`Prediction attempt ${attempt} failed:`, error);

        if (attempt < maxRetries) {
          // Wait before retry with exponential backoff
          const waitTime = Math.pow(2, attempt) * 1000;
          logger.logInfo(`Retrying in ${waitTime}ms...`);
          await new Promise(resolve => setTimeout(resolve, waitTime));
        }
      }
    }

    // All attempts failed, try fallback with basic statistical model
    try {
      logger.logInfo(`Attempting fallback prediction for type ${type}`);
      const fallbackOptions = {
        ...options,
        model: 'statistical',
        maxPredictions: Math.min(options.maxPredictions || 5, 5)
      };

      const predictionMethod = type === 'lo'
        ? this.predictionEngine.predictLo.bind(this.predictionEngine)
        : this.predictionEngine.predictDe.bind(this.predictionEngine);

      const fallbackResult = await this._getPredictionWithTimeout(predictionMethod, fallbackOptions, 5000);

      if (fallbackResult && !fallbackResult.error) {
        return {
          ...fallbackResult,
          fallback: true,
          originalError: lastError.message
        };
      }
    } catch (fallbackError) {
      logger.logError('Fallback prediction also failed:', fallbackError);
    }

    throw lastError;
  }

  // Method to get handler functions for registration
  getHandlers() {
    return {
      dukienlo: this.handleDukienLo,
      dukiende: this.handleDukienDe
    };
  }
}

module.exports = PredictionHandler;