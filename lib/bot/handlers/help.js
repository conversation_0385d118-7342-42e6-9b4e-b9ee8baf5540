const logger = require('../../logger')('logs');
const MessageFormatter = require('../utils/formatter');

// Help command handler
// Handles /help command with comprehensive usage instructions

class HelpHandler {
  constructor() {
    this.commands = [
      {
        category: 'D<PERSON> Đoán',
        emoji: '🎯',
        commands: [
          {
            command: '/dukienlo',
            description: 'Dự đoán số lô với độ tin cậy',
            usage: '/dukienlo',
            example: '/dukienlo'
          },
          {
            command: '/dukiende',
            description: 'D<PERSON> đoán số đề với độ tin cậy',
            usage: '/dukiende',
            example: '/dukiende'
          }
        ]
      },
      {
        category: 'Lịch Sử',
        emoji: '📊',
        commands: [
          {
            command: '/lichsu',
            description: 'Xem kết quả xổ số gần đây',
            usage: '/lichsu [số_kết_quả] [trang] [miền]',
            example: '/lichsu 10 2 north',
            details: 'Mặc định: 5 kết quả, trang 1, miề<PERSON>'
          }
        ]
      },
      {
        category: '<PERSON>',
        emoji: '📈',
        commands: [
          {
            command: '/xuhuonglo',
            description: '<PERSON><PERSON> tích xu hướng số lô nóng/lạnh',
            usage: '/xuhuonglo [thời_gian]',
            example: '/xuhuonglo 30',
            details: 'Thời gian tính bằng ngày (mặc định: 7 ngày)'
          },
          {
            command: '/xuhuongde',
            description: 'Phân tích xu hướng số đề nóng/lạnh',
            usage: '/xuhuongde [thời_gian]',
            example: '/xuhuongde 30',
            details: 'Thời gian tính bằng ngày (mặc định: 7 ngày)'
          }
        ]
      },
      {
        category: 'Phân Tích',
        emoji: '🔍',
        commands: [
          {
            command: '/number',
            description: 'Phân tích chi tiết số cụ thể',
            usage: '/number [số]',
            example: '/number 25',
            details: 'Số từ 00 đến 99. Hiển thị thống kê, xu hướng và tương quan'
          }
        ]
      },
      {
        category: 'Hỗ Trợ',
        emoji: '❓',
        commands: [
          {
            command: '/help',
            description: 'Hiển thị hướng dẫn sử dụng',
            usage: '/help [lệnh]',
            example: '/help number',
            details: 'Không có tham số: hiển thị tất cả lệnh'
          }
        ]
      }
    ];
  }

  /**
   * Handle /help command
   * @param {Object} ctx - Telegram context
   */
  async handleHelp(ctx) {
    try {
      const args = ctx.args || [];

      if (args.length === 0) {
        // Show general help
        await this._sendGeneralHelp(ctx);
      } else {
        // Show specific command help
        const commandName = args[0].toLowerCase().replace('/', '');
        await this._sendCommandHelp(ctx, commandName);
      }

      logger.logInfo(`Help requested by user ${ctx.userId}, args: ${args.join(' ')}`);

    } catch (error) {
      logger.logError('Error in handleHelp:', error);
      await ctx.reply(MessageFormatter.formatError(error, 'hiển thị trợ giúp'));
    }
  }

  /**
   * Send general help message with all commands
   * @param {Object} ctx - Telegram context
   */
  async _sendGeneralHelp(ctx) {
    let message = `🤖 ${MessageFormatter.bold('Bot Dự Đoán Xổ Số Miền Bắc')}\n\n`;
    message += `${MessageFormatter.italic('Sử dụng AI và phân tích thống kê để dự đoán kết quả xổ số')}\n\n`;

    this.commands.forEach(category => {
      message += `${category.emoji} ${MessageFormatter.bold(category.category)}:\n`;

      category.commands.forEach(cmd => {
        message += `• ${MessageFormatter.code(cmd.command)} - ${cmd.description}\n`;
      });

      message += '\n';
    });

    message += `💡 ${MessageFormatter.bold('Mẹo sử dụng:')}\n`;
    message += `• Gửi ${MessageFormatter.code('/help [lệnh]')} để xem hướng dẫn chi tiết\n`;
    message += `• Ví dụ: ${MessageFormatter.code('/help number')}\n`;
    message += `• Tất cả dự đoán chỉ mang tính tham khảo\n\n`;

    message += `📞 ${MessageFormatter.bold('Hỗ trợ:')}\n`;
    message += `• Bot hoạt động 24/7\n`;
    message += `• Dữ liệu cập nhật hàng ngày\n`;
    message += `• Báo lỗi: liên hệ quản trị viên`;

    await ctx.reply(
      MessageFormatter.truncateMessage(message),
      {
        parse_mode: 'HTML',
        reply_markup: this._createHelpKeyboard()
      }
    );
  }

  /**
   * Send specific command help
   * @param {Object} ctx - Telegram context
   * @param {string} commandName - Command to get help for
   */
  async _sendCommandHelp(ctx, commandName) {
    const command = this._findCommand(commandName);

    if (!command) {
      await ctx.reply(
        `❌ Không tìm thấy lệnh "${commandName}".\n\n` +
        `💡 Gửi ${MessageFormatter.code('/help')} để xem tất cả lệnh có sẵn.`,
        { parse_mode: 'HTML' }
      );
      return;
    }

    let message = `📖 ${MessageFormatter.bold(`Hướng Dẫn: ${command.command}`)}\n\n`;

    message += `📝 ${MessageFormatter.bold('Mô tả:')}\n`;
    message += `${command.description}\n\n`;

    message += `⌨️ ${MessageFormatter.bold('Cách sử dụng:')}\n`;
    message += `${MessageFormatter.code(command.usage)}\n\n`;

    message += `💡 ${MessageFormatter.bold('Ví dụ:')}\n`;
    message += `${MessageFormatter.code(command.example)}\n\n`;

    if (command.details) {
      message += `ℹ️ ${MessageFormatter.bold('Chi tiết:')}\n`;
      message += `${command.details}\n\n`;
    }

    // Add specific usage tips based on command
    const tips = this._getCommandTips(commandName);
    if (tips.length > 0) {
      message += `🎯 ${MessageFormatter.bold('Mẹo sử dụng:')}\n`;
      tips.forEach(tip => {
        message += `• ${tip}\n`;
      });
      message += '\n';
    }

    message += `🔙 Gửi ${MessageFormatter.code('/help')} để quay lại menu chính.`;

    await ctx.reply(
      MessageFormatter.truncateMessage(message),
      {
        parse_mode: 'HTML',
        reply_markup: this._createCommandHelpKeyboard(commandName)
      }
    );
  }

  /**
   * Handle help callback queries
   * @param {Object} ctx - Telegram context
   */
  async handleHelpCallback(ctx) {
    try {
      const [action, param] = ctx.callbackQuery.data.split(':');

      if (action !== 'help') {
        return;
      }

      await ctx.answerCbQuery();

      if (param === 'main') {
        await this._sendGeneralHelp(ctx);
      } else if (param === 'examples') {
        await this._sendExamples(ctx);
      } else if (param === 'tips') {
        await this._sendTips(ctx);
      } else {
        // Specific command help
        await this._sendCommandHelp(ctx, param);
      }

    } catch (error) {
      logger.logError('Error in handleHelpCallback:', error);
      await ctx.answerCbQuery('❌ Lỗi khi tải trợ giúp.');
    }
  }

  /**
   * Send usage examples
   * @param {Object} ctx - Telegram context
   */
  async _sendExamples(ctx) {
    let message = `📚 ${MessageFormatter.bold('Ví Dụ Sử Dụng')}\n\n`;

    message += `🎯 ${MessageFormatter.bold('Dự đoán cơ bản:')}\n`;
    message += `• ${MessageFormatter.code('/dukienlo')} - Dự đoán số lô hôm nay\n`;
    message += `• ${MessageFormatter.code('/dukiende')} - Dự đoán số đề hôm nay\n\n`;

    message += `📊 ${MessageFormatter.bold('Xem lịch sử:')}\n`;
    message += `• ${MessageFormatter.code('/lichsu')} - 5 kết quả gần nhất\n`;
    message += `• ${MessageFormatter.code('/lichsu 10')} - 10 kết quả gần nhất\n`;
    message += `• ${MessageFormatter.code('/lichsu 5 2')} - Trang 2, mỗi trang 5 kết quả\n\n`;

    message += `📈 ${MessageFormatter.bold('Phân tích xu hướng:')}\n`;
    message += `• ${MessageFormatter.code('/xuhuonglo')} - Xu hướng lô 7 ngày qua\n`;
    message += `• ${MessageFormatter.code('/xuhuonglo 30')} - Xu hướng lô 30 ngày qua\n`;
    message += `• ${MessageFormatter.code('/xuhuongde 14')} - Xu hướng đề 14 ngày qua\n\n`;

    message += `🔍 ${MessageFormatter.bold('Phân tích số cụ thể:')}\n`;
    message += `• ${MessageFormatter.code('/number 25')} - Phân tích số 25\n`;
    message += `• ${MessageFormatter.code('/number 07')} - Phân tích số 07\n`;
    message += `• ${MessageFormatter.code('/number 99')} - Phân tích số 99\n\n`;

    message += `❓ ${MessageFormatter.bold('Trợ giúp:')}\n`;
    message += `• ${MessageFormatter.code('/help')} - Menu trợ giúp chính\n`;
    message += `• ${MessageFormatter.code('/help number')} - Hướng dẫn lệnh /number`;

    await ctx.editMessageText(
      MessageFormatter.truncateMessage(message),
      {
        parse_mode: 'HTML',
        reply_markup: {
          inline_keyboard: [
            [{ text: '🔙 Quay lại', callback_data: 'help:main' }]
          ]
        }
      }
    );
  }

  /**
   * Send usage tips
   * @param {Object} ctx - Telegram context
   */
  async _sendTips(ctx) {
    let message = `💡 ${MessageFormatter.bold('Mẹo Sử Dụng Bot')}\n\n`;

    message += `🎯 ${MessageFormatter.bold('Dự đoán hiệu quả:')}\n`;
    message += `• Kết hợp nhiều phương pháp phân tích\n`;
    message += `• Chú ý đến độ tin cậy của dự đoán\n`;
    message += `• Theo dõi xu hướng dài hạn\n`;
    message += `• Không dựa hoàn toàn vào dự đoán\n\n`;

    message += `📊 ${MessageFormatter.bold('Phân tích dữ liệu:')}\n`;
    message += `• Xem lịch sử để hiểu mẫu số\n`;
    message += `• So sánh xu hướng ngắn hạn và dài hạn\n`;
    message += `• Chú ý đến số nóng và số lạnh\n`;
    message += `• Phân tích tương quan giữa các số\n\n`;

    message += `🔍 ${MessageFormatter.bold('Sử dụng lệnh /number:')}\n`;
    message += `• Nhập số có 2 chữ số (00-99)\n`;
    message += `• Xem cả phân tích lô và đề\n`;
    message += `• Chú ý đến khuyến nghị của bot\n`;
    message += `• Sử dụng nút để xem chi tiết\n\n`;

    message += `⚠️ ${MessageFormatter.bold('Lưu ý quan trọng:')}\n`;
    message += `• Tất cả dự đoán chỉ mang tính tham khảo\n`;
    message += `• Kết quả xổ số là ngẫu nhiên\n`;
    message += `• Chơi có trách nhiệm\n`;
    message += `• Bot không đảm bảo độ chính xác 100%`;

    await ctx.editMessageText(
      MessageFormatter.truncateMessage(message),
      {
        parse_mode: 'HTML',
        reply_markup: {
          inline_keyboard: [
            [{ text: '🔙 Quay lại', callback_data: 'help:main' }]
          ]
        }
      }
    );
  }

  /**
   * Find command by name
   * @param {string} commandName - Command name to find
   * @returns {Object|null} Command object or null
   */
  _findCommand(commandName) {
    for (const category of this.commands) {
      for (const cmd of category.commands) {
        if (cmd.command.replace('/', '') === commandName) {
          return cmd;
        }
      }
    }
    return null;
  }

  /**
   * Get command-specific tips
   * @param {string} commandName - Command name
   * @returns {Array} Array of tips
   */
  _getCommandTips(commandName) {
    const tips = {
      'dukienlo': [
        'Dự đoán được cập nhật hàng ngày',
        'Chú ý đến độ tin cậy (màu xanh = cao, đỏ = thấp)',
        'Kết hợp với phân tích xu hướng để tăng độ chính xác'
      ],
      'dukiende': [
        'Số đề có tỷ lệ trúng thấp hơn số lô',
        'Xem dự đoán với độ tin cậy cao nhất',
        'So sánh với xu hướng số nóng/lạnh'
      ],
      'lichsu': [
        'Mặc định hiển thị 5 kết quả gần nhất',
        'Có thể xem tối đa 20 kết quả mỗi lần',
        'Sử dụng nút điều hướng để xem thêm'
      ],
      'xuhuonglo': [
        'Mặc định phân tích 7 ngày gần đây',
        'Có thể chọn từ 3 đến 90 ngày',
        'Số nóng = xuất hiện nhiều, số lạnh = ít xuất hiện'
      ],
      'xuhuongde': [
        'Tương tự xu hướng lô nhưng cho số đề',
        'Số đề có ít dữ liệu hơn số lô',
        'Nên phân tích thời gian dài hơn (30+ ngày)'
      ],
      'number': [
        'Nhập số từ 00 đến 99',
        'Xem được cả phân tích lô và đề',
        'Sử dụng các nút để xem chi tiết khác nhau',
        'Chú ý đến phần khuyến nghị'
      ]
    };

    return tips[commandName] || [];
  }

  /**
   * Create help keyboard
   * @returns {Object} Inline keyboard
   */
  _createHelpKeyboard() {
    return {
      inline_keyboard: [
        [
          { text: '📚 Ví dụ', callback_data: 'help:examples' },
          { text: '💡 Mẹo', callback_data: 'help:tips' }
        ],
        [
          { text: '🎯 Dự đoán', callback_data: 'help:dukienlo' },
          { text: '📊 Lịch sử', callback_data: 'help:lichsu' }
        ],
        [
          { text: '📈 Xu hướng', callback_data: 'help:xuhuonglo' },
          { text: '🔍 Phân tích', callback_data: 'help:number' }
        ]
      ]
    };
  }

  /**
   * Create command-specific help keyboard
   * @param {string} commandName - Command name
   * @returns {Object} Inline keyboard
   */
  _createCommandHelpKeyboard(commandName) {
    const keyboards = {
      'dukienlo': [
        [
          { text: '🎯 Thử ngay', callback_data: 'run:dukienlo' },
          { text: '📈 Xu hướng lô', callback_data: 'help:xuhuonglo' }
        ]
      ],
      'dukiende': [
        [
          { text: '🎯 Thử ngay', callback_data: 'run:dukiende' },
          { text: '📈 Xu hướng đề', callback_data: 'help:xuhuongde' }
        ]
      ],
      'number': [
        [
          { text: '🔍 Thử với số 25', callback_data: 'run:number:25' },
          { text: '📊 Xem lịch sử', callback_data: 'help:lichsu' }
        ]
      ]
    };

    const specificKeyboard = keyboards[commandName] || [];

    return {
      inline_keyboard: [
        ...specificKeyboard,
        [{ text: '🔙 Quay lại', callback_data: 'help:main' }]
      ]
    };
  }

  /**
   * Handle invalid command suggestions
   * @param {Object} ctx - Telegram context
   * @param {string} invalidCommand - Invalid command that was entered
   */
  async handleInvalidCommand(ctx, invalidCommand) {
    try {
      const suggestions = this._getSuggestions(invalidCommand);

      let message = `❌ Lệnh không hợp lệ: ${MessageFormatter.code('/' + invalidCommand)}\n\n`;

      if (suggestions.length > 0) {
        message += `💡 ${MessageFormatter.bold('Có phải bạn muốn dùng:')}\n`;
        suggestions.forEach(suggestion => {
          message += `• ${MessageFormatter.code('/' + suggestion.command)} - ${suggestion.description}\n`;
        });
        message += '\n';
      }

      message += `📖 Gửi ${MessageFormatter.code('/help')} để xem tất cả lệnh có sẵn.`;

      await ctx.reply(
        MessageFormatter.truncateMessage(message),
        {
          parse_mode: 'HTML',
          reply_markup: {
            inline_keyboard: [
              [{ text: '📖 Xem trợ giúp', callback_data: 'help:main' }]
            ]
          }
        }
      );

      logger.logInfo(`Invalid command "${invalidCommand}" by user ${ctx.userId}, suggestions: ${suggestions.map(s => s.command).join(', ')}`);

    } catch (error) {
      logger.logError('Error in handleInvalidCommand:', error);
      await ctx.reply(MessageFormatter.formatError(error, 'xử lý lệnh không hợp lệ'));
    }
  }

  /**
   * Get command suggestions based on invalid input
   * @param {string} invalidCommand - Invalid command
   * @returns {Array} Array of suggested commands
   */
  _getSuggestions(invalidCommand) {
    const allCommands = [];

    this.commands.forEach(category => {
      category.commands.forEach(cmd => {
        allCommands.push({
          command: cmd.command.replace('/', ''),
          description: cmd.description
        });
      });
    });

    // Simple fuzzy matching
    const suggestions = allCommands.filter(cmd => {
      const cmdName = cmd.command.toLowerCase();
      const input = invalidCommand.toLowerCase();

      // Exact match
      if (cmdName === input) return true;

      // Starts with
      if (cmdName.startsWith(input) || input.startsWith(cmdName)) return true;

      // Contains
      if (cmdName.includes(input) || input.includes(cmdName)) return true;

      // Levenshtein distance (simple version)
      return this._calculateDistance(cmdName, input) <= 2;
    });

    return suggestions.slice(0, 3); // Return top 3 suggestions
  }

  /**
   * Calculate simple edit distance
   * @param {string} a - First string
   * @param {string} b - Second string
   * @returns {number} Edit distance
   */
  _calculateDistance(a, b) {
    if (a.length === 0) return b.length;
    if (b.length === 0) return a.length;

    const matrix = [];

    for (let i = 0; i <= b.length; i++) {
      matrix[i] = [i];
    }

    for (let j = 0; j <= a.length; j++) {
      matrix[0][j] = j;
    }

    for (let i = 1; i <= b.length; i++) {
      for (let j = 1; j <= a.length; j++) {
        if (b.charAt(i - 1) === a.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          );
        }
      }
    }

    return matrix[b.length][a.length];
  }
}

module.exports = HelpHandler;