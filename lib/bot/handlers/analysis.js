const logger = require('../../logger')('logs');
const MessageFormatter = require('../utils/formatter');

// Analysis command handler
// Handles /number command for detailed number analysis

class AnalysisHandler {
  constructor(predictionEngine, dataManager) {
    this.predictionEngine = predictionEngine;
    this.dataManager = dataManager;
    this.maxAnalysisHistory = 365; // Days of history to analyze
  }

  /**
   * Handle /number command for detailed number analysis
   * @param {Object} ctx - Telegram context
   */
  async handleNumberAnalysis(ctx) {
    try {
      const args = ctx.args || [];

      if (args.length === 0) {
        await ctx.reply(
          '❌ Vui lòng cung cấp số cần phân tích.\n\n' +
          '📝 Cách sử dụng: /number [số]\n' +
          '💡 Ví dụ: /number 25'
        );
        return;
      }

      const numberInput = args[0];

      // Validate number format
      const validationResult = this._validateNumber(numberInput);
      if (!validationResult.isValid) {
        await ctx.reply(validationResult.message);
        return;
      }

      const number = validationResult.number;

      // Send initial message
      const initialMessage = await ctx.reply(
        `🔍 Đang phân tích số ${MessageFormatter.formatNumber(number)}...\n⏳ Vui lòng đợi...`
      );

      try {
        // Get historical data
        const historicalData = await this.dataManager.getLotteryHistory({
          region: 'north',
          limit: this.maxAnalysisHistory,
          sortBy: 'date',
          sortOrder: 'desc'
        });

        if (!historicalData || historicalData.length === 0) {
          await ctx.editMessageText(
            '❌ Không có dữ liệu lịch sử để phân tích.',
            { message_id: initialMessage.message_id }
          );
          return;
        }

        // Perform analysis for both lo and de
        const loAnalysis = await this._performNumberAnalysis(number, historicalData, 'lo');
        const deAnalysis = await this._performNumberAnalysis(number, historicalData, 'de');

        // Format and send analysis results
        const analysisMessage = this._formatAnalysisResults(number, loAnalysis, deAnalysis, historicalData.length);

        await ctx.editMessageText(
          analysisMessage,
          {
            message_id: initialMessage.message_id,
            parse_mode: 'HTML',
            reply_markup: this._createAnalysisKeyboard(number)
          }
        );

        logger.logInfo(`Number analysis completed for ${number} by user ${ctx.userId}`);

      } catch (analysisError) {
        logger.logError('Error during number analysis:', analysisError);
        await ctx.editMessageText(
          MessageFormatter.formatError(analysisError, 'phân tích số'),
          { message_id: initialMessage.message_id }
        );
      }

    } catch (error) {
      logger.logError('Error in handleNumberAnalysis:', error);
      await ctx.reply(MessageFormatter.formatError(error, 'xử lý lệnh phân tích'));
    }
  }

  /**
   * Handle analysis callback queries
   * @param {Object} ctx - Telegram context
   */
  async handleAnalysisCallback(ctx) {
    try {
      const [action, number, type] = ctx.callbackQuery.data.split(':');

      if (action !== 'analysis') {
        return;
      }

      await ctx.answerCbQuery('🔍 Đang tải phân tích chi tiết...');

      // Get fresh historical data
      const historicalData = await this.dataManager.getLotteryHistory({
        region: 'north',
        limit: this.maxAnalysisHistory,
        sortBy: 'date',
        sortOrder: 'desc'
      });

      if (!historicalData || historicalData.length === 0) {
        await ctx.answerCbQuery('❌ Không có dữ liệu lịch sử.');
        return;
      }

      let analysisMessage;

      if (type === 'detailed') {
        // Show detailed analysis for both lo and de
        const loAnalysis = await this._performNumberAnalysis(number, historicalData, 'lo');
        const deAnalysis = await this._performNumberAnalysis(number, historicalData, 'de');
        analysisMessage = this._formatDetailedAnalysis(number, loAnalysis, deAnalysis);
      } else if (type === 'trends') {
        // Show trend analysis
        const loAnalysis = await this._performNumberAnalysis(number, historicalData, 'lo');
        const deAnalysis = await this._performNumberAnalysis(number, historicalData, 'de');
        analysisMessage = this._formatTrendAnalysis(number, loAnalysis, deAnalysis);
      } else if (type === 'correlations') {
        // Show correlation analysis
        const loAnalysis = await this._performNumberAnalysis(number, historicalData, 'lo');
        const deAnalysis = await this._performNumberAnalysis(number, historicalData, 'de');
        analysisMessage = this._formatCorrelationAnalysis(number, loAnalysis, deAnalysis);
      } else {
        // Default: show summary
        const loAnalysis = await this._performNumberAnalysis(number, historicalData, 'lo');
        const deAnalysis = await this._performNumberAnalysis(number, historicalData, 'de');
        analysisMessage = this._formatAnalysisResults(number, loAnalysis, deAnalysis, historicalData.length);
      }

      await ctx.editMessageText(
        analysisMessage,
        {
          parse_mode: 'HTML',
          reply_markup: this._createAnalysisKeyboard(number)
        }
      );

    } catch (error) {
      logger.logError('Error in handleAnalysisCallback:', error);
      await ctx.answerCbQuery('❌ Lỗi khi tải phân tích.');
    }
  }

  /**
   * Validate number input
   * @param {string} input - User input
   * @returns {Object} Validation result
   */
  _validateNumber(input) {
    // Remove any non-digit characters
    const cleanInput = input.replace(/\D/g, '');

    if (cleanInput.length === 0) {
      return {
        isValid: false,
        message: '❌ Số không hợp lệ. Vui lòng nhập số từ 00 đến 99.\n💡 Ví dụ: /number 25'
      };
    }

    const number = parseInt(cleanInput, 10);

    if (isNaN(number) || number < 0 || number > 99) {
      return {
        isValid: false,
        message: '❌ Số phải từ 00 đến 99.\n💡 Ví dụ: /number 25'
      };
    }

    return {
      isValid: true,
      number: number.toString().padStart(2, '0')
    };
  }

  /**
   * Perform number analysis using the prediction engine
   * @param {string} number - Number to analyze
   * @param {Array} historicalData - Historical lottery data
   * @param {string} type - 'lo' or 'de'
   * @returns {Object} Analysis results
   */
  async _performNumberAnalysis(number, historicalData, type) {
    try {
      if (!this.predictionEngine.numberAnalyzer) {
        // Fallback to basic analysis if numberAnalyzer is not available
        return this._performBasicAnalysis(number, historicalData, type);
      }

      return this.predictionEngine.numberAnalyzer.analyzeNumber(number, historicalData, type);
    } catch (error) {
      logger.logError(`Error analyzing number ${number} for type ${type}:`, error);
      return this._performBasicAnalysis(number, historicalData, type);
    }
  }

  /**
   * Perform basic analysis when advanced analyzer is not available
   * @param {string} number - Number to analyze
   * @param {Array} historicalData - Historical lottery data
   * @param {string} type - 'lo' or 'de'
   * @returns {Object} Basic analysis results
   */
  _performBasicAnalysis(number, historicalData, type) {
    const occurrences = [];
    let totalOccurrences = 0;

    historicalData.forEach((draw, index) => {
      if (draw.numbers && draw.numbers[type] && draw.numbers[type].includes(number)) {
        occurrences.push({
          date: draw.date,
          index,
          draw
        });
        totalOccurrences++;
      }
    });

    const frequency = totalOccurrences / historicalData.length;
    const expectedFrequency = type === 'lo' ? 0.27 : 0.02;
    const lastSeen = occurrences.length > 0 ? occurrences[occurrences.length - 1].date : null;
    const daysSinceLastSeen = lastSeen
      ? Math.floor((new Date() - new Date(lastSeen)) / (1000 * 60 * 60 * 24))
      : null;

    return {
      number,
      type,
      totalOccurrences,
      frequency,
      expectedFrequency,
      relativeFrequency: frequency / expectedFrequency,
      lastSeen,
      daysSinceLastSeen,
      trends: {
        short: this._calculateShortTrend(occurrences, historicalData.slice(-7)),
        medium: this._calculateShortTrend(occurrences, historicalData.slice(-30)),
        long: this._calculateShortTrend(occurrences, historicalData.slice(-90))
      },
      recommendations: this._generateBasicRecommendations(number, totalOccurrences, frequency, expectedFrequency, daysSinceLastSeen)
    };
  }

  /**
   * Calculate short-term trend
   */
  _calculateShortTrend(occurrences, recentData) {
    const recentOccurrences = occurrences.filter(occ =>
      recentData.some(draw => draw.date.getTime() === new Date(occ.date).getTime())
    );

    const frequency = recentOccurrences.length / recentData.length;
    const expectedFrequency = 0.27; // Approximate for lo

    return {
      occurrences: recentOccurrences.length,
      frequency,
      trend: frequency > expectedFrequency ? 'hot' : frequency < expectedFrequency ? 'cold' : 'neutral',
      strength: Math.abs(frequency - expectedFrequency) / expectedFrequency
    };
  }

  /**
   * Generate basic recommendations
   */
  _generateBasicRecommendations(number, totalOccurrences, frequency, expectedFrequency, daysSinceLastSeen) {
    const recommendations = [];

    if (frequency > expectedFrequency * 1.2) {
      recommendations.push({
        type: 'frequency',
        priority: 'medium',
        message: `Số ${number} xuất hiện thường xuyên hơn mức trung bình`,
        confidence: Math.min(80, 50 + (frequency / expectedFrequency - 1) * 30)
      });
    }

    if (daysSinceLastSeen !== null && daysSinceLastSeen > 30) {
      recommendations.push({
        type: 'absence',
        priority: 'low',
        message: `Số ${number} đã không xuất hiện ${daysSinceLastSeen} ngày`,
        confidence: Math.min(60, 30 + daysSinceLastSeen)
      });
    }

    return recommendations;
  }

  /**
   * Format analysis results for display
   */
  _formatAnalysisResults(number, loAnalysis, deAnalysis, totalDraws) {
    let message = `🔍 ${MessageFormatter.bold(`Phân Tích Số ${MessageFormatter.formatNumber(number)}`)}\n`;
    message += `📊 Dữ liệu: ${totalDraws} kỳ quay gần đây\n\n`;

    // Lo analysis
    message += `🎲 ${MessageFormatter.bold('Phân Tích Lô:')}\n`;
    message += `• Xuất hiện: ${loAnalysis.totalOccurrences} lần (${(loAnalysis.frequency * 100).toFixed(2)}%)\n`;

    if (loAnalysis.lastSeen) {
      const lastSeenDate = new Date(loAnalysis.lastSeen).toLocaleDateString('vi-VN');
      message += `• Lần cuối: ${lastSeenDate}`;
      if (loAnalysis.daysSinceLastSeen !== null) {
        message += ` (${loAnalysis.daysSinceLastSeen} ngày trước)`;
      }
      message += '\n';
    } else {
      message += '• Chưa từng xuất hiện\n';
    }

    if (loAnalysis.trends && loAnalysis.trends.short) {
      const trend = loAnalysis.trends.short;
      const trendEmoji = trend.trend === 'hot' ? '🔥' : trend.trend === 'cold' ? '❄️' : '⚖️';
      message += `• Xu hướng: ${trendEmoji} ${trend.trend} (${trend.occurrences} lần/7 kỳ)\n`;
    }

    message += '\n';

    // De analysis
    message += `🎯 ${MessageFormatter.bold('Phân Tích Đề:')}\n`;
    message += `• Xuất hiện: ${deAnalysis.totalOccurrences} lần (${(deAnalysis.frequency * 100).toFixed(2)}%)\n`;

    if (deAnalysis.lastSeen) {
      const lastSeenDate = new Date(deAnalysis.lastSeen).toLocaleDateString('vi-VN');
      message += `• Lần cuối: ${lastSeenDate}`;
      if (deAnalysis.daysSinceLastSeen !== null) {
        message += ` (${deAnalysis.daysSinceLastSeen} ngày trước)`;
      }
      message += '\n';
    } else {
      message += '• Chưa từng xuất hiện\n';
    }

    if (deAnalysis.trends && deAnalysis.trends.short) {
      const trend = deAnalysis.trends.short;
      const trendEmoji = trend.trend === 'hot' ? '🔥' : trend.trend === 'cold' ? '❄️' : '⚖️';
      message += `• Xu hướng: ${trendEmoji} ${trend.trend} (${trend.occurrences} lần/7 kỳ)\n`;
    }

    // Recommendations
    const allRecommendations = [...(loAnalysis.recommendations || []), ...(deAnalysis.recommendations || [])];
    if (allRecommendations.length > 0) {
      message += `\n💡 ${MessageFormatter.bold('Khuyến Nghị:')}\n`;
      allRecommendations.slice(0, 3).forEach((rec, index) => {
        const confidenceEmoji = rec.confidence >= 70 ? '🟢' : rec.confidence >= 50 ? '🟡' : '🔴';
        message += `${index + 1}. ${rec.message} ${confidenceEmoji}\n`;
      });
    }

    message += '\n📝 Lưu ý: Phân tích dựa trên dữ liệu lịch sử, chỉ mang tính tham khảo.';

    return MessageFormatter.truncateMessage(message);
  }

  /**
   * Format detailed analysis
   */
  _formatDetailedAnalysis(number, loAnalysis, deAnalysis) {
    let message = `📊 ${MessageFormatter.bold(`Phân Tích Chi Tiết Số ${MessageFormatter.formatNumber(number)}`)}\n\n`;

    // Detailed Lo analysis
    message += `🎲 ${MessageFormatter.bold('Chi Tiết Lô:')}\n`;
    message += `• Tổng xuất hiện: ${loAnalysis.totalOccurrences} lần\n`;
    message += `• Tần suất: ${(loAnalysis.frequency * 100).toFixed(3)}%\n`;
    message += `• Tần suất kỳ vọng: ${(loAnalysis.expectedFrequency * 100).toFixed(1)}%\n`;
    message += `• Tỷ lệ so với kỳ vọng: ${(loAnalysis.relativeFrequency * 100).toFixed(1)}%\n`;

    if (loAnalysis.trends) {
      message += `\n📈 Xu hướng theo thời gian:\n`;
      Object.entries(loAnalysis.trends).forEach(([period, trend]) => {
        if (trend && typeof trend === 'object') {
          const periodName = period === 'short' ? '7 ngày' : period === 'medium' ? '30 ngày' : '90 ngày';
          message += `• ${periodName}: ${trend.occurrences} lần (${trend.trend})\n`;
        }
      });
    }

    message += `\n🎯 ${MessageFormatter.bold('Chi Tiết Đề:')}\n`;
    message += `• Tổng xuất hiện: ${deAnalysis.totalOccurrences} lần\n`;
    message += `• Tần suất: ${(deAnalysis.frequency * 100).toFixed(3)}%\n`;
    message += `• Tần suất kỳ vọng: ${(deAnalysis.expectedFrequency * 100).toFixed(1)}%\n`;
    message += `• Tỷ lệ so với kỳ vọng: ${(deAnalysis.relativeFrequency * 100).toFixed(1)}%\n`;

    return MessageFormatter.truncateMessage(message);
  }

  /**
   * Format trend analysis
   */
  _formatTrendAnalysis(number, loAnalysis, deAnalysis) {
    let message = `📈 ${MessageFormatter.bold(`Xu Hướng Số ${MessageFormatter.formatNumber(number)}`)}\n\n`;

    if (loAnalysis.trends) {
      message += `🎲 ${MessageFormatter.bold('Xu Hướng Lô:')}\n`;
      Object.entries(loAnalysis.trends).forEach(([period, trend]) => {
        if (trend && typeof trend === 'object') {
          const periodName = period === 'short' ? 'Tuần qua' : period === 'medium' ? 'Tháng qua' : 'Quý qua';
          const trendEmoji = trend.trend === 'hot' ? '🔥' : trend.trend === 'cold' ? '❄️' : '⚖️';
          message += `• ${periodName}: ${trendEmoji} ${trend.trend} (${trend.occurrences} lần)\n`;
        }
      });
    }

    if (deAnalysis.trends) {
      message += `\n🎯 ${MessageFormatter.bold('Xu Hướng Đề:')}\n`;
      Object.entries(deAnalysis.trends).forEach(([period, trend]) => {
        if (trend && typeof trend === 'object') {
          const periodName = period === 'short' ? 'Tuần qua' : period === 'medium' ? 'Tháng qua' : 'Quý qua';
          const trendEmoji = trend.trend === 'hot' ? '🔥' : trend.trend === 'cold' ? '❄️' : '⚖️';
          message += `• ${periodName}: ${trendEmoji} ${trend.trend} (${trend.occurrences} lần)\n`;
        }
      });
    }

    return MessageFormatter.truncateMessage(message);
  }

  /**
   * Format correlation analysis
   */
  _formatCorrelationAnalysis(number, loAnalysis, deAnalysis) {
    let message = `🔗 ${MessageFormatter.bold(`Tương Quan Số ${MessageFormatter.formatNumber(number)}`)}\n\n`;

    if (loAnalysis.correlations && loAnalysis.correlations.length > 0) {
      message += `🎲 ${MessageFormatter.bold('Tương Quan Lô:')}\n`;
      loAnalysis.correlations.slice(0, 5).forEach((corr, index) => {
        const strength = Math.abs(corr.correlation);
        const strengthEmoji = strength >= 0.7 ? '🟢' : strength >= 0.5 ? '🟡' : '🔴';
        message += `${index + 1}. Số ${MessageFormatter.formatNumber(corr.number)} ${strengthEmoji} (${(corr.correlation * 100).toFixed(1)}%)\n`;
      });
    } else {
      message += `🎲 ${MessageFormatter.bold('Tương Quan Lô:')}\nKhông tìm thấy tương quan đáng kể.\n`;
    }

    if (deAnalysis.correlations && deAnalysis.correlations.length > 0) {
      message += `\n🎯 ${MessageFormatter.bold('Tương Quan Đề:')}\n`;
      deAnalysis.correlations.slice(0, 5).forEach((corr, index) => {
        const strength = Math.abs(corr.correlation);
        const strengthEmoji = strength >= 0.7 ? '🟢' : strength >= 0.5 ? '🟡' : '🔴';
        message += `${index + 1}. Số ${MessageFormatter.formatNumber(corr.number)} ${strengthEmoji} (${(corr.correlation * 100).toFixed(1)}%)\n`;
      });
    } else {
      message += `\n🎯 ${MessageFormatter.bold('Tương Quan Đề:')}\nKhông tìm thấy tương quan đáng kể.\n`;
    }

    return MessageFormatter.truncateMessage(message);
  }

  /**
   * Create inline keyboard for analysis
   */
  _createAnalysisKeyboard(number) {
    return {
      inline_keyboard: [
        [
          { text: '📊 Chi tiết', callback_data: `analysis:${number}:detailed` },
          { text: '📈 Xu hướng', callback_data: `analysis:${number}:trends` }
        ],
        [
          { text: '🔗 Tương quan', callback_data: `analysis:${number}:correlations` },
          { text: '🔄 Làm mới', callback_data: `analysis:${number}:summary` }
        ]
      ]
    };
  }
}

module.exports = AnalysisHandler;