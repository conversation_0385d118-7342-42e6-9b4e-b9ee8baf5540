const TelegramBot = require('node-telegram-bot-api');
const logger = require('../logger')('logs');
const authMiddleware = require('./middleware/auth');
const rateLimitMiddleware = require('./middleware/rateLimit');
const loggingMiddleware = require('./middleware/logging');

// Bot Handler - Main Telegram bot interface
// Handles user interactions, command routing, and session management
class BotHandler {
  constructor(token, options = {}) {
    if (!token) {
      throw new Error('Bot token is required');
    }

    this.token = token;
    this.options = {
      polling: true,
      ...options
    };
    this.bot = null;
    this.commands = new Map();
    this.middleware = [];
    this.isStarted = false;

    // Bind methods to preserve context
    this.handleMessage = this.handleMessage.bind(this);
    this.handleCallbackQuery = this.handleCallbackQuery.bind(this);
  }

  async start() {
    try {
      if (this.isStarted) {
        logger.logError('Bot is already started');
        return;
      }

      // Initialize bot instance
      this.bot = new TelegramBot(this.token, this.options);

      // Set up error handling
      this.bot.on('error', (error) => {
        logger.logError('Telegram bot error:', error);
      });

      this.bot.on('polling_error', (error) => {
        logger.logError('Telegram polling error:', error);
      });

      // Set up message handlers
      this.bot.on('message', this.handleMessage);
      this.bot.on('callback_query', this.handleCallbackQuery);

      // Register default middleware
      this.use(loggingMiddleware);
      this.use(rateLimitMiddleware);
      this.use(authMiddleware);

      this.isStarted = true;
      logger.logInfo('Telegram bot started successfully');

      return this.bot;
    } catch (error) {
      logger.logError('Failed to start bot:', error);
      throw error;
    }
  }

  async stop() {
    try {
      if (!this.isStarted || !this.bot) {
        logger.logError('Bot is not started');
        return;
      }

      await this.bot.stopPolling();
      this.bot = null;
      this.isStarted = false;

      logger.logInfo('Telegram bot stopped successfully');
    } catch (error) {
      logger.logError('Failed to stop bot:', error);
      throw error;
    }
  }

  use(middleware) {
    if (typeof middleware !== 'function') {
      throw new Error('Middleware must be a function');
    }
    this.middleware.push(middleware);
  }

  registerCommand(command, handler) {
    if (!command || typeof command !== 'string') {
      throw new Error('Command must be a non-empty string');
    }

    if (typeof handler !== 'function') {
      throw new Error('Handler must be a function');
    }

    // Remove leading slash if present
    const cleanCommand = command.startsWith('/') ? command.slice(1) : command;
    this.commands.set(cleanCommand, handler);

    logger.logInfo(`Registered command: /${cleanCommand}`);
  }

  async handleMessage(msg) {
    try {
      const ctx = {
        message: msg,
        bot: this.bot,
        chatId: msg.chat.id,
        userId: msg.from?.id,
        text: msg.text,
        isCommand: msg.text?.startsWith('/'),
        command: null,
        args: []
      };

      // Parse command and arguments
      if (ctx.isCommand) {
        const parts = msg.text.split(' ');
        const commandPart = parts[0].slice(1); // Remove leading slash
        const atIndex = commandPart.indexOf('@');

        // Handle commands with bot username (e.g., /start@botname)
        ctx.command = atIndex > -1 ? commandPart.slice(0, atIndex) : commandPart;
        ctx.args = parts.slice(1);
      }

      // Run middleware chain
      await this.runMiddleware(ctx);

      // Handle command if present
      if (ctx.isCommand && this.commands.has(ctx.command)) {
        const handler = this.commands.get(ctx.command);
        await handler(ctx);
      } else if (ctx.isCommand) {
        // Unknown command
        await this.sendMessage(ctx.chatId,
          `Lệnh không hợp lệ: /${ctx.command}\nGửi /help để xem danh sách lệnh có sẵn.`
        );
      }
    } catch (error) {
      logger.logError('Error handling message:', error);

      // Send error message to user
      try {
        await this.sendMessage(msg.chat.id,
          'Đã xảy ra lỗi khi xử lý tin nhắn của bạn. Vui lòng thử lại sau.'
        );
      } catch (sendError) {
        logger.logError('Failed to send error message:', sendError);
      }
    }
  }

  async handleCallbackQuery(query) {
    try {
      const ctx = {
        callbackQuery: query,
        bot: this.bot,
        chatId: query.message?.chat?.id,
        userId: query.from?.id,
        data: query.data
      };

      // Run middleware chain
      await this.runMiddleware(ctx);

      // Answer callback query to remove loading state
      await this.bot.answerCallbackQuery(query.id);

      // Emit callback query event for handlers to listen
      this.bot.emit('callback_query_handled', ctx);

    } catch (error) {
      logger.logError('Error handling callback query:', error);

      try {
        await this.bot.answerCallbackQuery(query.id, {
          text: 'Đã xảy ra lỗi khi xử lý yêu cầu.',
          show_alert: true
        });
      } catch (answerError) {
        logger.logError('Failed to answer callback query:', answerError);
      }
    }
  }

  async runMiddleware(ctx) {
    for (const middleware of this.middleware) {
      try {
        await middleware(ctx);
      } catch (error) {
        logger.logError('Middleware error:', error);
        throw error;
      }
    }
  }

  async sendMessage(chatId, message, options = {}) {
    try {
      if (!this.bot) {
        throw new Error('Bot is not initialized');
      }

      const defaultOptions = {
        parse_mode: 'HTML',
        disable_web_page_preview: true
      };

      const result = await this.bot.sendMessage(chatId, message, {
        ...defaultOptions,
        ...options
      });

      return result;
    } catch (error) {
      logger.logError('Failed to send message:', error);
      throw error;
    }
  }

  async sendScheduledMessage(groupIds, message, options = {}) {
    const results = [];
    const errors = [];

    for (const groupId of groupIds) {
      try {
        const result = await this.sendMessage(groupId, message, options);
        results.push({ groupId, success: true, result });
      } catch (error) {
        logger.logError(`Failed to send scheduled message to group ${groupId}:`, error);
        errors.push({ groupId, error: error.message });
      }
    }

    return { results, errors };
  }

  async editMessage(chatId, messageId, text, options = {}) {
    try {
      if (!this.bot) {
        throw new Error('Bot is not initialized');
      }

      const defaultOptions = {
        parse_mode: 'HTML',
        disable_web_page_preview: true
      };

      return await this.bot.editMessageText(text, {
        chat_id: chatId,
        message_id: messageId,
        ...defaultOptions,
        ...options
      });
    } catch (error) {
      logger.logError('Failed to edit message:', error);
      throw error;
    }
  }

  async deleteMessage(chatId, messageId) {
    try {
      if (!this.bot) {
        throw new Error('Bot is not initialized');
      }

      return await this.bot.deleteMessage(chatId, messageId);
    } catch (error) {
      logger.logError('Failed to delete message:', error);
      throw error;
    }
  }

  getBot() {
    return this.bot;
  }

  isRunning() {
    return this.isStarted && this.bot !== null;
  }
}

module.exports = BotHandler;