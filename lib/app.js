// Main Application - Initializes and coordinates all services
// Entry point for the lottery prediction bot

const BotHandler = require('./bot');
const PredictionEngine = require('./prediction');
const DataManager = require('./data');
const LLMService = require('./llm');
const Scheduler = require('./scheduler');

class LotteryPredictionApp {
  constructor(config = {}) {
    this.config = config;
    this.services = {};
    this.isInitialized = false;
    this.isRunning = false;
  }

  async initialize() {
    console.log('🚀 Initializing Lottery Prediction Bot...');

    try {
      // Initialize core services
      await this.initializeDataManager();
      await this.initializePredictionEngine();
      await this.initializeLLMService();
      await this.initializeBotHandler();
      await this.initializeScheduler();

      // Setup service dependencies
      this.setupServiceDependencies();

      this.isInitialized = true;
      console.log('✅ Application initialized successfully');

    } catch (error) {
      console.error('❌ Application initialization failed:', error.message);
      throw error;
    }
  }

  async initializeDataManager() {
    console.log('📊 Initializing Data Manager...');

    this.services.dataManager = new DataManager({
      mongodb: this.config.mongodb,
      redis: this.config.redis
    });

    await this.services.dataManager.initialize();
  }

  async initializePredictionEngine() {
    console.log('🧠 Initializing Prediction Engine...');

    this.services.predictionEngine = new PredictionEngine(
      this.services.dataManager,
      this.config.ml
    );

    await this.services.predictionEngine.initialize();
  }

  async initializeLLMService() {
    console.log('🤖 Initializing LLM Service...');

    this.services.llmService = new LLMService({
      provider: 'heyai',
      apiKey: this.config.openai?.apiKey,
      model: this.config.openai?.model || 'gpt-3.5-turbo',
      url: this.config.openai?.url || 'https://api.openai.com/v1',
      dailyBudget: this.config.openai?.dailyBudget || 50
    });

    await this.services.llmService.initialize();
  }

  async initializeBotHandler() {
    console.log('🤖 Initializing Bot Handler...');

    this.services.botHandler = new BotHandler(
      this.config.telegram?.botToken,
      {
        webhook: this.config.telegram?.webhook,
        polling: this.config.telegram?.polling !== false
      }
    );

    // Register command handlers
    this.registerBotCommands();
  }

  async initializeScheduler() {
    console.log('⏰ Initializing Scheduler...');

    this.services.scheduler = new Scheduler({
      timezone: 'Asia/Ho_Chi_Minh'
    });

    // Register scheduled jobs
    this.registerScheduledJobs();

    await this.services.scheduler.initialize();
  }

  setupServiceDependencies() {
    // Setup cross-service dependencies
    console.log('🔗 Setting up service dependencies...');

    // Prediction engine needs data manager
    // Bot handlers need prediction engine and LLM service
    // Scheduler needs all services for jobs
  }

  registerBotCommands() {
    // Register bot command handlers
    const { botHandler, predictionEngine, dataManager, llmService } = this.services;

    // Command registration will be implemented in later tasks
    console.log('📝 Bot commands registered');
  }

  registerScheduledJobs() {
    // Register scheduled jobs
    const { scheduler, botHandler, predictionEngine, dataManager, llmService } = this.services;

    // Job registration will be implemented in later tasks
    console.log('📅 Scheduled jobs registered');
  }

  async start() {
    if (!this.isInitialized) {
      throw new Error('Application not initialized. Call initialize() first.');
    }

    console.log('🚀 Starting Lottery Prediction Bot...');

    try {
      // Start bot
      await this.services.botHandler.start();

      // Start scheduler
      if (this.config.scheduler?.enabled !== false) {
        await this.services.scheduler.start();
      }

      this.isRunning = true;
      console.log('✅ Bot is running and ready to serve predictions!');

    } catch (error) {
      console.error('❌ Failed to start application:', error.message);
      throw error;
    }
  }

  async stop() {
    console.log('🛑 Stopping Lottery Prediction Bot...');

    try {
      // Stop scheduler
      if (this.services.scheduler) {
        await this.services.scheduler.stop();
      }

      // Stop bot
      if (this.services.botHandler) {
        await this.services.botHandler.stop();
      }

      this.isRunning = false;
      console.log('✅ Bot stopped successfully');

    } catch (error) {
      console.error('❌ Error stopping application:', error.message);
      throw error;
    }
  }

  async restart() {
    console.log('🔄 Restarting application...');

    await this.stop();
    await this.start();
  }

  getStatus() {
    return {
      initialized: this.isInitialized,
      running: this.isRunning,
      services: {
        dataManager: !!this.services.dataManager,
        predictionEngine: !!this.services.predictionEngine,
        llmService: !!this.services.llmService,
        botHandler: !!this.services.botHandler,
        scheduler: !!this.services.scheduler
      },
      uptime: this.isRunning ? process.uptime() : 0
    };
  }

  getService(name) {
    return this.services[name];
  }

  async healthCheck() {
    const health = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      services: {}
    };

    // Check each service health
    for (const [name, service] of Object.entries(this.services)) {
      try {
        // Each service should implement a health check method
        health.services[name] = {
          status: 'healthy',
          details: service.getStatus ? await service.getStatus() : 'running'
        };
      } catch (error) {
        health.services[name] = {
          status: 'unhealthy',
          error: error.message
        };
        health.status = 'degraded';
      }
    }

    return health;
  }
}

module.exports = LotteryPredictionApp;