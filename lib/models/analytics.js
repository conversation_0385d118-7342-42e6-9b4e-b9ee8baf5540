const mongoose = require('mongoose');
const mongoConnections = require('../connections/mongo');

const AnalyticsSchema = new mongoose.Schema({
  date: {
    type: Date,
    required: true,
    index: true
  },
  type: {
    type: String,
    required: true,
    enum: ['daily', 'weekly', 'monthly'],
    index: true
  },
  period: {
    year: {
      type: Number,
      required: true,
      index: true
    },
    month: {
      type: Number,
      min: 1,
      max: 12,
      index: true
    },
    week: {
      type: Number,
      min: 1,
      max: 53,
      index: true
    },
    day: {
      type: Number,
      min: 1,
      max: 31,
      index: true
    }
  },
  metrics: {
    totalUsers: {
      type: Number,
      default: 0,
      min: 0
    },
    activeUsers: {
      type: Number,
      default: 0,
      min: 0
    },
    newUsers: {
      type: Number,
      default: 0,
      min: 0
    },
    totalGroups: {
      type: Number,
      default: 0,
      min: 0
    },
    activeGroups: {
      type: Number,
      default: 0,
      min: 0
    },
    totalQueries: {
      type: Number,
      default: 0,
      min: 0
    },
    popularNumbers: [{
      number: {
        type: String,
        required: true,
        validate: {
          validator: function(v) {
            return /^\d{2,3}$/.test(v);
          },
          message: 'Number must be 2-3 digits'
        }
      },
      count: {
        type: Number,
        required: true,
        min: 0
      },
      type: {
        type: String,
        enum: ['lo', 'de'],
        required: true
      }
    }],
    commandUsage: {
      dukienlo: { type: Number, default: 0 },
      dukiende: { type: Number, default: 0 },
      lichsu: { type: Number, default: 0 },
      xuhuonglo: { type: Number, default: 0 },
      xuhuongde: { type: Number, default: 0 },
      number: { type: Number, default: 0 },
      help: { type: Number, default: 0 }
    },
    predictionAccuracy: {
      lo: {
        average: { type: Number, min: 0, max: 100 },
        best: { type: Number, min: 0, max: 100 },
        worst: { type: Number, min: 0, max: 100 },
        count: { type: Number, default: 0 }
      },
      de: {
        average: { type: Number, min: 0, max: 100 },
        best: { type: Number, min: 0, max: 100 },
        worst: { type: Number, min: 0, max: 100 },
        count: { type: Number, default: 0 }
      }
    },
    modelPerformance: [{
      modelVersion: {
        type: String,
        required: true
      },
      type: {
        type: String,
        enum: ['lo', 'de'],
        required: true
      },
      accuracy: {
        type: Number,
        min: 0,
        max: 100
      },
      predictions: {
        type: Number,
        default: 0
      }
    }],
    systemMetrics: {
      averageResponseTime: { type: Number, min: 0 }, // in milliseconds
      errorRate: { type: Number, min: 0, max: 100 }, // percentage
      uptime: { type: Number, min: 0, max: 100 }, // percentage
      cacheHitRate: { type: Number, min: 0, max: 100 } // percentage
    }
  },
  createdAt: {
    type: Date,
    default: Date.now,
    index: true
  }
}, {
  id: false,
  versionKey: false
});

// Compound indexes for performance
AnalyticsSchema.index({ date: -1, type: 1 });
AnalyticsSchema.index({ type: 1, 'period.year': -1, 'period.month': -1 });
AnalyticsSchema.index({ type: 1, 'period.year': -1, 'period.week': -1 });
AnalyticsSchema.index({ 'metrics.popularNumbers.number': 1 });

// Ensure unique analytics record per date and type
AnalyticsSchema.index({ date: 1, type: 1 }, { unique: true });

// Method to add popular number
AnalyticsSchema.methods.addPopularNumber = function(number, type, count = 1) {
  const existing = this.metrics.popularNumbers.find(item =>
    item.number === number && item.type === type
  );

  if (existing) {
    existing.count += count;
  } else {
    this.metrics.popularNumbers.push({ number, type, count });
  }

  // Sort by count and keep top 20
  this.metrics.popularNumbers.sort((a, b) => b.count - a.count);
  this.metrics.popularNumbers = this.metrics.popularNumbers.slice(0, 20);
};

// Method to update command usage
AnalyticsSchema.methods.incrementCommand = function(command, count = 1) {
  if (this.metrics.commandUsage.hasOwnProperty(command)) {
    this.metrics.commandUsage[command] += count;
    this.metrics.totalQueries += count;
  }
};

// Method to update prediction accuracy
AnalyticsSchema.methods.updatePredictionAccuracy = function(type, accuracy) {
  const predAccuracy = this.metrics.predictionAccuracy[type];

  if (!predAccuracy.average) {
    predAccuracy.average = accuracy;
    predAccuracy.best = accuracy;
    predAccuracy.worst = accuracy;
    predAccuracy.count = 1;
  } else {
    // Calculate new average
    const totalAccuracy = predAccuracy.average * predAccuracy.count + accuracy;
    predAccuracy.count++;
    predAccuracy.average = totalAccuracy / predAccuracy.count;

    // Update best and worst
    predAccuracy.best = Math.max(predAccuracy.best, accuracy);
    predAccuracy.worst = Math.min(predAccuracy.worst, accuracy);
  }
};

// Method to update model performance
AnalyticsSchema.methods.updateModelPerformance = function(modelVersion, type, accuracy) {
  let modelPerf = this.metrics.modelPerformance.find(m =>
    m.modelVersion === modelVersion && m.type === type
  );

  if (!modelPerf) {
    modelPerf = {
      modelVersion,
      type,
      accuracy,
      predictions: 1
    };
    this.metrics.modelPerformance.push(modelPerf);
  } else {
    // Calculate new average accuracy
    const totalAccuracy = modelPerf.accuracy * modelPerf.predictions + accuracy;
    modelPerf.predictions++;
    modelPerf.accuracy = totalAccuracy / modelPerf.predictions;
  }
};

// Static method to get analytics for date range
AnalyticsSchema.statics.getAnalyticsForPeriod = function(type, fromDate, toDate) {
  const query = { type };

  if (fromDate || toDate) {
    query.date = {};
    if (fromDate) query.date.$gte = fromDate;
    if (toDate) query.date.$lte = toDate;
  }

  return this.find(query).sort({ date: -1 });
};

// Static method to aggregate metrics
AnalyticsSchema.statics.aggregateMetrics = function(type, year, month = null) {
  const matchQuery = {
    type,
    'period.year': year
  };

  if (month) {
    matchQuery['period.month'] = month;
  }

  return this.aggregate([
    { $match: matchQuery },
    {
      $group: {
        _id: null,
        totalUsers: { $sum: '$metrics.totalUsers' },
        totalQueries: { $sum: '$metrics.totalQueries' },
        avgAccuracyLo: { $avg: '$metrics.predictionAccuracy.lo.average' },
        avgAccuracyDe: { $avg: '$metrics.predictionAccuracy.de.average' },
        avgResponseTime: { $avg: '$metrics.systemMetrics.averageResponseTime' }
      }
    }
  ]);
};

module.exports = mongoConnections('master').model('Analytics', AnalyticsSchema);