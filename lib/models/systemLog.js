const mongoose = require('mongoose');
const mongoConnections = require('../connections/mongo');

const SystemLogSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    index: true
  },
  action: {
    type: String,
    required: true,
    enum: [
      'user_created', 'user_updated', 'user_deleted',
      'group_created', 'group_updated', 'group_deleted',
      'prediction_generated', 'prediction_verified',
      'model_trained', 'model_updated',
      'data_collected', 'cache_cleared',
      'error_occurred', 'system_started', 'system_stopped'
    ],
    index: true
  },
  description: {
    type: String,
    required: true,
    maxlength: 1000
  },
  level: {
    type: String,
    enum: ['info', 'warn', 'error', 'debug'],
    default: 'info',
    index: true
  },
  data: {
    type: mongoose.Schema.Types.Mixed
  },
  updatedData: {
    type: mongoose.Schema.Types.Mixed
  },
  metadata: {
    ip: String,
    userAgent: String,
    telegramId: Number,
    chatId: Number,
    source: {
      type: String,
      enum: ['bot', 'scheduler', 'api', 'system'],
      default: 'system'
    }
  },
  createdAt: {
    type: Date,
    default: Date.now,
    index: true
  }
}, {
  id: false,
  versionKey: false
});

// Indexes for performance
SystemLogSchema.index({ action: 1, createdAt: -1 });
SystemLogSchema.index({ level: 1, createdAt: -1 });
SystemLogSchema.index({ 'metadata.source': 1, createdAt: -1 });
SystemLogSchema.index({ user: 1, createdAt: -1 });

// Static method to log system events
SystemLogSchema.statics.logEvent = function(action, description, options = {}) {
  const logData = {
    action,
    description,
    level: options.level || 'info',
    user: options.user || null,
    data: options.data || null,
    updatedData: options.updatedData || null,
    metadata: {
      ip: options.ip,
      userAgent: options.userAgent,
      telegramId: options.telegramId,
      chatId: options.chatId,
      source: options.source || 'system'
    }
  };

  return this.create(logData);
};

// Static method to get logs by criteria
SystemLogSchema.statics.getLogs = function(criteria = {}, options = {}) {
  const query = this.find(criteria);

  if (options.populate) {
    query.populate('user', 'telegramId firstName lastName username');
  }

  if (options.limit) {
    query.limit(options.limit);
  }

  if (options.sort) {
    query.sort(options.sort);
  } else {
    query.sort({ createdAt: -1 });
  }

  return query;
};

// Static method to clean old logs
SystemLogSchema.statics.cleanOldLogs = function(daysToKeep = 30) {
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

  return this.deleteMany({
    createdAt: { $lt: cutoffDate },
    level: { $ne: 'error' } // Keep error logs longer
  });
};

module.exports = mongoConnections('master').model('SystemLog', SystemLogSchema);