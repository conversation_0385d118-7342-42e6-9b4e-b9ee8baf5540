const mongoose = require('mongoose');
const mongoConnections = require('../connections/mongo');

const GroupSchema = new mongoose.Schema({
  telegramId: {
    type: Number,
    required: true,
    unique: true,
    index: true
  },
  title: {
    type: String,
    required: true
  },
  type: {
    type: String,
    required: true,
    enum: ['group', 'supergroup', 'channel'],
    index: true
  },
  description: {
    type: String,
    maxlength: 500
  },
  settings: {
    dailyPredictions: {
      type: Boolean,
      default: false
    },
    predictionTime: {
      type: String,
      default: '08:00',
      validate: {
        validator: function(v) {
          return /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.test(v);
        },
        message: 'Prediction time must be in HH:MM format'
      }
    },
    weeklyReports: {
      type: Boolean,
      default: false
    },
    weeklyReportDay: {
      type: Number,
      default: 1, // Monday
      min: 0,
      max: 6
    },
    language: {
      type: String,
      default: 'vi',
      enum: ['vi', 'en']
    },
    timezone: {
      type: String,
      default: 'Asia/Ho_Chi_Minh'
    },
    enabledCommands: [{
      type: String,
      enum: ['dukienlo', 'dukiende', 'lichsu', 'xuhuonglo', 'xuhuongde', 'number', 'help'],
      default: ['dukienlo', 'dukiende', 'lichsu', 'xuhuonglo', 'xuhuongde', 'number', 'help']
    }],
    predictionTypes: [{
      type: String,
      enum: ['lo', 'de'],
      default: ['lo', 'de']
    }],
    maxPredictionsPerMessage: {
      type: Number,
      default: 5,
      min: 1,
      max: 20
    }
  },
  statistics: {
    memberCount: {
      type: Number,
      default: 0,
      min: 0
    },
    totalMessages: {
      type: Number,
      default: 0,
      min: 0
    },
    lastActivity: {
      type: Date,
      default: Date.now,
      index: true
    },
    commandUsage: {
      dukienlo: { type: Number, default: 0 },
      dukiende: { type: Number, default: 0 },
      lichsu: { type: Number, default: 0 },
      xuhuonglo: { type: Number, default: 0 },
      xuhuongde: { type: Number, default: 0 },
      number: { type: Number, default: 0 },
      help: { type: Number, default: 0 }
    },
    joinDate: {
      type: Date,
      default: Date.now
    }
  },
  admins: [{
    telegramId: {
      type: Number,
      required: true
    },
    username: String,
    firstName: String,
    addedAt: {
      type: Date,
      default: Date.now
    }
  }],
  isActive: {
    type: Boolean,
    default: true,
    index: true
  },
  isBlocked: {
    type: Boolean,
    default: false,
    index: true
  },
  createdAt: {
    type: Date,
    default: Date.now,
    index: true
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  id: false,
  versionKey: false
});

// Indexes for performance
GroupSchema.index({ telegramId: 1 }, { unique: true });
GroupSchema.index({ type: 1, isActive: 1 });
GroupSchema.index({ 'statistics.lastActivity': -1 });
GroupSchema.index({ 'settings.dailyPredictions': 1, isActive: 1 });
GroupSchema.index({ createdAt: -1 });

// Pre-save middleware to update updatedAt
GroupSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// Method to increment command usage
GroupSchema.methods.incrementCommand = function(command) {
  if (this.statistics.commandUsage.hasOwnProperty(command)) {
    this.statistics.commandUsage[command]++;
    this.statistics.totalMessages++;
    this.statistics.lastActivity = new Date();
  }
};

// Method to add admin
GroupSchema.methods.addAdmin = function(telegramId, username, firstName) {
  const existingAdmin = this.admins.find(admin => admin.telegramId === telegramId);
  if (!existingAdmin) {
    this.admins.push({
      telegramId,
      username,
      firstName,
      addedAt: new Date()
    });
  }
};

// Method to remove admin
GroupSchema.methods.removeAdmin = function(telegramId) {
  this.admins = this.admins.filter(admin => admin.telegramId !== telegramId);
};

// Method to check if user is admin
GroupSchema.methods.isAdmin = function(telegramId) {
  return this.admins.some(admin => admin.telegramId === telegramId);
};

// Method to update member count
GroupSchema.methods.updateMemberCount = function(count) {
  this.statistics.memberCount = count;
  this.statistics.lastActivity = new Date();
};

// Static method to get groups with daily predictions enabled
GroupSchema.statics.getGroupsWithDailyPredictions = function() {
  return this.find({
    'settings.dailyPredictions': true,
    isActive: true,
    isBlocked: false
  });
};

// Static method to get groups for weekly reports
GroupSchema.statics.getGroupsForWeeklyReports = function(dayOfWeek) {
  return this.find({
    'settings.weeklyReports': true,
    'settings.weeklyReportDay': dayOfWeek,
    isActive: true,
    isBlocked: false
  });
};

module.exports = mongoConnections('master').model('Group', GroupSchema);