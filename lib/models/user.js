const mongoose = require('mongoose');
const mongoConnections = require('../connections/mongo');

const UserSchema = new mongoose.Schema({
  telegramId: {
    type: Number,
    required: true,
    unique: true,
    index: true
  },
  username: {
    type: String,
    index: true
  },
  firstName: {
    type: String,
    required: true
  },
  lastName: {
    type: String
  },
  preferences: {
    notifications: {
      type: Boolean,
      default: true
    },
    favoriteNumbers: [{
      type: String,
      validate: {
        validator: function(v) {
          return /^\d{2,3}$/.test(v);
        },
        message: 'Favorite numbers must be 2-3 digits'
      }
    }],
    timezone: {
      type: String,
      default: 'Asia/Ho_Chi_Minh'
    },
    language: {
      type: String,
      default: 'vi',
      enum: ['vi', 'en']
    },
    predictionTypes: [{
      type: String,
      enum: ['lo', 'de'],
      default: ['lo', 'de']
    }]
  },
  statistics: {
    totalQueries: {
      type: Number,
      default: 0,
      min: 0
    },
    lastActive: {
      type: Date,
      default: Date.now,
      index: true
    },
    mostQueriedNumbers: [{
      number: String,
      count: {
        type: Number,
        default: 0,
        min: 0
      }
    }],
    joinDate: {
      type: Date,
      default: Date.now
    },
    commandUsage: {
      dukienlo: { type: Number, default: 0 },
      dukiende: { type: Number, default: 0 },
      lichsu: { type: Number, default: 0 },
      xuhuonglo: { type: Number, default: 0 },
      xuhuongde: { type: Number, default: 0 },
      number: { type: Number, default: 0 },
      help: { type: Number, default: 0 }
    }
  },
  isActive: {
    type: Boolean,
    default: true,
    index: true
  },
  isBlocked: {
    type: Boolean,
    default: false,
    index: true
  },
  createdAt: {
    type: Date,
    default: Date.now,
    index: true
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  id: false,
  versionKey: false
});

// Indexes for performance
UserSchema.index({ telegramId: 1 }, { unique: true });
UserSchema.index({ 'statistics.lastActive': -1 });
UserSchema.index({ isActive: 1, isBlocked: 1 });
UserSchema.index({ createdAt: -1 });

// Pre-save middleware to update updatedAt
UserSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// Method to increment command usage
UserSchema.methods.incrementCommand = function(command) {
  if (this.statistics.commandUsage.hasOwnProperty(command)) {
    this.statistics.commandUsage[command]++;
    this.statistics.totalQueries++;
    this.statistics.lastActive = new Date();
  }
};

// Method to add favorite number
UserSchema.methods.addFavoriteNumber = function(number) {
  if (!this.preferences.favoriteNumbers.includes(number)) {
    this.preferences.favoriteNumbers.push(number);
  }
};

// Method to track queried number
UserSchema.methods.trackQueriedNumber = function(number) {
  const existing = this.statistics.mostQueriedNumbers.find(item => item.number === number);
  if (existing) {
    existing.count++;
  } else {
    this.statistics.mostQueriedNumbers.push({ number, count: 1 });
  }

  // Keep only top 10 most queried numbers
  this.statistics.mostQueriedNumbers.sort((a, b) => b.count - a.count);
  this.statistics.mostQueriedNumbers = this.statistics.mostQueriedNumbers.slice(0, 10);
};

// Static method to get active users count
UserSchema.statics.getActiveUsersCount = function(days = 7) {
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - days);

  return this.countDocuments({
    'statistics.lastActive': { $gte: cutoffDate },
    isActive: true,
    isBlocked: false
  });
};

module.exports = mongoConnections('master').model('User', UserSchema);
