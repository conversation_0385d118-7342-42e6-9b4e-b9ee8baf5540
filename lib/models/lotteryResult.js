const mongoose = require('mongoose');
const mongoConnections = require('../connections/mongo');

const LotteryResultSchema = new mongoose.Schema({
  date: {
    type: Date,
    required: true,
    index: true
  },
  region: {
    type: String,
    required: true,
    enum: ['north', 'central', 'south'],
    default: 'north'
  },
  prizes: {
    special: {
      type: String,
      required: true,
      validate: {
        validator: function(v) {
          return /^\d{5}$/.test(v);
        },
        message: 'Special prize must be 5 digits'
      }
    },
    first: {
      type: String,
      required: true,
      validate: {
        validator: function(v) {
          return /^\d{5}$/.test(v);
        },
        message: 'First prize must be 5 digits'
      }
    },
    second: [{
      type: String,
      validate: {
        validator: function(v) {
          return /^\d{5}$/.test(v);
        },
        message: 'Second prize must be 5 digits'
      }
    }],
    third: [{
      type: String,
      validate: {
        validator: function(v) {
          return /^\d{5}$/.test(v);
        },
        message: 'Third prize must be 5 digits'
      }
    }],
    fourth: [{
      type: String,
      validate: {
        validator: function(v) {
          return /^\d{4}$/.test(v);
        },
        message: 'Fourth prize must be 4 digits'
      }
    }],
    fifth: [{
      type: String,
      validate: {
        validator: function(v) {
          return /^\d{4}$/.test(v);
        },
        message: 'Fifth prize must be 4 digits'
      }
    }],
    sixth: [{
      type: String,
      validate: {
        validator: function(v) {
          return /^\d{3}$/.test(v);
        },
        message: 'Sixth prize must be 3 digits'
      }
    }],
    seventh: [{
      type: String,
      validate: {
        validator: function(v) {
          return /^\d{2}$/.test(v);
        },
        message: 'Seventh prize must be 2 digits'
      }
    }]
  },
  numbers: {
    lo: [{
      type: String,
      validate: {
        validator: function(v) {
          return /^\d{2}$/.test(v);
        },
        message: 'Lo number must be 2 digits'
      }
    }],
    de: {
      type: String,
      validate: {
        validator: function(v) {
          return /^\d{2}$/.test(v);
        },
        message: 'De number must be 2 digits'
      }
    }
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  id: false,
  versionKey: false
});

// Indexes for performance
LotteryResultSchema.index({ date: -1, region: 1 });
LotteryResultSchema.index({ 'numbers.lo': 1 });
LotteryResultSchema.index({ 'numbers.de': 1 });
LotteryResultSchema.index({ createdAt: -1 });

// Ensure unique combination of date and region
LotteryResultSchema.index({ date: 1, region: 1 }, { unique: true });

// Pre-save middleware to update updatedAt
LotteryResultSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// Method to extract all lo numbers from prizes
LotteryResultSchema.methods.extractLoNumbers = function() {
  const loNumbers = new Set();

  // Extract last 2 digits from all prizes
  if (this.prizes.special) loNumbers.add(this.prizes.special.slice(-2));
  if (this.prizes.first) loNumbers.add(this.prizes.first.slice(-2));

  this.prizes.second.forEach(prize => loNumbers.add(prize.slice(-2)));
  this.prizes.third.forEach(prize => loNumbers.add(prize.slice(-2)));
  this.prizes.fourth.forEach(prize => loNumbers.add(prize.slice(-2)));
  this.prizes.fifth.forEach(prize => loNumbers.add(prize.slice(-2)));
  this.prizes.sixth.forEach(prize => loNumbers.add(prize.slice(-2)));
  this.prizes.seventh.forEach(prize => loNumbers.add(prize));

  return Array.from(loNumbers);
};

// Method to extract de number (last 2 digits of special prize)
LotteryResultSchema.methods.extractDeNumber = function() {
  if (!this.prizes.special) return null;
  return this.prizes.special.slice(-2);
};

module.exports = mongoConnections('master').model('LotteryResult', LotteryResultSchema);