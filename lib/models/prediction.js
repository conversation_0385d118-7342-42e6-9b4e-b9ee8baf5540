const mongoose = require('mongoose');
const mongoConnections = require('../connections/mongo');

const PredictionSchema = new mongoose.Schema({
  date: {
    type: Date,
    required: true,
    index: true
  },
  type: {
    type: String,
    required: true,
    enum: ['lo', 'de'],
    index: true
  },
  predictions: [{
    number: {
      type: String,
      required: true,
      validate: {
        validator: function(v) {
          // Validate based on prediction type
          const parent = this.parent();
          if (parent.type === 'lo') {
            return /^\d{2}$/.test(v);
          } else if (parent.type === 'de') {
            return /^\d{3}$/.test(v);
          }
          return false;
        },
        message: 'Number format must match prediction type (2 digits for lo, 3 for de)'
      }
    },
    confidence: {
      type: Number,
      required: true,
      min: 0,
      max: 100,
      validate: {
        validator: function(v) {
          return v >= 0 && v <= 100;
        },
        message: 'Confidence must be between 0 and 100'
      }
    },
    method: {
      type: String,
      required: true,
      enum: ['lstm', 'statistical', 'ensemble', 'frequency', 'pattern'],
      index: true
    },
    reasoning: {
      type: String,
      maxlength: 500
    },
    probability: {
      type: Number,
      min: 0,
      max: 1
    }
  }],
  accuracy: {
    type: Number,
    min: 0,
    max: 100,
    default: null // Will be calculated after actual results are available
  },
  modelVersion: {
    type: String,
    required: true,
    index: true
  },
  metadata: {
    trainingDataSize: Number,
    processingTime: Number, // in milliseconds
    dataQuality: {
      type: Number,
      min: 0,
      max: 1
    }
  },
  isVerified: {
    type: Boolean,
    default: false,
    index: true
  },
  actualResults: [{
    number: String,
    hit: Boolean
  }],
  createdAt: {
    type: Date,
    default: Date.now,
    index: true
  }
}, {
  id: false,
  versionKey: false
});

// Compound indexes for performance
PredictionSchema.index({ date: -1, type: 1 });
PredictionSchema.index({ date: 1, type: 1, modelVersion: 1 });
PredictionSchema.index({ 'predictions.method': 1, accuracy: -1 });
PredictionSchema.index({ createdAt: -1, isVerified: 1 });

// Ensure unique prediction per date and type
PredictionSchema.index({ date: 1, type: 1 }, { unique: true });

// Method to calculate accuracy after results are available
PredictionSchema.methods.calculateAccuracy = function(actualNumbers) {
  if (!actualNumbers || actualNumbers.length === 0) {
    this.accuracy = 0;
    this.isVerified = true;
    return 0;
  }

  let hits = 0;
  const actualSet = new Set(actualNumbers);

  this.actualResults = this.predictions.map(pred => {
    const hit = actualSet.has(pred.number);
    if (hit) hits++;
    return {
      number: pred.number,
      hit: hit
    };
  });

  this.accuracy = this.predictions.length > 0 ? (hits / this.predictions.length) * 100 : 0;
  this.isVerified = true;

  return this.accuracy;
};

// Method to get top predictions by confidence
PredictionSchema.methods.getTopPredictions = function(limit = 5) {
  return this.predictions
    .sort((a, b) => b.confidence - a.confidence)
    .slice(0, limit);
};

// Method to get predictions by method
PredictionSchema.methods.getPredictionsByMethod = function(method) {
  return this.predictions.filter(pred => pred.method === method);
};

// Static method to get accuracy statistics for a model version
PredictionSchema.statics.getModelAccuracy = function(modelVersion, type, fromDate, toDate) {
  const query = {
    modelVersion: modelVersion,
    type: type,
    isVerified: true,
    accuracy: { $ne: null }
  };

  if (fromDate || toDate) {
    query.date = {};
    if (fromDate) query.date.$gte = fromDate;
    if (toDate) query.date.$lte = toDate;
  }

  return this.aggregate([
    { $match: query },
    {
      $group: {
        _id: null,
        avgAccuracy: { $avg: '$accuracy' },
        minAccuracy: { $min: '$accuracy' },
        maxAccuracy: { $max: '$accuracy' },
        count: { $sum: 1 }
      }
    }
  ]);
};

module.exports = mongoConnections('master').model('Prediction', PredictionSchema);