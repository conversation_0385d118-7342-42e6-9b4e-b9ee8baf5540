// Group Manager - Manages group settings, statistics, and admin controls
// Implements Task 9.2: Group Management System

const GroupRepository = require('../data/repositories/groupRepo');
const mongoConnection = require('../connections/mongo');
const redisConnection = require('../connections/redis');
const logger = require('../logger')('./logs');

class GroupManager {
  constructor(options = {}) {
    this.options = {
      cacheEnabled: true,
      cacheTTL: 21600, // 6 hours for group settings
      maxGroupsPerUser: 50,
      defaultSettings: {
        dailyPredictions: false,
        predictionTime: '08:00',
        weeklyReports: false,
        weeklyReportDay: 1, // Monday
        language: 'vi',
        timezone: 'Asia/Ho_Chi_Minh',
        enabledCommands: ['dukienlo', 'dukiende', 'lichsu', 'xuhuonglo', 'xuhuongde', 'number', 'help'],
        predictionTypes: ['lo', 'de'],
        maxPredictionsPerMessage: 5
      },
      ...options
    };
    
    this.groupRepo = null;
    this.cache = null;
    this.isInitialized = false;
  }

  async initialize() {
    try {
      // Initialize database connection
      const mongoConn = mongoConnection('default');
      if (!mongoConn) {
        throw new Error('MongoDB connection not available');
      }

      this.groupRepo = new GroupRepository(mongoConn.db);
      await this.groupRepo.createIndexes();

      // Initialize Redis cache if enabled
      if (this.options.cacheEnabled) {
        const redisConn = redisConnection('default');
        if (redisConn) {
          this.cache = redisConn.getConnection();
          await this.cache.connect();
        } else {
          logger.logWarn('Redis connection not available, running without cache');
          this.options.cacheEnabled = false;
        }
      }

      this.isInitialized = true;
      logger.logInfo('GroupManager initialized successfully');
    } catch (error) {
      logger.logError('Failed to initialize GroupManager:', error);
      throw error;
    }
  }

  async registerGroup(telegramGroupData) {
    try {
      if (!this.isInitialized) {
        throw new Error('GroupManager not initialized');
      }

      // Validate group data
      this._validateGroupData(telegramGroupData);

      // Check if group already exists
      const existingGroup = await this.groupRepo.findByTelegramId(telegramGroupData.id);
      if (existingGroup) {
        // Reactivate if it was deactivated
        if (!existingGroup.isActive) {
          await this.groupRepo.reactivateGroup(telegramGroupData.id);
          logger.logInfo(`Reactivated group: ${telegramGroupData.id}`);
        }
        return existingGroup;
      }

      // Create new group
      const groupData = {
        telegramId: telegramGroupData.id,
        title: telegramGroupData.title,
        type: telegramGroupData.type,
        username: telegramGroupData.username,
        description: telegramGroupData.description,
        settings: { ...this.options.defaultSettings },
        statistics: {
          memberCount: telegramGroupData.member_count || 0,
          totalMessages: 0,
          lastActivity: new Date(),
          commandUsage: {
            dukienlo: 0,
            dukiende: 0,
            lichsu: 0,
            xuhuonglo: 0,
            xuhuongde: 0,
            number: 0,
            help: 0
          },
          joinDate: new Date()
        },
        admins: [],
        isActive: true,
        isBlocked: false,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      const newGroup = await this.groupRepo.createGroup(groupData);
      
      // Cache the new group settings
      if (this.options.cacheEnabled && this.cache) {
        await this._cacheGroupSettings(newGroup);
      }

      logger.logInfo(`New group registered: ${telegramGroupData.id} - ${telegramGroupData.title}`);
      return newGroup;
    } catch (error) {
      logger.logError('Failed to register group:', error);
      throw error;
    }
  }

  async getGroupById(telegramId) {
    try {
      if (!this.isInitialized) {
        throw new Error('GroupManager not initialized');
      }

      return await this.groupRepo.findByTelegramId(telegramId);
    } catch (error) {
      logger.logError('Failed to get group:', error);
      throw error;
    }
  }

  async getGroupSettings(telegramId) {
    try {
      if (!this.isInitialized) {
        throw new Error('GroupManager not initialized');
      }

      // Try cache first
      if (this.options.cacheEnabled && this.cache) {
        const cached = await this._getCachedGroupSettings(telegramId);
        if (cached) {
          return cached;
        }
      }

      // Get from database
      const settings = await this.groupRepo.getSettings(telegramId);
      
      // Cache the result
      if (settings && this.options.cacheEnabled && this.cache) {
        await this._cacheGroupSettings({ telegramId, settings });
      }

      return settings || this.options.defaultSettings;
    } catch (error) {
      logger.logError('Failed to get group settings:', error);
      return this.options.defaultSettings;
    }
  }

  async updateGroupSettings(telegramId, settings, adminTelegramId = null) {
    try {
      if (!this.isInitialized) {
        throw new Error('GroupManager not initialized');
      }

      // Validate admin permissions if adminTelegramId is provided
      if (adminTelegramId) {
        const isAdmin = await this.isGroupAdmin(telegramId, adminTelegramId);
        if (!isAdmin) {
          throw new Error('User is not authorized to change group settings');
        }
      }

      // Validate settings
      const validatedSettings = this._validateSettings(settings);
      
      // Update in database
      await this.groupRepo.updateSettings(telegramId, validatedSettings);
      
      // Invalidate cache
      if (this.options.cacheEnabled && this.cache) {
        await this._invalidateGroupCache(telegramId);
      }

      logger.logInfo(`Updated settings for group: ${telegramId}`);
      return true;
    } catch (error) {
      logger.logError('Failed to update group settings:', error);
      throw error;
    }
  }

  async logGroupInteraction(telegramId, command, memberCount = null) {
    try {
      if (!this.isInitialized) {
        throw new Error('GroupManager not initialized');
      }

      const updateData = { command };
      if (memberCount !== null) {
        updateData.memberCount = memberCount;
      }

      // Log interaction
      await this.groupRepo.updateStats(telegramId, updateData);
      
      // Invalidate cache to reflect updated stats
      if (this.options.cacheEnabled && this.cache) {
        await this._invalidateGroupCache(telegramId);
      }

      logger.logInfo(`Logged interaction for group ${telegramId}: ${command}`);
    } catch (error) {
      logger.logError('Failed to log group interaction:', error);
      // Don't throw error for logging failures
    }
  }

  async addGroupAdmin(telegramId, adminData, requesterTelegramId = null) {
    try {
      if (!this.isInitialized) {
        throw new Error('GroupManager not initialized');
      }

      // Validate requester permissions if provided
      if (requesterTelegramId) {
        const isAdmin = await this.isGroupAdmin(telegramId, requesterTelegramId);
        if (!isAdmin) {
          throw new Error('User is not authorized to add admins');
        }
      }

      await this.groupRepo.addAdmin(telegramId, adminData);
      
      // Invalidate cache
      if (this.options.cacheEnabled && this.cache) {
        await this._invalidateGroupCache(telegramId);
      }

      logger.logInfo(`Added admin ${adminData.telegramId} to group ${telegramId}`);
      return true;
    } catch (error) {
      logger.logError('Failed to add group admin:', error);
      throw error;
    }
  }

  async removeGroupAdmin(telegramId, adminTelegramId, requesterTelegramId = null) {
    try {
      if (!this.isInitialized) {
        throw new Error('GroupManager not initialized');
      }

      // Validate requester permissions if provided
      if (requesterTelegramId && requesterTelegramId !== adminTelegramId) {
        const isAdmin = await this.isGroupAdmin(telegramId, requesterTelegramId);
        if (!isAdmin) {
          throw new Error('User is not authorized to remove admins');
        }
      }

      await this.groupRepo.removeAdmin(telegramId, adminTelegramId);
      
      // Invalidate cache
      if (this.options.cacheEnabled && this.cache) {
        await this._invalidateGroupCache(telegramId);
      }

      logger.logInfo(`Removed admin ${adminTelegramId} from group ${telegramId}`);
      return true;
    } catch (error) {
      logger.logError('Failed to remove group admin:', error);
      throw error;
    }
  }

  async isGroupAdmin(telegramId, userTelegramId) {
    try {
      if (!this.isInitialized) {
        throw new Error('GroupManager not initialized');
      }

      return await this.groupRepo.isAdmin(telegramId, userTelegramId);
    } catch (error) {
      logger.logError('Failed to check admin status:', error);
      return false;
    }
  }

  async getGroupsForDailyPredictions() {
    try {
      if (!this.isInitialized) {
        throw new Error('GroupManager not initialized');
      }

      return await this.groupRepo.getGroupsWithDailyPredictions();
    } catch (error) {
      logger.logError('Failed to get groups for daily predictions:', error);
      return [];
    }
  }

  async getGroupsForWeeklyReports(dayOfWeek = null) {
    try {
      if (!this.isInitialized) {
        throw new Error('GroupManager not initialized');
      }

      const today = dayOfWeek || new Date().getDay();
      return await this.groupRepo.getGroupsForWeeklyReports(today);
    } catch (error) {
      logger.logError('Failed to get groups for weekly reports:', error);
      return [];
    }
  }

  async getGroupAnalytics() {
    try {
      if (!this.isInitialized) {
        throw new Error('GroupManager not initialized');
      }

      // Try cache first
      if (this.options.cacheEnabled && this.cache) {
        const cacheKey = 'analytics:groups:global';
        const cached = await this.cache.get(cacheKey);
        if (cached) {
          return JSON.parse(cached);
        }
      }

      // Get from database
      const analytics = await this.groupRepo.getGroupAnalytics();
      
      // Cache the result for shorter time (15 minutes)
      if (this.options.cacheEnabled && this.cache) {
        const cacheKey = 'analytics:groups:global';
        await this.cache.setEx(cacheKey, 900, JSON.stringify(analytics));
      }

      return analytics;
    } catch (error) {
      logger.logError('Failed to get group analytics:', error);
      throw error;
    }
  }

  async getCommandUsageStats() {
    try {
      if (!this.isInitialized) {
        throw new Error('GroupManager not initialized');
      }

      return await this.groupRepo.getCommandUsageStats();
    } catch (error) {
      logger.logError('Failed to get command usage stats:', error);
      throw error;
    }
  }

  async deactivateGroup(telegramId, reason = 'removed') {
    try {
      if (!this.isInitialized) {
        throw new Error('GroupManager not initialized');
      }

      await this.groupRepo.deactivateGroup(telegramId, reason);
      
      // Invalidate cache
      if (this.options.cacheEnabled && this.cache) {
        await this._invalidateGroupCache(telegramId);
      }

      logger.logInfo(`Deactivated group: ${telegramId} - Reason: ${reason}`);
      return true;
    } catch (error) {
      logger.logError('Failed to deactivate group:', error);
      throw error;
    }
  }

  // Private helper methods
  async _cacheGroupSettings(group) {
    try {
      const cacheKey = `group:settings:${group.telegramId}`;
      await this.cache.setEx(cacheKey, this.options.cacheTTL, JSON.stringify(group.settings));
    } catch (error) {
      logger.logError('Failed to cache group settings:', error);
    }
  }

  async _getCachedGroupSettings(telegramId) {
    try {
      const cacheKey = `group:settings:${telegramId}`;
      const cached = await this.cache.get(cacheKey);
      return cached ? JSON.parse(cached) : null;
    } catch (error) {
      logger.logError('Failed to get cached group settings:', error);
      return null;
    }
  }

  async _invalidateGroupCache(telegramId) {
    try {
      const settingsKey = `group:settings:${telegramId}`;
      await this.cache.del(settingsKey);
    } catch (error) {
      logger.logError('Failed to invalidate group cache:', error);
    }
  }

  _validateGroupData(groupData) {
    if (!groupData.id || typeof groupData.id !== 'number') {
      throw new Error('Group ID is required and must be a number');
    }
    
    if (!groupData.title || typeof groupData.title !== 'string') {
      throw new Error('Group title is required and must be a string');
    }
    
    if (!groupData.type || !['group', 'supergroup', 'channel'].includes(groupData.type)) {
      throw new Error('Group type must be one of: group, supergroup, channel');
    }
  }

  _validateSettings(settings) {
    const validated = {};
    
    if (settings.dailyPredictions !== undefined) {
      validated.dailyPredictions = Boolean(settings.dailyPredictions);
    }
    
    if (settings.predictionTime && typeof settings.predictionTime === 'string') {
      if (/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.test(settings.predictionTime)) {
        validated.predictionTime = settings.predictionTime;
      }
    }
    
    if (settings.weeklyReports !== undefined) {
      validated.weeklyReports = Boolean(settings.weeklyReports);
    }
    
    if (settings.weeklyReportDay !== undefined) {
      const day = parseInt(settings.weeklyReportDay);
      if (day >= 0 && day <= 6) {
        validated.weeklyReportDay = day;
      }
    }
    
    if (settings.language && typeof settings.language === 'string') {
      validated.language = settings.language;
    }
    
    if (settings.timezone && typeof settings.timezone === 'string') {
      validated.timezone = settings.timezone;
    }
    
    if (settings.enabledCommands && Array.isArray(settings.enabledCommands)) {
      const validCommands = ['dukienlo', 'dukiende', 'lichsu', 'xuhuonglo', 'xuhuongde', 'number', 'help'];
      validated.enabledCommands = settings.enabledCommands.filter(cmd => validCommands.includes(cmd));
    }
    
    if (settings.predictionTypes && Array.isArray(settings.predictionTypes)) {
      validated.predictionTypes = settings.predictionTypes.filter(type => ['lo', 'de'].includes(type));
    }
    
    if (settings.maxPredictionsPerMessage !== undefined) {
      const max = parseInt(settings.maxPredictionsPerMessage);
      if (max >= 1 && max <= 20) {
        validated.maxPredictionsPerMessage = max;
      }
    }

    return validated;
  }

  async cleanup() {
    try {
      if (this.cache && this.cache.isOpen) {
        await this.cache.quit();
      }
      this.isInitialized = false;
      logger.logInfo('GroupManager cleanup completed');
    } catch (error) {
      logger.logError('Error during GroupManager cleanup:', error);
    }
  }
}

module.exports = GroupManager;
