// User Manager - Manages user registration, preferences, and analytics
// Implements Task 9.1: User Tracking System

const UserRepository = require('../data/repositories/userRepo');
const mongoConnection = require('../connections/mongo');
const redisConnection = require('../connections/redis');
const logger = require('../logger')('./logs');

class UserManager {
  constructor(options = {}) {
    this.options = {
      cacheEnabled: true,
      cacheTTL: 3600, // 1 hour
      privacyCompliant: true,
      ...options
    };
    
    this.userRepo = null;
    this.cache = null;
    this.isInitialized = false;
  }

  async initialize() {
    try {
      // Initialize database connection
      const mongoConn = mongoConnection('default');
      if (!mongoConn) {
        throw new Error('MongoDB connection not available');
      }

      this.userRepo = new UserRepository(mongoConn.db);
      await this.userRepo.createIndexes();

      // Initialize Redis cache if enabled
      if (this.options.cacheEnabled) {
        const redisConn = redisConnection('default');
        if (redisConn) {
          this.cache = redisConn.getConnection();
          await this.cache.connect();
        } else {
          logger.logWarn('Redis connection not available, running without cache');
          this.options.cacheEnabled = false;
        }
      }

      this.isInitialized = true;
      logger.logInfo('UserManager initialized successfully');
    } catch (error) {
      logger.logError('Failed to initialize UserManager:', error);
      throw error;
    }
  }

  async registerUser(telegramUserData) {
    try {
      if (!this.isInitialized) {
        throw new Error('UserManager not initialized');
      }

      // Check if user already exists
      const existingUser = await this.userRepo.findByTelegramId(telegramUserData.id);
      if (existingUser) {
        // Update last active and return existing user
        await this.updateLastActive(telegramUserData.id);
        return existingUser;
      }

      // Create new user
      const userData = {
        telegramId: telegramUserData.id,
        username: telegramUserData.username,
        firstName: telegramUserData.first_name,
        lastName: telegramUserData.last_name,
        languageCode: telegramUserData.language_code || 'vi',
        isBot: telegramUserData.is_bot || false,
        preferences: {
          notifications: true,
          favoriteNumbers: [],
          timezone: 'Asia/Ho_Chi_Minh',
          language: telegramUserData.language_code || 'vi'
        },
        statistics: {
          totalQueries: 0,
          lastActive: new Date(),
          mostQueriedNumbers: [],
          joinDate: new Date()
        },
        createdAt: new Date(),
        updatedAt: new Date()
      };

      const newUser = await this.userRepo.create(userData);
      
      // Cache the new user
      if (this.options.cacheEnabled && this.cache) {
        await this._cacheUser(newUser);
      }

      logger.logInfo(`New user registered: ${telegramUserData.id}`);
      return newUser;
    } catch (error) {
      logger.logError('Failed to register user:', error);
      throw error;
    }
  }

  async getUserById(telegramId) {
    try {
      if (!this.isInitialized) {
        throw new Error('UserManager not initialized');
      }

      // Try cache first
      if (this.options.cacheEnabled && this.cache) {
        const cached = await this._getCachedUser(telegramId);
        if (cached) {
          return cached;
        }
      }

      // Get from database
      const user = await this.userRepo.findByTelegramId(telegramId);
      
      // Cache the result
      if (user && this.options.cacheEnabled && this.cache) {
        await this._cacheUser(user);
      }

      return user;
    } catch (error) {
      logger.logError('Failed to get user:', error);
      throw error;
    }
  }

  async updateUserPreferences(telegramId, preferences) {
    try {
      if (!this.isInitialized) {
        throw new Error('UserManager not initialized');
      }

      // Validate preferences
      const validatedPreferences = this._validatePreferences(preferences);
      
      // Update in database
      await this.userRepo.updatePreferences(telegramId, validatedPreferences);
      
      // Invalidate cache
      if (this.options.cacheEnabled && this.cache) {
        await this._invalidateUserCache(telegramId);
      }

      logger.logInfo(`Updated preferences for user: ${telegramId}`);
      return true;
    } catch (error) {
      logger.logError('Failed to update user preferences:', error);
      throw error;
    }
  }

  async logUserInteraction(telegramId, command, metadata = {}) {
    try {
      if (!this.isInitialized) {
        throw new Error('UserManager not initialized');
      }

      // Privacy compliance - anonymize sensitive data if needed
      const sanitizedMetadata = this.options.privacyCompliant 
        ? this._sanitizeMetadata(metadata)
        : metadata;

      // Log interaction
      await this.userRepo.logInteraction(telegramId, command, sanitizedMetadata);
      
      // Invalidate user cache to reflect updated stats
      if (this.options.cacheEnabled && this.cache) {
        await this._invalidateUserCache(telegramId);
      }

      logger.logInfo(`Logged interaction for user ${telegramId}: ${command}`);
    } catch (error) {
      logger.logError('Failed to log user interaction:', error);
      // Don't throw error for logging failures to avoid disrupting user experience
    }
  }

  async getUserStats(telegramId) {
    try {
      if (!this.isInitialized) {
        throw new Error('UserManager not initialized');
      }

      // Try cache first
      if (this.options.cacheEnabled && this.cache) {
        const cacheKey = `user:stats:${telegramId}`;
        const cached = await this.cache.get(cacheKey);
        if (cached) {
          return JSON.parse(cached);
        }
      }

      // Get from database
      const stats = await this.userRepo.getStats(telegramId);
      
      // Cache the result
      if (stats && this.options.cacheEnabled && this.cache) {
        const cacheKey = `user:stats:${telegramId}`;
        await this.cache.setEx(cacheKey, this.options.cacheTTL, JSON.stringify(stats));
      }

      return stats;
    } catch (error) {
      logger.logError('Failed to get user stats:', error);
      throw error;
    }
  }

  async updateLastActive(telegramId) {
    try {
      if (!this.isInitialized) {
        throw new Error('UserManager not initialized');
      }

      await this.userRepo.updateStats(telegramId, {});
      
      // Invalidate cache
      if (this.options.cacheEnabled && this.cache) {
        await this._invalidateUserCache(telegramId);
      }
    } catch (error) {
      logger.logError('Failed to update last active:', error);
      // Don't throw error for this operation
    }
  }

  async getUserAnalytics() {
    try {
      if (!this.isInitialized) {
        throw new Error('UserManager not initialized');
      }

      // Try cache first
      if (this.options.cacheEnabled && this.cache) {
        const cacheKey = 'analytics:users:global';
        const cached = await this.cache.get(cacheKey);
        if (cached) {
          return JSON.parse(cached);
        }
      }

      // Get from database
      const analytics = await this.userRepo.getUserAnalytics();
      
      // Cache the result for shorter time (15 minutes)
      if (this.options.cacheEnabled && this.cache) {
        const cacheKey = 'analytics:users:global';
        await this.cache.setEx(cacheKey, 900, JSON.stringify(analytics));
      }

      return analytics;
    } catch (error) {
      logger.logError('Failed to get user analytics:', error);
      throw error;
    }
  }

  async getActiveUsers(days = 30) {
    try {
      if (!this.isInitialized) {
        throw new Error('UserManager not initialized');
      }

      return await this.userRepo.getActiveUsers(days);
    } catch (error) {
      logger.logError('Failed to get active users:', error);
      throw error;
    }
  }

  async getUserInteractionHistory(telegramId, limit = 50) {
    try {
      if (!this.isInitialized) {
        throw new Error('UserManager not initialized');
      }

      return await this.userRepo.getInteractionHistory(telegramId, limit);
    } catch (error) {
      logger.logError('Failed to get user interaction history:', error);
      throw error;
    }
  }

  // Private helper methods
  async _cacheUser(user) {
    try {
      const cacheKey = `user:${user.telegramId}`;
      await this.cache.setEx(cacheKey, this.options.cacheTTL, JSON.stringify(user));
    } catch (error) {
      logger.logError('Failed to cache user:', error);
    }
  }

  async _getCachedUser(telegramId) {
    try {
      const cacheKey = `user:${telegramId}`;
      const cached = await this.cache.get(cacheKey);
      return cached ? JSON.parse(cached) : null;
    } catch (error) {
      logger.logError('Failed to get cached user:', error);
      return null;
    }
  }

  async _invalidateUserCache(telegramId) {
    try {
      const userKey = `user:${telegramId}`;
      const statsKey = `user:stats:${telegramId}`;
      await Promise.all([
        this.cache.del(userKey),
        this.cache.del(statsKey)
      ]);
    } catch (error) {
      logger.logError('Failed to invalidate user cache:', error);
    }
  }

  _validatePreferences(preferences) {
    const validated = {};
    
    if (preferences.notifications !== undefined) {
      validated.notifications = Boolean(preferences.notifications);
    }
    
    if (preferences.favoriteNumbers && Array.isArray(preferences.favoriteNumbers)) {
      validated.favoriteNumbers = preferences.favoriteNumbers
        .filter(num => typeof num === 'string' && /^\d{2}$/.test(num))
        .slice(0, 10); // Limit to 10 favorite numbers
    }
    
    if (preferences.timezone && typeof preferences.timezone === 'string') {
      validated.timezone = preferences.timezone;
    }
    
    if (preferences.language && typeof preferences.language === 'string') {
      validated.language = preferences.language;
    }

    return validated;
  }

  _sanitizeMetadata(metadata) {
    // Remove or anonymize sensitive data for privacy compliance
    const sanitized = { ...metadata };
    
    // Remove IP addresses, personal identifiers, etc.
    delete sanitized.ip;
    delete sanitized.location;
    
    // Keep only necessary data for analytics
    return {
      command: sanitized.command,
      queriedNumber: sanitized.queriedNumber,
      timestamp: sanitized.timestamp || new Date(),
      success: sanitized.success
    };
  }

  async cleanup() {
    try {
      if (this.cache && this.cache.isOpen) {
        await this.cache.quit();
      }
      this.isInitialized = false;
      logger.logInfo('UserManager cleanup completed');
    } catch (error) {
      logger.logError('Error during UserManager cleanup:', error);
    }
  }
}

module.exports = UserManager;
