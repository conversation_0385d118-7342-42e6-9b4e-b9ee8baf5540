// Analytics Manager - Aggregates and manages analytics data
// Provides comprehensive analytics for users, groups, and system performance

const mongoConnection = require('../connections/mongo');
const redisConnection = require('../connections/redis');
const logger = require('../logger')('./logs');

class AnalyticsManager {
  constructor(options = {}) {
    this.options = {
      cacheEnabled: true,
      cacheTTL: 900, // 15 minutes for analytics
      retentionDays: 90, // Keep analytics data for 90 days
      ...options
    };

    this.db = null;
    this.cache = null;
    this.isInitialized = false;
  }

  async initialize() {
    try {
      // Initialize database connection
      const mongoConn = mongoConnection('default');
      if (!mongoConn) {
        throw new Error('MongoDB connection not available');
      }

      this.db = mongoConn.db;

      // Initialize Redis cache if enabled
      if (this.options.cacheEnabled) {
        const redisConn = redisConnection('default');
        if (redisConn) {
          this.cache = redisConn.getConnection();
          await this.cache.connect();
        } else {
          logger.logWarn('Redis connection not available, running without cache');
          this.options.cacheEnabled = false;
        }
      }

      // Create analytics collection indexes
      await this._createIndexes();

      this.isInitialized = true;
      logger.logInfo('AnalyticsManager initialized successfully');
    } catch (error) {
      logger.logError('Failed to initialize AnalyticsManager:', error);
      throw error;
    }
  }

  async recordDailyAnalytics() {
    try {
      if (!this.isInitialized) {
        throw new Error('AnalyticsManager not initialized');
      }

      const today = new Date();
      today.setHours(0, 0, 0, 0);

      // Check if today's analytics already exist
      const existing = await this.db.collection('analytics').findOne({
        date: today,
        type: 'daily'
      });

      if (existing) {
        logger.logInfo('Daily analytics already recorded for today');
        return existing;
      }

      // Gather analytics data
      const [userAnalytics, groupAnalytics, commandStats, popularNumbers] = await Promise.all([
        this._getUserAnalytics(),
        this._getGroupAnalytics(),
        this._getCommandUsageStats(),
        this._getPopularNumbers()
      ]);

      const analyticsData = {
        date: today,
        type: 'daily',
        metrics: {
          users: userAnalytics,
          groups: groupAnalytics,
          commands: commandStats,
          popularNumbers: popularNumbers,
          systemHealth: await this._getSystemHealth()
        },
        createdAt: new Date()
      };

      // Save to database
      const result = await this.db.collection('analytics').insertOne(analyticsData);

      // Cache the result
      if (this.options.cacheEnabled && this.cache) {
        const cacheKey = `analytics:daily:${today.toISOString().split('T')[0]}`;
        await this.cache.setEx(cacheKey, this.options.cacheTTL, JSON.stringify(analyticsData));
      }

      logger.logInfo('Daily analytics recorded successfully');
      return analyticsData;
    } catch (error) {
      logger.logError('Failed to record daily analytics:', error);
      throw error;
    }
  }

  async getAnalyticsSummary(days = 7) {
    try {
      if (!this.isInitialized) {
        throw new Error('AnalyticsManager not initialized');
      }

      const cacheKey = `analytics:summary:${days}d`;

      // Try cache first
      if (this.options.cacheEnabled && this.cache) {
        const cached = await this.cache.get(cacheKey);
        if (cached) {
          return JSON.parse(cached);
        }
      }

      const fromDate = new Date();
      fromDate.setDate(fromDate.getDate() - days);
      fromDate.setHours(0, 0, 0, 0);

      const analytics = await this.db.collection('analytics')
        .find({
          date: { $gte: fromDate },
          type: 'daily'
        })
        .sort({ date: -1 })
        .toArray();

      const summary = this._aggregateAnalytics(analytics);

      // Cache the result
      if (this.options.cacheEnabled && this.cache) {
        await this.cache.setEx(cacheKey, this.options.cacheTTL, JSON.stringify(summary));
      }

      return summary;
    } catch (error) {
      logger.logError('Failed to get analytics summary:', error);
      throw error;
    }
  }

  async getRealtimeStats() {
    try {
      if (!this.isInitialized) {
        throw new Error('AnalyticsManager not initialized');
      }

      const cacheKey = 'analytics:realtime';

      // Try cache first (shorter TTL for realtime)
      if (this.options.cacheEnabled && this.cache) {
        const cached = await this.cache.get(cacheKey);
        if (cached) {
          return JSON.parse(cached);
        }
      }

      const [userStats, groupStats, recentActivity] = await Promise.all([
        this._getUserAnalytics(),
        this._getGroupAnalytics(),
        this._getRecentActivity()
      ]);

      const realtimeStats = {
        users: userStats,
        groups: groupStats,
        recentActivity: recentActivity,
        timestamp: new Date()
      };

      // Cache for 5 minutes
      if (this.options.cacheEnabled && this.cache) {
        await this.cache.setEx(cacheKey, 300, JSON.stringify(realtimeStats));
      }

      return realtimeStats;
    } catch (error) {
      logger.logError('Failed to get realtime stats:', error);
      throw error;
    }
  }

  async getPopularNumbers(days = 30, limit = 20) {
    try {
      if (!this.isInitialized) {
        throw new Error('AnalyticsManager not initialized');
      }

      const fromDate = new Date();
      fromDate.setDate(fromDate.getDate() - days);

      const pipeline = [
        {
          $match: {
            timestamp: { $gte: fromDate },
            'metadata.queriedNumber': { $exists: true }
          }
        },
        {
          $group: {
            _id: '$metadata.queriedNumber',
            count: { $sum: 1 },
            lastQueried: { $max: '$timestamp' }
          }
        },
        {
          $sort: { count: -1 }
        },
        {
          $limit: limit
        },
        {
          $project: {
            number: '$_id',
            count: 1,
            lastQueried: 1,
            _id: 0
          }
        }
      ];

      return await this.db.collection('user_interactions').aggregate(pipeline).toArray();
    } catch (error) {
      logger.logError('Failed to get popular numbers:', error);
      return [];
    }
  }

  async getCommandUsageTrends(days = 30) {
    try {
      if (!this.isInitialized) {
        throw new Error('AnalyticsManager not initialized');
      }

      const fromDate = new Date();
      fromDate.setDate(fromDate.getDate() - days);

      const pipeline = [
        {
          $match: {
            date: { $gte: fromDate },
            type: 'daily'
          }
        },
        {
          $sort: { date: 1 }
        },
        {
          $project: {
            date: 1,
            commands: '$metrics.commands'
          }
        }
      ];

      return await this.db.collection('analytics').aggregate(pipeline).toArray();
    } catch (error) {
      logger.logError('Failed to get command usage trends:', error);
      return [];
    }
  }

  async cleanupOldAnalytics() {
    try {
      if (!this.isInitialized) {
        throw new Error('AnalyticsManager not initialized');
      }

      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - this.options.retentionDays);

      const result = await this.db.collection('analytics').deleteMany({
        date: { $lt: cutoffDate }
      });

      logger.logInfo(`Cleaned up ${result.deletedCount} old analytics records`);
      return result.deletedCount;
    } catch (error) {
      logger.logError('Failed to cleanup old analytics:', error);
      throw error;
    }
  }

  // Private helper methods
  async _getUserAnalytics() {
    try {
      const pipeline = [
        {
          $group: {
            _id: null,
            totalUsers: { $sum: 1 },
            avgQueries: { $avg: '$statistics.totalQueries' },
            activeToday: {
              $sum: {
                $cond: [
                  {
                    $gte: [
                      '$statistics.lastActive',
                      new Date(Date.now() - 24 * 60 * 60 * 1000)
                    ]
                  },
                  1,
                  0
                ]
              }
            },
            activeThisWeek: {
              $sum: {
                $cond: [
                  {
                    $gte: [
                      '$statistics.lastActive',
                      new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
                    ]
                  },
                  1,
                  0
                ]
              }
            }
          }
        }
      ];

      const result = await this.db.collection('users').aggregate(pipeline).toArray();
      return result[0] || { totalUsers: 0, avgQueries: 0, activeToday: 0, activeThisWeek: 0 };
    } catch (error) {
      logger.logError('Failed to get user analytics:', error);
      return { totalUsers: 0, avgQueries: 0, activeToday: 0, activeThisWeek: 0 };
    }
  }

  async _getGroupAnalytics() {
    try {
      const pipeline = [
        {
          $match: {
            isActive: true,
            isBlocked: false
          }
        },
        {
          $group: {
            _id: null,
            totalGroups: { $sum: 1 },
            totalMembers: { $sum: '$statistics.memberCount' },
            avgMembersPerGroup: { $avg: '$statistics.memberCount' },
            groupsWithDailyPredictions: {
              $sum: {
                $cond: ['$settings.dailyPredictions', 1, 0]
              }
            },
            groupsWithWeeklyReports: {
              $sum: {
                $cond: ['$settings.weeklyReports', 1, 0]
              }
            },
            activeToday: {
              $sum: {
                $cond: [
                  {
                    $gte: [
                      '$statistics.lastActivity',
                      new Date(Date.now() - 24 * 60 * 60 * 1000)
                    ]
                  },
                  1,
                  0
                ]
              }
            }
          }
        }
      ];

      const result = await this.db.collection('groups').aggregate(pipeline).toArray();
      return result[0] || {
        totalGroups: 0,
        totalMembers: 0,
        avgMembersPerGroup: 0,
        groupsWithDailyPredictions: 0,
        groupsWithWeeklyReports: 0,
        activeToday: 0
      };
    } catch (error) {
      logger.logError('Failed to get group analytics:', error);
      return {
        totalGroups: 0,
        totalMembers: 0,
        avgMembersPerGroup: 0,
        groupsWithDailyPredictions: 0,
        groupsWithWeeklyReports: 0,
        activeToday: 0
      };
    }
  }

  async _getCommandUsageStats() {
    try {
      const pipeline = [
        {
          $match: {
            isActive: true,
            isBlocked: false
          }
        },
        {
          $group: {
            _id: null,
            totalDukienlo: { $sum: '$statistics.commandUsage.dukienlo' },
            totalDukiende: { $sum: '$statistics.commandUsage.dukiende' },
            totalLichsu: { $sum: '$statistics.commandUsage.lichsu' },
            totalXuhuonglo: { $sum: '$statistics.commandUsage.xuhuonglo' },
            totalXuhuongde: { $sum: '$statistics.commandUsage.xuhuongde' },
            totalNumber: { $sum: '$statistics.commandUsage.number' },
            totalHelp: { $sum: '$statistics.commandUsage.help' }
          }
        }
      ];

      const result = await this.db.collection('groups').aggregate(pipeline).toArray();
      return result[0] || {
        totalDukienlo: 0,
        totalDukiende: 0,
        totalLichsu: 0,
        totalXuhuonglo: 0,
        totalXuhuongde: 0,
        totalNumber: 0,
        totalHelp: 0
      };
    } catch (error) {
      logger.logError('Failed to get command usage stats:', error);
      return {
        totalDukienlo: 0,
        totalDukiende: 0,
        totalLichsu: 0,
        totalXuhuonglo: 0,
        totalXuhuongde: 0,
        totalNumber: 0,
        totalHelp: 0
      };
    }
  }

  async _getPopularNumbers() {
    try {
      return await this.getPopularNumbers(7, 10); // Last 7 days, top 10
    } catch (error) {
      logger.logError('Failed to get popular numbers:', error);
      return [];
    }
  }

  async _getSystemHealth() {
    try {
      const memoryUsage = process.memoryUsage();
      const uptime = process.uptime();

      return {
        memoryUsage: {
          rss: Math.round(memoryUsage.rss / 1024 / 1024), // MB
          heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024), // MB
          heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024), // MB
        },
        uptime: Math.round(uptime), // seconds
        nodeVersion: process.version,
        platform: process.platform
      };
    } catch (error) {
      logger.logError('Failed to get system health:', error);
      return {};
    }
  }

  async _getRecentActivity() {
    try {
      const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);

      const recentInteractions = await this.db.collection('user_interactions')
        .find({ timestamp: { $gte: oneHourAgo } })
        .sort({ timestamp: -1 })
        .limit(50)
        .toArray();

      return {
        totalInteractions: recentInteractions.length,
        interactions: recentInteractions.map(interaction => ({
          command: interaction.command,
          timestamp: interaction.timestamp,
          metadata: interaction.metadata
        }))
      };
    } catch (error) {
      logger.logError('Failed to get recent activity:', error);
      return { totalInteractions: 0, interactions: [] };
    }
  }

  _aggregateAnalytics(analyticsArray) {
    if (!analyticsArray.length) {
      return {
        totalDays: 0,
        averages: {},
        trends: {},
        summary: {}
      };
    }

    const totalDays = analyticsArray.length;
    const latest = analyticsArray[0];
    const oldest = analyticsArray[analyticsArray.length - 1];

    // Calculate averages
    const averages = {
      usersPerDay: analyticsArray.reduce((sum, day) => sum + (day.metrics.users.activeToday || 0), 0) / totalDays,
      groupsPerDay: analyticsArray.reduce((sum, day) => sum + (day.metrics.groups.activeToday || 0), 0) / totalDays,
      commandsPerDay: analyticsArray.reduce((sum, day) => {
        const commands = day.metrics.commands || {};
        return sum + Object.values(commands).reduce((cmdSum, count) => cmdSum + (count || 0), 0);
      }, 0) / totalDays
    };

    // Calculate trends (growth from oldest to latest)
    const trends = {
      userGrowth: this._calculateGrowth(oldest.metrics.users.totalUsers, latest.metrics.users.totalUsers),
      groupGrowth: this._calculateGrowth(oldest.metrics.groups.totalGroups, latest.metrics.groups.totalGroups),
      activityGrowth: this._calculateGrowth(
        oldest.metrics.users.activeToday + oldest.metrics.groups.activeToday,
        latest.metrics.users.activeToday + latest.metrics.groups.activeToday
      )
    };

    return {
      totalDays,
      averages,
      trends,
      summary: {
        currentUsers: latest.metrics.users.totalUsers,
        currentGroups: latest.metrics.groups.totalGroups,
        totalMembers: latest.metrics.groups.totalMembers,
        popularNumbers: (latest.metrics.popularNumbers || []).slice(0, 5)
      }
    };
  }

  _calculateGrowth(oldValue, newValue) {
    if (!oldValue || oldValue === 0) return newValue > 0 ? 100 : 0;
    return Math.round(((newValue - oldValue) / oldValue) * 100);
  }

  async _createIndexes() {
    try {
      await this.db.collection('analytics').createIndexes([
        { key: { date: -1, type: 1 } },
        { key: { type: 1 } },
        { key: { createdAt: -1 } }
      ]);
    } catch (error) {
      logger.logError('Failed to create analytics indexes:', error);
    }
  }

  async cleanup() {
    try {
      if (this.cache && this.cache.isOpen) {
        await this.cache.quit();
      }
      this.isInitialized = false;
      logger.logInfo('AnalyticsManager cleanup completed');
    } catch (error) {
      logger.logError('Error during AnalyticsManager cleanup:', error);
    }
  }
}

module.exports = AnalyticsManager;
