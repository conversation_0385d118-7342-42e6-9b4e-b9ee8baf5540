// Group Repository - Database operations for group data
// Handles group settings, statistics, and admin management

const BaseRepository = require('./baseRepository');

class GroupRepository extends BaseRepository {
  constructor(database) {
    super(database, 'groups');
  }

  async createGroup(groupData) {
    // Create new group record with default settings
    try {
      const group = {
        ...groupData,
        settings: {
          dailyPredictions: false,
          predictionTime: '08:00',
          weeklyReports: false,
          weeklyReportDay: 1, // Monday
          language: 'vi',
          timezone: 'Asia/Ho_Chi_Minh',
          enabledCommands: ['dukienlo', 'dukiende', 'lichsu', 'xuhuonglo', 'xuhuongde', 'number', 'help'],
          predictionTypes: ['lo', 'de'],
          maxPredictionsPerMessage: 5,
          ...groupData.settings
        },
        statistics: {
          memberCount: 0,
          totalMessages: 0,
          lastActivity: new Date(),
          commandUsage: {
            dukienlo: 0,
            dukiende: 0,
            lichsu: 0,
            xuhuonglo: 0,
            xuhuongde: 0,
            number: 0,
            help: 0
          },
          joinDate: new Date(),
          ...groupData.statistics
        },
        admins: groupData.admins || [],
        isActive: true,
        isBlocked: false
      };

      return await this.create(group);
    } catch (error) {
      throw new Error(`Failed to create group: ${error.message}`);
    }
  }

  async findByTelegramId(telegramId) {
    // Find group by Telegram ID
    try {
      return await this.findOne({ telegramId });
    } catch (error) {
      throw new Error(`Failed to find group: ${error.message}`);
    }
  }

  async updateSettings(telegramId, settings) {
    // Update group settings
    try {
      const updateData = {};
      Object.keys(settings).forEach(key => {
        updateData[`settings.${key}`] = settings[key];
      });

      return await this.update({ telegramId }, updateData);
    } catch (error) {
      throw new Error(`Failed to update group settings: ${error.message}`);
    }
  }

  async getSettings(telegramId) {
    // Get group settings
    try {
      const group = await this.findOne(
        { telegramId },
        { projection: { settings: 1, isActive: 1, isBlocked: 1 } }
      );

      return group ? group.settings : null;
    } catch (error) {
      throw new Error(`Failed to get group settings: ${error.message}`);
    }
  }

  async updateStats(telegramId, updateData) {
    // Update group statistics
    try {
      const update = {
        'statistics.lastActivity': new Date()
      };

      if (updateData.command) {
        update[`statistics.commandUsage.${updateData.command}`] = 1;
        update['statistics.totalMessages'] = 1;
      }

      if (updateData.memberCount !== undefined) {
        update['statistics.memberCount'] = updateData.memberCount;
      }

      return await this.update(
        { telegramId },
        {},
        {
          $set: update,
          $inc: updateData.command ? {
            [`statistics.commandUsage.${updateData.command}`]: 1,
            'statistics.totalMessages': 1
          } : {}
        }
      );
    } catch (error) {
      throw new Error(`Failed to update group stats: ${error.message}`);
    }
  }

  async addAdmin(telegramId, adminData) {
    // Add admin to group
    try {
      const admin = {
        telegramId: adminData.telegramId,
        username: adminData.username,
        firstName: adminData.firstName,
        addedAt: new Date()
      };

      return await this.db.collection(this.collection)
        .updateOne(
          { telegramId },
          { $addToSet: { admins: admin } }
        );
    } catch (error) {
      throw new Error(`Failed to add admin: ${error.message}`);
    }
  }

  async removeAdmin(telegramId, adminTelegramId) {
    // Remove admin from group
    try {
      return await this.db.collection(this.collection)
        .updateOne(
          { telegramId },
          { $pull: { admins: { telegramId: adminTelegramId } } }
        );
    } catch (error) {
      throw new Error(`Failed to remove admin: ${error.message}`);
    }
  }

  async isAdmin(telegramId, userTelegramId) {
    // Check if user is admin of the group
    try {
      const group = await this.findOne(
        {
          telegramId,
          'admins.telegramId': userTelegramId
        },
        { projection: { _id: 1 } }
      );

      return !!group;
    } catch (error) {
      throw new Error(`Failed to check admin status: ${error.message}`);
    }
  }

  async getGroupsWithDailyPredictions() {
    // Get groups that have daily predictions enabled
    try {
      return await this.find({
        'settings.dailyPredictions': true,
        isActive: true,
        isBlocked: false
      });
    } catch (error) {
      throw new Error(`Failed to get groups with daily predictions: ${error.message}`);
    }
  }

  async getGroupsForWeeklyReports(dayOfWeek) {
    // Get groups that should receive weekly reports on specific day
    try {
      return await this.find({
        'settings.weeklyReports': true,
        'settings.weeklyReportDay': dayOfWeek,
        isActive: true,
        isBlocked: false
      });
    } catch (error) {
      throw new Error(`Failed to get groups for weekly reports: ${error.message}`);
    }
  }

  async getActiveGroups(days = 30) {
    // Get active groups within specified days
    try {
      const fromDate = new Date();
      fromDate.setDate(fromDate.getDate() - days);

      return await this.find({
        'statistics.lastActivity': { $gte: fromDate },
        isActive: true,
        isBlocked: false
      });
    } catch (error) {
      throw new Error(`Failed to get active groups: ${error.message}`);
    }
  }

  async getGroupAnalytics() {
    // Get group analytics for admin dashboard
    try {
      const pipeline = [
        {
          $match: {
            isActive: true,
            isBlocked: false
          }
        },
        {
          $group: {
            _id: null,
            totalGroups: { $sum: 1 },
            totalMembers: { $sum: '$statistics.memberCount' },
            avgMembersPerGroup: { $avg: '$statistics.memberCount' },
            groupsWithDailyPredictions: {
              $sum: {
                $cond: ['$settings.dailyPredictions', 1, 0]
              }
            },
            groupsWithWeeklyReports: {
              $sum: {
                $cond: ['$settings.weeklyReports', 1, 0]
              }
            },
            activeToday: {
              $sum: {
                $cond: [
                  {
                    $gte: [
                      '$statistics.lastActivity',
                      new Date(Date.now() - 24 * 60 * 60 * 1000)
                    ]
                  },
                  1,
                  0
                ]
              }
            }
          }
        }
      ];

      const result = await this.aggregate(pipeline);
      return result[0] || {
        totalGroups: 0,
        totalMembers: 0,
        avgMembersPerGroup: 0,
        groupsWithDailyPredictions: 0,
        groupsWithWeeklyReports: 0,
        activeToday: 0
      };
    } catch (error) {
      throw new Error(`Failed to get group analytics: ${error.message}`);
    }
  }

  async getCommandUsageStats() {
    // Get command usage statistics across all groups
    try {
      const pipeline = [
        {
          $match: {
            isActive: true,
            isBlocked: false
          }
        },
        {
          $group: {
            _id: null,
            totalDukienlo: { $sum: '$statistics.commandUsage.dukienlo' },
            totalDukiende: { $sum: '$statistics.commandUsage.dukiende' },
            totalLichsu: { $sum: '$statistics.commandUsage.lichsu' },
            totalXuhuonglo: { $sum: '$statistics.commandUsage.xuhuonglo' },
            totalXuhuongde: { $sum: '$statistics.commandUsage.xuhuongde' },
            totalNumber: { $sum: '$statistics.commandUsage.number' },
            totalHelp: { $sum: '$statistics.commandUsage.help' }
          }
        }
      ];

      const result = await this.aggregate(pipeline);
      return result[0] || {
        totalDukienlo: 0,
        totalDukiende: 0,
        totalLichsu: 0,
        totalXuhuonglo: 0,
        totalXuhuongde: 0,
        totalNumber: 0,
        totalHelp: 0
      };
    } catch (error) {
      throw new Error(`Failed to get command usage stats: ${error.message}`);
    }
  }

  async deactivateGroup(telegramId, reason = 'removed') {
    // Deactivate group (when bot is removed)
    try {
      return await this.update(
        { telegramId },
        {
          isActive: false,
          deactivatedAt: new Date(),
          deactivationReason: reason
        }
      );
    } catch (error) {
      throw new Error(`Failed to deactivate group: ${error.message}`);
    }
  }

  async reactivateGroup(telegramId) {
    // Reactivate group (when bot is re-added)
    try {
      return await this.update(
        { telegramId },
        {
          isActive: true,
          reactivatedAt: new Date(),
          $unset: { deactivatedAt: '', deactivationReason: '' }
        }
      );
    } catch (error) {
      throw new Error(`Failed to reactivate group: ${error.message}`);
    }
  }

  async createIndexes() {
    // Create database indexes for performance
    try {
      await this.db.collection(this.collection).createIndexes([
        { key: { telegramId: 1 }, unique: true },
        { key: { type: 1, isActive: 1 } },
        { key: { 'statistics.lastActivity': -1 } },
        { key: { 'settings.dailyPredictions': 1, isActive: 1 } },
        { key: { 'settings.weeklyReports': 1, 'settings.weeklyReportDay': 1, isActive: 1 } },
        { key: { isActive: 1, isBlocked: 1 } },
        { key: { createdAt: -1 } }
      ]);
    } catch (error) {
      console.error('Failed to create group indexes:', error.message);
    }
  }

  validateData(data) {
    // Validate group data
    if (!data.telegramId || typeof data.telegramId !== 'number') {
      throw new Error('telegramId is required and must be a number');
    }

    if (!data.title || typeof data.title !== 'string') {
      throw new Error('title is required and must be a string');
    }

    if (!data.type || !['group', 'supergroup', 'channel'].includes(data.type)) {
      throw new Error('type must be one of: group, supergroup, channel');
    }

    return true;
  }
}

module.exports = GroupRepository;