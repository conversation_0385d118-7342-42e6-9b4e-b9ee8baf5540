// Base Repository - Abstract base class for all repositories
// Provides common CRUD operations and error handling patterns

class BaseRepository {
  constructor(database, collectionName) {
    if (new.target === BaseRepository) {
      throw new Error('BaseRepository is an abstract class and cannot be instantiated directly');
    }
    
    this.db = database;
    this.collection = collectionName;
  }

  /**
   * Create a new document
   * @param {Object} data - Document data
   * @returns {Promise<string>} - Inserted document ID
   */
  async create(data) {
    try {
      const document = {
        ...data,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      const result = await this.db.collection(this.collection).insertOne(document);
      return result.insertedId;
    } catch (error) {
      throw new Error(`Failed to create document in ${this.collection}: ${error.message}`);
    }
  }

  /**
   * Find document by ID
   * @param {string} id - Document ID
   * @returns {Promise<Object|null>} - Found document or null
   */
  async findById(id) {
    try {
      const ObjectId = require('mongodb').ObjectId;
      return await this.db.collection(this.collection).findOne({ _id: new ObjectId(id) });
    } catch (error) {
      throw new Error(`Failed to find document by ID in ${this.collection}: ${error.message}`);
    }
  }

  /**
   * Find documents by query
   * @param {Object} query - MongoDB query object
   * @param {Object} options - Query options (sort, limit, skip, projection)
   * @returns {Promise<Array>} - Array of found documents
   */
  async find(query = {}, options = {}) {
    try {
      let cursor = this.db.collection(this.collection).find(query);

      if (options.projection) {
        cursor = cursor.project(options.projection);
      }

      if (options.sort) {
        cursor = cursor.sort(options.sort);
      }

      if (options.skip) {
        cursor = cursor.skip(options.skip);
      }

      if (options.limit) {
        cursor = cursor.limit(options.limit);
      }

      return await cursor.toArray();
    } catch (error) {
      throw new Error(`Failed to find documents in ${this.collection}: ${error.message}`);
    }
  }

  /**
   * Find one document by query
   * @param {Object} query - MongoDB query object
   * @param {Object} options - Query options (projection)
   * @returns {Promise<Object|null>} - Found document or null
   */
  async findOne(query, options = {}) {
    try {
      return await this.db.collection(this.collection).findOne(query, options);
    } catch (error) {
      throw new Error(`Failed to find document in ${this.collection}: ${error.message}`);
    }
  }

  /**
   * Update document by ID
   * @param {string} id - Document ID
   * @param {Object} updateData - Data to update
   * @returns {Promise<Object>} - Update result
   */
  async updateById(id, updateData) {
    try {
      const ObjectId = require('mongodb').ObjectId;
      const update = {
        $set: {
          ...updateData,
          updatedAt: new Date()
        }
      };

      return await this.db.collection(this.collection).updateOne(
        { _id: new ObjectId(id) },
        update
      );
    } catch (error) {
      throw new Error(`Failed to update document by ID in ${this.collection}: ${error.message}`);
    }
  }

  /**
   * Update documents by query
   * @param {Object} query - MongoDB query object
   * @param {Object} updateData - Data to update
   * @param {Object} options - Update options (upsert, multi)
   * @returns {Promise<Object>} - Update result
   */
  async update(query, updateData, options = {}) {
    try {
      const update = {
        $set: {
          ...updateData,
          updatedAt: new Date()
        }
      };

      if (options.upsert && !updateData.createdAt) {
        update.$setOnInsert = { createdAt: new Date() };
      }

      const method = options.multi ? 'updateMany' : 'updateOne';
      return await this.db.collection(this.collection)[method](query, update, options);
    } catch (error) {
      throw new Error(`Failed to update documents in ${this.collection}: ${error.message}`);
    }
  }

  /**
   * Delete document by ID
   * @param {string} id - Document ID
   * @returns {Promise<Object>} - Delete result
   */
  async deleteById(id) {
    try {
      const ObjectId = require('mongodb').ObjectId;
      return await this.db.collection(this.collection).deleteOne({ _id: new ObjectId(id) });
    } catch (error) {
      throw new Error(`Failed to delete document by ID in ${this.collection}: ${error.message}`);
    }
  }

  /**
   * Delete documents by query
   * @param {Object} query - MongoDB query object
   * @returns {Promise<Object>} - Delete result
   */
  async delete(query) {
    try {
      return await this.db.collection(this.collection).deleteMany(query);
    } catch (error) {
      throw new Error(`Failed to delete documents in ${this.collection}: ${error.message}`);
    }
  }

  /**
   * Count documents by query
   * @param {Object} query - MongoDB query object
   * @returns {Promise<number>} - Document count
   */
  async count(query = {}) {
    try {
      return await this.db.collection(this.collection).countDocuments(query);
    } catch (error) {
      throw new Error(`Failed to count documents in ${this.collection}: ${error.message}`);
    }
  }

  /**
   * Check if document exists
   * @param {Object} query - MongoDB query object
   * @returns {Promise<boolean>} - True if document exists
   */
  async exists(query) {
    try {
      const count = await this.db.collection(this.collection).countDocuments(query, { limit: 1 });
      return count > 0;
    } catch (error) {
      throw new Error(`Failed to check document existence in ${this.collection}: ${error.message}`);
    }
  }

  /**
   * Perform aggregation query
   * @param {Array} pipeline - Aggregation pipeline
   * @returns {Promise<Array>} - Aggregation results
   */
  async aggregate(pipeline) {
    try {
      return await this.db.collection(this.collection).aggregate(pipeline).toArray();
    } catch (error) {
      throw new Error(`Failed to perform aggregation in ${this.collection}: ${error.message}`);
    }
  }

  /**
   * Create database indexes - to be implemented by child classes
   * @returns {Promise<void>}
   */
  async createIndexes() {
    // Override in child classes
    console.log(`No indexes defined for ${this.collection}`);
  }

  /**
   * Validate document data - to be implemented by child classes
   * @param {Object} data - Document data to validate
   * @returns {boolean} - True if valid
   */
  validateData(data) {
    // Override in child classes for custom validation
    return true;
  }

  /**
   * Transform data before saving - to be implemented by child classes
   * @param {Object} data - Raw data
   * @returns {Object} - Transformed data
   */
  transformData(data) {
    // Override in child classes for custom transformation
    return data;
  }
}

module.exports = BaseRepository;