// Lottery Repository - Database operations for lottery data
// Handles CRUD operations for lottery results and predictions

const BaseRepository = require('./baseRepository');

class LotteryRepository extends BaseRepository {
  constructor(database) {
    super(database, 'lotteryResults');
  }

  async save(lotteryResult) {
    // Save lottery result to database
    try {
      const result = await this.db.collection(this.collection).insertOne({
        ...lotteryResult,
        createdAt: new Date(),
        updatedAt: new Date()
      });

      return result.insertedId;
    } catch (error) {
      throw new Error(`Failed to save lottery result: ${error.message}`);
    }
  }

  async getHistory(fromDate, toDate, region = 'north') {
    // Get lottery history within date range
    try {
      const query = {
        region,
        date: {
          $gte: new Date(fromDate),
          $lte: new Date(toDate)
        }
      };

      return await this.db.collection(this.collection)
        .find(query)
        .sort({ date: -1 })
        .toArray();
    } catch (error) {
      throw new Error(`Failed to get lottery history: ${error.message}`);
    }
  }

  async getLatest(region = 'north', limit = 10) {
    // Get latest lottery results
    try {
      return await this.db.collection(this.collection)
        .find({ region })
        .sort({ date: -1 })
        .limit(limit)
        .toArray();
    } catch (error) {
      throw new Error(`Failed to get latest results: ${error.message}`);
    }
  }

  async getByDate(date, region = 'north') {
    // Get lottery result for specific date
    try {
      return await this.db.collection(this.collection)
        .findOne({
          date: new Date(date),
          region
        });
    } catch (error) {
      throw new Error(`Failed to get result by date: ${error.message}`);
    }
  }

  async getNumberFrequency(number, region = 'north', days = 100) {
    // Get frequency of specific number in recent results
    try {
      const fromDate = new Date();
      fromDate.setDate(fromDate.getDate() - days);

      const results = await this.db.collection(this.collection)
        .find({
          region,
          date: { $gte: fromDate },
          $or: [
            { 'numbers.lo': number },
            { 'numbers.de': number }
          ]
        })
        .toArray();

      return {
        number,
        frequency: results.length,
        period: days,
        lastSeen: results.length > 0 ? results[0].date : null
      };
    } catch (error) {
      throw new Error(`Failed to get number frequency: ${error.message}`);
    }
  }

  async getStatistics(region = 'north', days = 100) {
    // Get comprehensive statistics for the region
    try {
      const fromDate = new Date();
      fromDate.setDate(fromDate.getDate() - days);

      const pipeline = [
        {
          $match: {
            region,
            date: { $gte: fromDate }
          }
        },
        {
          $unwind: '$numbers.lo'
        },
        {
          $group: {
            _id: '$numbers.lo',
            frequency: { $sum: 1 },
            lastSeen: { $max: '$date' }
          }
        },
        {
          $sort: { frequency: -1 }
        }
      ];

      return await this.aggregate(pipeline);
    } catch (error) {
      throw new Error(`Failed to get statistics: ${error.message}`);
    }
  }

  async createIndexes() {
    // Create database indexes for performance
    try {
      await this.db.collection(this.collection).createIndexes([
        { key: { date: -1, region: 1 } },
        { key: { region: 1, 'numbers.lo': 1 } },
        { key: { region: 1, 'numbers.de': 1 } },
        { key: { createdAt: -1 } }
      ]);
    } catch (error) {
      console.error('Failed to create indexes:', error.message);
    }
  }
}

module.exports = LotteryRepository;