// User Repository - Database operations for user data
// Handles user preferences, statistics, and interaction tracking

const BaseRepository = require('./baseRepository');

class UserRepository extends BaseRepository {
  constructor(database) {
    super(database, 'users');
  }

  async create(userData) {
    // Create new user record
    try {
      const user = {
        ...userData,
        preferences: {
          notifications: true,
          favoriteNumbers: [],
          timezone: 'Asia/Ho_Chi_Minh',
          ...userData.preferences
        },
        statistics: {
          totalQueries: 0,
          lastActive: new Date(),
          mostQueriedNumbers: [],
          ...userData.statistics
        }
      };

      return await super.create(user);
    } catch (error) {
      throw new Error(`Failed to create user: ${error.message}`);
    }
  }

  async findByTelegramId(telegramId) {
    // Find user by Telegram ID
    try {
      return await this.findOne({ telegramId });
    } catch (error) {
      throw new Error(`Failed to find user: ${error.message}`);
    }
  }

  async updateStats(telegramId, updateData) {
    // Update user statistics
    try {
      const update = {
        $set: {
          'statistics.lastActive': new Date(),
          updatedAt: new Date()
        },
        $inc: {
          'statistics.totalQueries': 1
        }
      };

      if (updateData.queriedNumber) {
        update.$addToSet = {
          'statistics.mostQueriedNumbers': updateData.queriedNumber
        };
      }

      return await this.db.collection(this.collection)
        .updateOne({ telegramId }, update, { upsert: true });
    } catch (error) {
      throw new Error(`Failed to update user stats: ${error.message}`);
    }
  }

  async updatePreferences(telegramId, preferences) {
    // Update user preferences
    try {
      return await this.db.collection(this.collection)
        .updateOne(
          { telegramId },
          {
            $set: {
              preferences: { ...preferences },
              updatedAt: new Date()
            }
          }
        );
    } catch (error) {
      throw new Error(`Failed to update preferences: ${error.message}`);
    }
  }

  async getStats(telegramId) {
    // Get user statistics
    try {
      const user = await this.findOne(
        { telegramId },
        { projection: { statistics: 1, preferences: 1 } }
      );

      return user ? { ...user.statistics, preferences: user.preferences } : null;
    } catch (error) {
      throw new Error(`Failed to get user stats: ${error.message}`);
    }
  }

  async getActiveUsers(days = 30) {
    // Get active users within specified days
    try {
      const fromDate = new Date();
      fromDate.setDate(fromDate.getDate() - days);

      return await this.count({
        'statistics.lastActive': { $gte: fromDate }
      });
    } catch (error) {
      throw new Error(`Failed to get active users: ${error.message}`);
    }
  }

  async getUserAnalytics() {
    // Get user analytics for admin dashboard
    try {
      const pipeline = [
        {
          $group: {
            _id: null,
            totalUsers: { $sum: 1 },
            avgQueries: { $avg: '$statistics.totalQueries' },
            activeToday: {
              $sum: {
                $cond: [
                  {
                    $gte: [
                      '$statistics.lastActive',
                      new Date(Date.now() - 24 * 60 * 60 * 1000)
                    ]
                  },
                  1,
                  0
                ]
              }
            }
          }
        }
      ];

      const result = await this.aggregate(pipeline);
      return result[0] || { totalUsers: 0, avgQueries: 0, activeToday: 0 };
    } catch (error) {
      throw new Error(`Failed to get user analytics: ${error.message}`);
    }
  }

  async logInteraction(telegramId, command, metadata = {}) {
    // Log user interaction for analytics
    try {
      const interaction = {
        telegramId,
        command,
        metadata,
        timestamp: new Date()
      };

      // Store in interactions collection
      await this.db.collection('user_interactions').insertOne(interaction);

      // Update user stats
      await this.updateStats(telegramId, {
        queriedNumber: metadata.queriedNumber,
        command
      });

    } catch (error) {
      throw new Error(`Failed to log interaction: ${error.message}`);
    }
  }

  async getInteractionHistory(telegramId, limit = 50) {
    // Get user interaction history
    try {
      return await this.db.collection('user_interactions')
        .find({ telegramId })
        .sort({ timestamp: -1 })
        .limit(limit)
        .toArray();
    } catch (error) {
      throw new Error(`Failed to get interaction history: ${error.message}`);
    }
  }

  async createIndexes() {
    // Create database indexes for performance
    try {
      await this.db.collection(this.collection).createIndexes([
        { key: { telegramId: 1 }, unique: true },
        { key: { 'statistics.lastActive': -1 } },
        { key: { createdAt: -1 } }
      ]);

      // Create indexes for interactions collection
      await this.db.collection('user_interactions').createIndexes([
        { key: { telegramId: 1 } },
        { key: { timestamp: -1 } },
        { key: { command: 1 } }
      ]);
    } catch (error) {
      console.error('Failed to create user indexes:', error.message);
    }
  }
}

module.exports = UserRepository;