// Lottery Data Collector - Fetches lottery results from xoso.com.vn
// Handles data parsing, validation, and error recovery

const axios = require('axios');
const cheerio = require('cheerio');
const moment = require('moment');

class LotteryCollector {
  constructor(options = {}) {
    this.options = {
      baseUrl: 'https://xoso.com.vn',
      timeout: 30000,
      retryAttempts: 3,
      retryDelay: 2000,
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
      ...options
    };

    this.axiosInstance = axios.create({
      timeout: this.options.timeout,
      headers: {
        'User-Agent': this.options.userAgent,
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'vi-VN,vi;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1'
      }
    });
  }

  /**
   * Fetch lottery results for a specific date and region
   * @param {Date} date - Date to fetch results for
   * @param {string} region - Region ('north', 'central', 'south')
   * @returns {Object} Parsed lottery results
   */
  async fetchLotteryResults(date, region = 'north') {
    const formattedDate = moment(date).format('DD-MM-YYYY');
    console.log(`🔍 Fetching lottery results for ${region} region on ${formattedDate}`);

    try {
      const url = this._buildUrl(date, region);
      const html = await this._fetchWithRetry(url);
      const results = this._parseResults(html, date, region);

      // Validate parsed results
      this._validateResults(results);

      console.log(`✅ Successfully fetched and parsed results for ${formattedDate}`);
      return results;

    } catch (error) {
      console.error(`❌ Failed to fetch lottery results for ${formattedDate}:`, error.message);
      throw new Error(`Data collection failed: ${error.message}`);
    }
  }

  /**
   * Fetch results for multiple dates
   * @param {Date} startDate - Start date
   * @param {Date} endDate - End date
   * @param {string} region - Region
   * @returns {Array} Array of lottery results
   */
  async fetchMultipleDates(startDate, endDate, region = 'north') {
    const results = [];
    const currentDate = moment(startDate);
    const end = moment(endDate);

    console.log(`📅 Fetching results from ${currentDate.format('DD-MM-YYYY')} to ${end.format('DD-MM-YYYY')}`);

    while (currentDate.isSameOrBefore(end)) {
      try {
        const result = await this.fetchLotteryResults(currentDate.toDate(), region);
        results.push(result);

        // Add delay between requests to be respectful
        await this._delay(1000);

      } catch (error) {
        console.warn(`⚠️ Failed to fetch results for ${currentDate.format('DD-MM-YYYY')}: ${error.message}`);
        // Continue with next date instead of failing completely
      }

      currentDate.add(1, 'day');
    }

    console.log(`📊 Successfully fetched ${results.length} lottery results`);
    return results;
  }

  /**
   * Get latest lottery results
   * @param {string} region - Region
   * @returns {Object} Latest lottery results
   */
  async fetchLatestResults(region = 'north') {
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    // Try today first, then yesterday
    try {
      return await this.fetchLotteryResults(today, region);
    } catch (error) {
      console.log('📅 No results for today, trying yesterday...');
      return await this.fetchLotteryResults(yesterday, region);
    }
  }

  /**
   * Build URL for specific date and region
   * @param {Date} date - Date
   * @param {string} region - Region
   * @returns {string} URL
   */
  _buildUrl(date, region) {
    const formattedDate = moment(date).format('DD-MM-YYYY');

    const regionPaths = {
      north: '/xo-so-mien-bac',
      central: '/xo-so-mien-trung',
      south: '/xo-so-mien-nam'
    };

    const regionPath = regionPaths[region] || regionPaths.north;
    return `${this.options.baseUrl}${regionPath}/xsmb-${formattedDate}.html`;
  }

  /**
   * Fetch URL with retry logic
   * @param {string} url - URL to fetch
   * @returns {string} HTML content
   */
  async _fetchWithRetry(url) {
    let lastError;

    for (let attempt = 1; attempt <= this.options.retryAttempts; attempt++) {
      try {
        console.log(`🌐 Fetching ${url} (attempt ${attempt}/${this.options.retryAttempts})`);

        const response = await this.axiosInstance.get(url);

        if (response.status === 200 && response.data) {
          return response.data;
        }

        throw new Error(`HTTP ${response.status}: ${response.statusText}`);

      } catch (error) {
        lastError = error;
        console.warn(`⚠️ Attempt ${attempt} failed: ${error.message}`);

        if (attempt < this.options.retryAttempts) {
          const delay = this.options.retryDelay * attempt;
          console.log(`⏳ Waiting ${delay}ms before retry...`);
          await this._delay(delay);
        }
      }
    }

    throw new Error(`Failed after ${this.options.retryAttempts} attempts: ${lastError.message}`);
  }

  /**
   * Parse HTML content to extract lottery results
   * @param {string} html - HTML content
   * @param {Date} date - Date of results
   * @param {string} region - Region
   * @returns {Object} Parsed results
   */
  _parseResults(html, date, region) {
    const $ = cheerio.load(html);

    // Find the main results table
    const resultsTable = $('.box_kqxs table').first();

    if (resultsTable.length === 0) {
      throw new Error('Could not find lottery results table in HTML');
    }

    const prizes = {};
    const numbers = { lo: [], de: null };

    // Parse each prize row
    resultsTable.find('tr').each((index, row) => {
      const $row = $(row);
      const prizeLabel = $row.find('td').first().text().trim();
      const prizeNumbers = [];

      // Extract numbers from the row
      $row.find('td').slice(1).each((i, cell) => {
        const cellText = $(cell).text().trim();
        if (cellText && /^\d+$/.test(cellText)) {
          prizeNumbers.push(cellText);
        }
      });

      // Map prize labels to our schema
      if (prizeLabel.includes('ĐB') || prizeLabel.includes('Đặc biệt')) {
        prizes.special = prizeNumbers[0];
      } else if (prizeLabel.includes('1') || prizeLabel.includes('Nhất')) {
        prizes.first = prizeNumbers[0];
      } else if (prizeLabel.includes('2') || prizeLabel.includes('Nhì')) {
        prizes.second = prizeNumbers;
      } else if (prizeLabel.includes('3') || prizeLabel.includes('Ba')) {
        prizes.third = prizeNumbers;
      } else if (prizeLabel.includes('4') || prizeLabel.includes('Tư')) {
        prizes.fourth = prizeNumbers;
      } else if (prizeLabel.includes('5') || prizeLabel.includes('Năm')) {
        prizes.fifth = prizeNumbers;
      } else if (prizeLabel.includes('6') || prizeLabel.includes('Sáu')) {
        prizes.sixth = prizeNumbers;
      } else if (prizeLabel.includes('7') || prizeLabel.includes('Bảy')) {
        prizes.seventh = prizeNumbers;
      }
    });

    // Extract lo numbers (last 2 digits of all prizes)
    this._extractLoNumbers(prizes, numbers);

    // Extract de number (last 2 digits of special prize)
    this._extractDeNumber(prizes, numbers);

    return {
      date: new Date(date),
      region,
      prizes,
      numbers,
      source: 'xoso.com.vn',
      collectedAt: new Date()
    };
  }

  /**
   * Extract lo numbers from prizes
   * @param {Object} prizes - Prize data
   * @param {Object} numbers - Numbers object to populate
   */
  _extractLoNumbers(prizes, numbers) {
    const loSet = new Set();

    // Extract from all prizes
    Object.values(prizes).forEach(prize => {
      if (Array.isArray(prize)) {
        prize.forEach(num => {
          if (num && num.length >= 2) {
            loSet.add(num.slice(-2));
          }
        });
      } else if (prize && prize.length >= 2) {
        loSet.add(prize.slice(-2));
      }
    });

    numbers.lo = Array.from(loSet);
  }

  /**
   * Extract de number from special prize
   * @param {Object} prizes - Prize data
   * @param {Object} numbers - Numbers object to populate
   */
  _extractDeNumber(prizes, numbers) {
    if (prizes.special && prizes.special.length >= 2) {
      numbers.de = prizes.special.slice(-2);
    }
  }

  /**
   * Validate parsed results
   * @param {Object} results - Parsed results
   */
  _validateResults(results) {
    if (!results.date || !results.region || !results.prizes) {
      throw new Error('Missing required fields in parsed results');
    }

    if (!results.prizes.special) {
      throw new Error('Special prize is required');
    }

    if (!/^\d{5}$/.test(results.prizes.special)) {
      throw new Error('Special prize must be 5 digits');
    }

    if (!results.numbers.de) {
      throw new Error('De number is required');
    }

    if (!results.numbers.lo || results.numbers.lo.length === 0) {
      throw new Error('Lo numbers are required');
    }
  }

  /**
   * Delay execution
   * @param {number} ms - Milliseconds to delay
   */
  _delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

module.exports = LotteryCollector;
