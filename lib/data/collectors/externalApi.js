// External API Integration - Manages external data sources
// Provides unified interface for different lottery data providers

const LotteryCollector = require('./lotteryCollector');

class ExternalApiManager {
  constructor(options = {}) {
    this.options = {
      primarySource: 'xoso.com.vn',
      fallbackSources: [],
      cacheResults: true,
      validateData: true,
      ...options
    };

    this.collectors = new Map();
    this.cache = new Map();
    
    // Initialize primary collector
    this._initializeCollectors();
  }

  /**
   * Initialize data collectors
   */
  _initializeCollectors() {
    // Primary collector - xoso.com.vn
    this.collectors.set('xoso.com.vn', new LotteryCollector({
      baseUrl: 'https://xoso.com.vn',
      timeout: 30000,
      retryAttempts: 3
    }));

    console.log('📡 External API Manager initialized with collectors:', Array.from(this.collectors.keys()));
  }

  /**
   * Fetch lottery results with fallback support
   * @param {Date} date - Date to fetch
   * @param {string} region - Region
   * @returns {Object} Lottery results
   */
  async fetchResults(date, region = 'north') {
    const cacheKey = this._getCacheKey(date, region);
    
    // Check cache first
    if (this.options.cacheResults && this.cache.has(cacheKey)) {
      console.log(`📋 Using cached results for ${date.toDateString()}`);
      return this.cache.get(cacheKey);
    }

    let lastError;
    const sources = [this.options.primarySource, ...this.options.fallbackSources];

    for (const source of sources) {
      try {
        console.log(`🔄 Trying source: ${source}`);
        
        const collector = this.collectors.get(source);
        if (!collector) {
          throw new Error(`No collector available for source: ${source}`);
        }

        const results = await collector.fetchLotteryResults(date, region);
        
        if (this.options.validateData) {
          this._validateResults(results);
        }

        // Cache successful results
        if (this.options.cacheResults) {
          this.cache.set(cacheKey, results);
        }

        console.log(`✅ Successfully fetched results from ${source}`);
        return results;

      } catch (error) {
        lastError = error;
        console.warn(`⚠️ Source ${source} failed: ${error.message}`);
        continue;
      }
    }

    throw new Error(`All sources failed. Last error: ${lastError?.message || 'Unknown error'}`);
  }

  /**
   * Fetch multiple dates with batch processing
   * @param {Date} startDate - Start date
   * @param {Date} endDate - End date
   * @param {string} region - Region
   * @param {Object} options - Batch options
   * @returns {Array} Array of results
   */
  async fetchBatch(startDate, endDate, region = 'north', options = {}) {
    const {
      batchSize = 5,
      delayBetweenBatches = 2000,
      continueOnError = true
    } = options;

    const dates = this._generateDateRange(startDate, endDate);
    const results = [];
    const errors = [];

    console.log(`📦 Starting batch fetch for ${dates.length} dates`);

    // Process in batches
    for (let i = 0; i < dates.length; i += batchSize) {
      const batch = dates.slice(i, i + batchSize);
      console.log(`🔄 Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(dates.length / batchSize)}`);

      const batchPromises = batch.map(async (date) => {
        try {
          const result = await this.fetchResults(date, region);
          return { date, result, success: true };
        } catch (error) {
          const errorInfo = { date, error: error.message, success: false };
          errors.push(errorInfo);
          
          if (!continueOnError) {
            throw error;
          }
          
          return errorInfo;
        }
      });

      const batchResults = await Promise.allSettled(batchPromises);
      
      batchResults.forEach(({ status, value }) => {
        if (status === 'fulfilled' && value.success) {
          results.push(value.result);
        }
      });

      // Delay between batches
      if (i + batchSize < dates.length) {
        console.log(`⏳ Waiting ${delayBetweenBatches}ms before next batch...`);
        await this._delay(delayBetweenBatches);
      }
    }

    console.log(`📊 Batch complete: ${results.length} successful, ${errors.length} failed`);
    
    if (errors.length > 0) {
      console.warn('❌ Batch errors:', errors);
    }

    return {
      results,
      errors,
      summary: {
        total: dates.length,
        successful: results.length,
        failed: errors.length
      }
    };
  }

  /**
   * Get latest results from all sources
   * @param {string} region - Region
   * @returns {Object} Latest results
   */
  async fetchLatest(region = 'north') {
    console.log(`🔍 Fetching latest results for ${region} region`);
    
    const collector = this.collectors.get(this.options.primarySource);
    if (!collector) {
      throw new Error(`Primary collector not available: ${this.options.primarySource}`);
    }

    return await collector.fetchLatestResults(region);
  }

  /**
   * Clear cache
   * @param {string} pattern - Optional pattern to match cache keys
   */
  clearCache(pattern = null) {
    if (!pattern) {
      this.cache.clear();
      console.log('🗑️ Cache cleared completely');
      return;
    }

    const keysToDelete = [];
    for (const key of this.cache.keys()) {
      if (key.includes(pattern)) {
        keysToDelete.push(key);
      }
    }

    keysToDelete.forEach(key => this.cache.delete(key));
    console.log(`🗑️ Cleared ${keysToDelete.length} cache entries matching pattern: ${pattern}`);
  }

  /**
   * Get cache statistics
   * @returns {Object} Cache stats
   */
  getCacheStats() {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys()),
      memoryUsage: this._estimateCacheSize()
    };
  }

  /**
   * Validate results data
   * @param {Object} results - Results to validate
   */
  _validateResults(results) {
    if (!results || typeof results !== 'object') {
      throw new Error('Results must be an object');
    }

    const required = ['date', 'region', 'prizes', 'numbers'];
    for (const field of required) {
      if (!results[field]) {
        throw new Error(`Missing required field: ${field}`);
      }
    }

    if (!results.prizes.special) {
      throw new Error('Special prize is required');
    }

    if (!results.numbers.de || results.numbers.de.length === 0) {
      throw new Error('De numbers are required');
    }

    if (!results.numbers.lo || results.numbers.lo.length === 0) {
      throw new Error('Lo numbers are required');
    }
  }

  /**
   * Generate cache key
   * @param {Date} date - Date
   * @param {string} region - Region
   * @returns {string} Cache key
   */
  _getCacheKey(date, region) {
    const dateStr = date.toISOString().split('T')[0];
    return `${region}_${dateStr}`;
  }

  /**
   * Generate date range
   * @param {Date} startDate - Start date
   * @param {Date} endDate - End date
   * @returns {Array} Array of dates
   */
  _generateDateRange(startDate, endDate) {
    const dates = [];
    const current = new Date(startDate);
    const end = new Date(endDate);

    while (current <= end) {
      dates.push(new Date(current));
      current.setDate(current.getDate() + 1);
    }

    return dates;
  }

  /**
   * Estimate cache memory usage
   * @returns {number} Estimated size in bytes
   */
  _estimateCacheSize() {
    let size = 0;
    for (const [key, value] of this.cache) {
      size += JSON.stringify({ key, value }).length * 2; // Rough estimate
    }
    return size;
  }

  /**
   * Delay execution
   * @param {number} ms - Milliseconds
   */
  _delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

module.exports = ExternalApiManager;
