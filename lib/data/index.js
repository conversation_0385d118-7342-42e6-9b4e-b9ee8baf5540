// Data Manager - Main data access and management system
// Coordinates database operations, caching, and data collection

const ExternalApiManager = require('./collectors/externalApi');

class DataManager {
  constructor(options = {}) {
    this.options = options;
    this.repositories = new Map();
    this.cache = null;
    this.collectors = new Map();
    this.externalApi = null;
  }

  async initialize() {
    // Initialize database connections and repositories
    console.log('Data Manager initialized - database connections pending');

    // Initialize external API manager
    this.externalApi = new ExternalApiManager({
      primarySource: 'xoso.com.vn',
      cacheResults: true,
      validateData: true
    });

    console.log('✅ External API Manager initialized');
  }

  async getLotteryHistory(options = {}) {
    // Get lottery history from database with caching
    // Support both old and new parameter formats
    let { fromDate, toDate, region = 'north', limit = 50, sortBy = 'date', sortOrder = 'desc' } = options;

    // Handle legacy parameter format
    if (typeof options === 'string' || options instanceof Date) {
      fromDate = arguments[0];
      toDate = arguments[1];
      region = arguments[2] || 'north';
      limit = 50;
    }

    const cacheKey = `history:${region}:${fromDate}:${toDate}:${limit}:${sortBy}:${sortOrder}`;

    // Try cache first
    let data = await this.cacheGet(cacheKey);
    if (data) {
      return data;
    }

    // Fetch from database
    const lotteryRepo = this.repositories.get('lottery');
    if (lotteryRepo) {
      if (fromDate && toDate) {
        data = await lotteryRepo.getHistory(fromDate, toDate, region);
      } else {
        data = await lotteryRepo.getLatest(region, limit);
      }

      // Apply sorting if needed
      if (data && Array.isArray(data)) {
        data.sort((a, b) => {
          const aVal = a[sortBy];
          const bVal = b[sortBy];

          if (sortOrder === 'desc') {
            return new Date(bVal) - new Date(aVal);
          } else {
            return new Date(aVal) - new Date(bVal);
          }
        });
      }

      await this.cacheSet(cacheKey, data, 3600); // Cache for 1 hour
    }

    return data || [];
  }

  async saveLotteryResult(result) {
    // Save new lottery result to database
    const lotteryRepo = this.repositories.get('lottery');
    if (lotteryRepo) {
      await lotteryRepo.save(result);
      // Invalidate related cache entries
      await this.invalidateCache('history:*');
    }
  }

  async getUserStats(userId) {
    // Get user statistics and preferences
    const cacheKey = `user:stats:${userId}`;

    let stats = await this.cacheGet(cacheKey);
    if (stats) {
      return stats;
    }

    const userRepo = this.repositories.get('user');
    if (userRepo) {
      stats = await userRepo.getStats(userId);
      await this.cacheSet(cacheKey, stats, 3600);
    }

    return stats || {};
  }

  async getGroupSettings(groupId) {
    // Get group settings and preferences
    const cacheKey = `group:settings:${groupId}`;

    let settings = await this.cacheGet(cacheKey);
    if (settings) {
      return settings;
    }

    const groupRepo = this.repositories.get('group');
    if (groupRepo) {
      settings = await groupRepo.getSettings(groupId);
      await this.cacheSet(cacheKey, settings, 21600); // Cache for 6 hours
    }

    return settings || {};
  }

  async cacheSet(key, value, ttl = 3600) {
    // Set cache value with TTL
    if (this.cache) {
      await this.cache.set(key, value, ttl);
    }
  }

  async cacheGet(key) {
    // Get cache value
    if (this.cache) {
      return await this.cache.get(key);
    }
    return null;
  }

  async invalidateCache(pattern) {
    // Invalidate cache entries matching pattern
    if (this.cache) {
      await this.cache.invalidate(pattern);
    }
  }

  async collectLatestResults() {
    // Trigger data collection from external sources
    const collector = this.collectors.get('lottery');
    if (collector) {
      return await collector.collectLatest();
    }
  }

  async logUserInteraction(userId, command, metadata = {}) {
    // Log user interaction for analytics and tracking
    try {
      const userRepo = this.repositories.get('user');
      if (userRepo) {
        await userRepo.logInteraction(userId, command, metadata);
      }

      // Also update user stats
      await this.updateUserStats(userId, {
        command,
        ...metadata
      });

      // Invalidate user stats cache
      await this.invalidateCache(`user:stats:${userId}`);
    } catch (error) {
      console.error('Failed to log user interaction:', error);
      // Don't throw error to avoid breaking the main flow
    }
  }

  async updateUserStats(userId, updateData) {
    // Update user statistics
    try {
      const userRepo = this.repositories.get('user');
      if (userRepo) {
        await userRepo.updateStats(userId, updateData);
      }
    } catch (error) {
      console.error('Failed to update user stats:', error);
    }
  }

  registerRepository(name, repository) {
    this.repositories.set(name, repository);
  }

  registerCollector(name, collector) {
    this.collectors.set(name, collector);
  }

  async getLotteryResultByDate(date, region = 'north') {
    // Get lottery result for specific date
    const cacheKey = `result:${region}:${date}`;

    let result = await this.cacheGet(cacheKey);
    if (result) {
      return result;
    }

    const lotteryRepo = this.repositories.get('lottery');
    if (lotteryRepo) {
      result = await lotteryRepo.getByDate(date, region);
      if (result) {
        await this.cacheSet(cacheKey, result, 7200); // Cache for 2 hours
      }
    }

    return result;
  }

  async getNumberStatistics(type = 'lo', days = 30, region = 'north') {
    // Get number frequency statistics
    const cacheKey = `stats:${type}:${region}:${days}`;

    let stats = await this.cacheGet(cacheKey);
    if (stats) {
      return stats;
    }

    const lotteryRepo = this.repositories.get('lottery');
    if (lotteryRepo) {
      const fromDate = new Date();
      fromDate.setDate(fromDate.getDate() - days);

      const history = await lotteryRepo.getHistory(fromDate, new Date(), region);

      if (history && history.length > 0) {
        stats = this._calculateNumberStatistics(history, type);
        await this.cacheSet(cacheKey, stats, 1800); // Cache for 30 minutes
      }
    }

    return stats || [];
  }

  _calculateNumberStatistics(history, type) {
    const numberFreq = new Map();
    const numberLastSeen = new Map();

    history.forEach(result => {
      const numbers = type === 'lo' ? result.numbers?.lo : result.numbers?.de;
      if (!numbers) return;

      numbers.forEach(num => {
        // Update frequency
        numberFreq.set(num, (numberFreq.get(num) || 0) + 1);

        // Update last seen (keep the most recent)
        const currentLastSeen = numberLastSeen.get(num);
        if (!currentLastSeen || new Date(result.date) > new Date(currentLastSeen)) {
          numberLastSeen.set(num, result.date);
        }
      });
    });

    // Convert to array and sort by frequency
    return Array.from(numberFreq.entries())
      .map(([number, count]) => ({
        number,
        count,
        lastSeen: numberLastSeen.get(number),
        frequency: ((count / history.length) * 100).toFixed(2)
      }))
      .sort((a, b) => b.count - a.count);
  }

  async getNumberFrequency(number, type = 'lo', days = 100, region = 'north') {
    // Get frequency of specific number
    const cacheKey = `freq:${number}:${type}:${region}:${days}`;

    let frequency = await this.cacheGet(cacheKey);
    if (frequency) {
      return frequency;
    }

    const lotteryRepo = this.repositories.get('lottery');
    if (lotteryRepo) {
      frequency = await lotteryRepo.getNumberFrequency(number, region, days);
      if (frequency) {
        await this.cacheSet(cacheKey, frequency, 3600); // Cache for 1 hour
      }
    }

    return frequency || { number, frequency: 0, period: days, lastSeen: null };
  }

  async getHotColdNumbers(type = 'lo', days = 30, region = 'north') {
    // Get hot and cold numbers based on frequency
    const stats = await this.getNumberStatistics(type, days, region);

    if (!stats || stats.length === 0) {
      return { hot: [], cold: [] };
    }

    const totalNumbers = stats.length;
    const hotThreshold = Math.ceil(totalNumbers * 0.2); // Top 20%
    const coldThreshold = Math.floor(totalNumbers * 0.8); // Bottom 20%

    return {
      hot: stats.slice(0, hotThreshold),
      cold: stats.slice(coldThreshold),
      period: days,
      type,
      region
    };
  }

  // Data Collection Methods

  /**
   * Collect lottery results from external sources
   * @param {Date} date - Date to collect
   * @param {string} region - Region
   * @returns {Object} Collected results
   */
  async collectLotteryResults(date, region = 'north') {
    if (!this.externalApi) {
      throw new Error('External API manager not initialized');
    }

    try {
      console.log(`🔍 Collecting lottery results for ${date.toDateString()}`);

      const results = await this.externalApi.fetchResults(date, region);

      // Save to database if repository is available
      const lotteryRepo = this.repositories.get('lottery');
      if (lotteryRepo) {
        await lotteryRepo.create(results);
        console.log(`💾 Saved results to database for ${date.toDateString()}`);
      }

      return results;

    } catch (error) {
      console.error(`❌ Failed to collect lottery results for ${date.toDateString()}:`, error.message);
      throw error;
    }
  }

  /**
   * Collect results for multiple dates
   * @param {Date} startDate - Start date
   * @param {Date} endDate - End date
   * @param {string} region - Region
   * @returns {Object} Collection summary
   */
  async collectBatchResults(startDate, endDate, region = 'north') {
    if (!this.externalApi) {
      throw new Error('External API manager not initialized');
    }

    console.log(`📦 Starting batch collection from ${startDate.toDateString()} to ${endDate.toDateString()}`);

    const batchResult = await this.externalApi.fetchBatch(startDate, endDate, region, {
      batchSize: 3,
      delayBetweenBatches: 2000,
      continueOnError: true
    });

    // Save successful results to database
    const lotteryRepo = this.repositories.get('lottery');
    if (lotteryRepo && batchResult.results.length > 0) {
      let savedCount = 0;

      for (const result of batchResult.results) {
        try {
          await lotteryRepo.create(result);
          savedCount++;
        } catch (error) {
          console.warn(`⚠️ Failed to save result for ${result.date}: ${error.message}`);
        }
      }

      console.log(`💾 Saved ${savedCount}/${batchResult.results.length} results to database`);
    }

    return {
      ...batchResult.summary,
      savedToDatabase: lotteryRepo ? true : false
    };
  }

  /**
   * Collect latest results
   * @param {string} region - Region
   * @returns {Object} Latest results
   */
  async collectLatestResults(region = 'north') {
    if (!this.externalApi) {
      throw new Error('External API manager not initialized');
    }

    try {
      const results = await this.externalApi.fetchLatest(region);

      // Save to database
      const lotteryRepo = this.repositories.get('lottery');
      if (lotteryRepo) {
        await lotteryRepo.create(results);
        console.log(`💾 Saved latest results to database`);
      }

      return results;

    } catch (error) {
      console.error(`❌ Failed to collect latest results:`, error.message);
      throw error;
    }
  }

  async updateGroupSettings(groupId, settings) {
    // Update group settings
    const groupRepo = this.repositories.get('group');
    if (groupRepo) {
      await groupRepo.updateSettings(groupId, settings);
      // Invalidate cache
      await this.invalidateCache(`group:settings:${groupId}`);
    }
  }

  async logAnalytics(eventType, data) {
    // Log analytics data
    try {
      const analyticsRepo = this.repositories.get('analytics');
      if (analyticsRepo) {
        await analyticsRepo.log(eventType, data);
      }
    } catch (error) {
      console.error('Failed to log analytics:', error);
    }
  }

  async getAnalytics(type = 'daily', fromDate, toDate) {
    // Get analytics data
    const analyticsRepo = this.repositories.get('analytics');
    if (analyticsRepo) {
      return await analyticsRepo.getAnalytics(type, fromDate, toDate);
    }
    return null;
  }

  setCache(cache) {
    this.cache = cache;
  }

  // Health check method
  async healthCheck() {
    const health = {
      status: 'healthy',
      repositories: {},
      cache: null,
      collectors: {},
      timestamp: new Date()
    };

    // Check repositories
    for (const [name, repo] of this.repositories.entries()) {
      try {
        if (typeof repo.healthCheck === 'function') {
          health.repositories[name] = await repo.healthCheck();
        } else {
          health.repositories[name] = { status: 'available' };
        }
      } catch (error) {
        health.repositories[name] = { status: 'error', error: error.message };
        health.status = 'degraded';
      }
    }

    // Check cache
    try {
      if (this.cache && typeof this.cache.ping === 'function') {
        await this.cache.ping();
        health.cache = { status: 'available' };
      } else if (this.cache) {
        health.cache = { status: 'available' };
      } else {
        health.cache = { status: 'not_configured' };
      }
    } catch (error) {
      health.cache = { status: 'error', error: error.message };
      health.status = 'degraded';
    }

    // Check collectors
    for (const [name, collector] of this.collectors.entries()) {
      try {
        if (typeof collector.healthCheck === 'function') {
          health.collectors[name] = await collector.healthCheck();
        } else {
          health.collectors[name] = { status: 'available' };
        }
      } catch (error) {
        health.collectors[name] = { status: 'error', error: error.message };
      }
    }

    return health;
  }
}

module.exports = DataManager;