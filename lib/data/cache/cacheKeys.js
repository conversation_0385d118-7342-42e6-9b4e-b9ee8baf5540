// Cache Key Management
// Centralized cache key definitions and utilities

class CacheKeys {
  constructor(prefix = 'lottery:') {
    this.prefix = prefix;
  }

  // Prediction cache keys
  predictions = {
    lo: (date) => `${this.prefix}predictions:lo:${date}`,
    de: (date) => `${this.prefix}predictions:de:${date}`,
    pattern: () => `${this.prefix}predictions:*`
  };

  // Lottery data cache keys
  lottery = {
    history: (region, fromDate, toDate) => `${this.prefix}history:${region}:${fromDate}:${toDate}`,
    latest: (region, limit) => `${this.prefix}latest:${region}:${limit}`,
    byDate: (date, region) => `${this.prefix}result:${date}:${region}`,
    frequency: (number, region, days) => `${this.prefix}frequency:${number}:${region}:${days}`,
    statistics: (region, days) => `${this.prefix}stats:${region}:${days}`,
    pattern: () => `${this.prefix}history:*`
  };

  // Trend analysis cache keys
  trends = {
    lo: (period) => `${this.prefix}trends:lo:${period}`,
    de: (period) => `${this.prefix}trends:de:${period}`,
    hot: (type, period) => `${this.prefix}trends:hot:${type}:${period}`,
    cold: (type, period) => `${this.prefix}trends:cold:${type}:${period}`,
    pattern: () => `${this.prefix}trends:*`
  };

  // User data cache keys
  user = {
    stats: (userId) => `${this.prefix}user:stats:${userId}`,
    preferences: (userId) => `${this.prefix}user:prefs:${userId}`,
    activity: (userId) => `${this.prefix}user:activity:${userId}`,
    pattern: () => `${this.prefix}user:*`
  };

  // Group data cache keys
  group = {
    settings: (groupId) => `${this.prefix}group:settings:${groupId}`,
    stats: (groupId) => `${this.prefix}group:stats:${groupId}`,
    members: (groupId) => `${this.prefix}group:members:${groupId}`,
    pattern: () => `${this.prefix}group:*`
  };

  // Model performance cache keys
  model = {
    accuracy: (modelType) => `${this.prefix}model:accuracy:${modelType}`,
    version: (modelType) => `${this.prefix}model:version:${modelType}`,
    metrics: (modelType) => `${this.prefix}model:metrics:${modelType}`,
    pattern: () => `${this.prefix}model:*`
  };

  // System cache keys
  system = {
    health: () => `${this.prefix}system:health`,
    config: () => `${this.prefix}system:config`,
    locks: (resource) => `${this.prefix}locks:${resource}`,
    pattern: () => `${this.prefix}system:*`
  };

  // Analytics cache keys
  analytics = {
    daily: (date) => `${this.prefix}analytics:daily:${date}`,
    weekly: (week) => `${this.prefix}analytics:weekly:${week}`,
    monthly: (month) => `${this.prefix}analytics:monthly:${month}`,
    popular: (period) => `${this.prefix}analytics:popular:${period}`,
    pattern: () => `${this.prefix}analytics:*`
  };

  // Utility methods
  generateKey(template, ...args) {
    // Generate cache key from template and arguments
    return template.replace(/\{(\d+)\}/g, (match, index) => {
      return args[index] || match;
    });
  }

  extractDate(key) {
    // Extract date from cache key
    const dateMatch = key.match(/(\d{4}-\d{2}-\d{2})/);
    return dateMatch ? dateMatch[1] : null;
  }

  isExpired(key, maxAge) {
    // Check if key should be considered expired based on date
    const date = this.extractDate(key);
    if (!date) return false;

    const keyDate = new Date(date);
    const now = new Date();
    const ageInMs = now - keyDate;
    const maxAgeInMs = maxAge * 1000;

    return ageInMs > maxAgeInMs;
  }

  getKeysByPattern(pattern) {
    // Get all keys matching pattern (to be used with Redis KEYS command)
    return `${this.prefix}${pattern}`;
  }

  removePrefix(key) {
    // Remove prefix from key
    return key.startsWith(this.prefix) ? key.substring(this.prefix.length) : key;
  }

  addPrefix(key) {
    // Add prefix to key
    return key.startsWith(this.prefix) ? key : `${this.prefix}${key}`;
  }

  // TTL strategies
  getTTL(keyType, context = {}) {
    const strategies = {
      // Prediction TTLs
      'predictions:lo': 24 * 60 * 60, // 24 hours
      'predictions:de': 24 * 60 * 60, // 24 hours

      // Lottery data TTLs
      'history': 6 * 60 * 60, // 6 hours
      'latest': 30 * 60, // 30 minutes
      'result': 24 * 60 * 60, // 24 hours (historical data)
      'frequency': 2 * 60 * 60, // 2 hours
      'stats': 1 * 60 * 60, // 1 hour

      // Trend analysis TTLs
      'trends': 1 * 60 * 60, // 1 hour
      'trends:hot': 30 * 60, // 30 minutes
      'trends:cold': 30 * 60, // 30 minutes

      // User data TTLs
      'user:stats': 1 * 60 * 60, // 1 hour
      'user:prefs': 6 * 60 * 60, // 6 hours
      'user:activity': 15 * 60, // 15 minutes

      // Group data TTLs
      'group:settings': 6 * 60 * 60, // 6 hours
      'group:stats': 1 * 60 * 60, // 1 hour
      'group:members': 30 * 60, // 30 minutes

      // Model performance TTLs
      'model:accuracy': 1 * 60 * 60, // 1 hour
      'model:version': 24 * 60 * 60, // 24 hours
      'model:metrics': 30 * 60, // 30 minutes

      // System TTLs
      'system:health': 5 * 60, // 5 minutes
      'system:config': 1 * 60 * 60, // 1 hour
      'locks': 10 * 60, // 10 minutes

      // Analytics TTLs
      'analytics:daily': 24 * 60 * 60, // 24 hours
      'analytics:weekly': 7 * 24 * 60 * 60, // 7 days
      'analytics:monthly': 30 * 24 * 60 * 60, // 30 days
      'analytics:popular': 1 * 60 * 60 // 1 hour
    };

    // Dynamic TTL based on context
    if (context.isHistorical) {
      return strategies[keyType] * 2; // Double TTL for historical data
    }

    if (context.isRealTime) {
      return Math.min(strategies[keyType], 5 * 60); // Max 5 minutes for real-time data
    }

    return strategies[keyType] || 3600; // Default 1 hour
  }

  // Cache warming keys
  getWarmupKeys() {
    const today = new Date().toISOString().split('T')[0];
    const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString().split('T')[0];

    return [
      // Recent predictions
      this.predictions.lo(today),
      this.predictions.de(today),

      // Latest lottery results
      this.lottery.latest('north', 10),
      this.lottery.byDate(today, 'north'),
      this.lottery.byDate(yesterday, 'north'),

      // Current trends
      this.trends.lo('weekly'),
      this.trends.de('weekly'),
      this.trends.hot('lo', 'daily'),
      this.trends.cold('lo', 'daily'),

      // System health
      this.system.health(),

      // Popular analytics
      this.analytics.popular('daily')
    ];
  }
}

module.exports = CacheKeys;