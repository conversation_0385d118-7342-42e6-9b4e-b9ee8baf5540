// Redis Cache Implementation
// Handles caching operations with Redis

const CacheKeys = require('./cacheKeys');

class RedisCache {
  constructor(redisClient, options = {}) {
    this.client = redisClient;
    this.options = {
      keyPrefix: options.keyPrefix || 'lottery:',
      defaultTTL: options.defaultTTL || 3600,
      enableWarmup: options.enableWarmup !== false,
      warmupInterval: options.warmupInterval || 30 * 60 * 1000, // 30 minutes
      maxRetries: options.maxRetries || 3,
      retryDelay: options.retryDelay || 1000,
      ...options
    };

    this.keys = new CacheKeys(this.options.keyPrefix);
    this.warmupTimer = null;
    this.stats = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      errors: 0
    };

    // Start cache warming if enabled
    if (this.options.enableWarmup) {
      this.startWarmup();
    }
  }

  async set(key, value, ttl = null) {
    // Set cache value with intelligent TTL and retry logic
    try {
      const fullKey = this.keys.addPrefix(key);
      const serializedValue = JSON.stringify(value);
      
      // Determine TTL using strategy
      let expiry = ttl;
      if (!expiry) {
        const keyType = this.extractKeyType(key);
        expiry = this.keys.getTTL(keyType, { isRealTime: this.isRealTimeData(value) });
      }

      await this.executeWithRetry(async () => {
        await this.client.setEx(fullKey, expiry, serializedValue);
      });

      this.stats.sets++;
      return true;
    } catch (error) {
      this.stats.errors++;
      console.error('Redis set error:', error.message);
      return false;
    }
  }

  async get(key) {
    // Get cache value with retry logic
    try {
      const fullKey = this.keys.addPrefix(key);
      const value = await this.executeWithRetry(async () => {
        return await this.client.get(fullKey);
      });

      if (value) {
        this.stats.hits++;
        return JSON.parse(value);
      } else {
        this.stats.misses++;
        return null;
      }
    } catch (error) {
      this.stats.errors++;
      console.error('Redis get error:', error.message);
      return null;
    }
  }

  async del(key) {
    // Delete cache entry
    try {
      const fullKey = this.keys.addPrefix(key);
      const result = await this.client.del(fullKey);
      this.stats.deletes += result;
      return result;
    } catch (error) {
      this.stats.errors++;
      console.error('Redis delete error:', error.message);
      return false;
    }
  }

  async invalidate(pattern) {
    // Invalidate cache entries matching pattern
    try {
      const fullPattern = this.options.keyPrefix + pattern;
      const keys = await this.client.keys(fullPattern);

      if (keys.length > 0) {
        return await this.client.del(keys);
      }

      return 0;
    } catch (error) {
      console.error('Redis invalidate error:', error.message);
      return false;
    }
  }

  async exists(key) {
    // Check if key exists
    try {
      const fullKey = this.keys.addPrefix(key);
      return await this.client.exists(fullKey);
    } catch (error) {
      console.error('Redis exists error:', error.message);
      return false;
    }
  }

  async ttl(key) {
    // Get TTL for key
    try {
      const fullKey = this.keys.addPrefix(key);
      return await this.client.ttl(fullKey);
    } catch (error) {
      console.error('Redis TTL error:', error.message);
      return -1;
    }
  }

  async increment(key, amount = 1) {
    // Increment numeric value
    try {
      const fullKey = this.keys.addPrefix(key);
      return await this.client.incrBy(fullKey, amount);
    } catch (error) {
      console.error('Redis increment error:', error.message);
      return null;
    }
  }

  async setHash(key, field, value, ttl = null) {
    // Set hash field
    try {
      const fullKey = this.keys.addPrefix(key);
      await this.client.hSet(fullKey, field, JSON.stringify(value));

      if (ttl) {
        await this.client.expire(fullKey, ttl);
      }

      return true;
    } catch (error) {
      console.error('Redis hash set error:', error.message);
      return false;
    }
  }

  async getHash(key, field = null) {
    // Get hash field or entire hash
    try {
      const fullKey = this.keys.addPrefix(key);

      if (field) {
        const value = await this.client.hGet(fullKey, field);
        return value ? JSON.parse(value) : null;
      } else {
        const hash = await this.client.hGetAll(fullKey);
        const result = {};

        for (const [k, v] of Object.entries(hash)) {
          result[k] = JSON.parse(v);
        }

        return result;
      }
    } catch (error) {
      console.error('Redis hash get error:', error.message);
      return null;
    }
  }

  async getStats() {
    // Get cache statistics
    try {
      const info = await this.client.info('memory');
      const keyCount = await this.client.dbSize();

      return {
        keyCount,
        memoryInfo: info,
        connected: this.client.isReady
      };
    } catch (error) {
      console.error('Redis stats error:', error.message);
      return null;
    }
  }

  async flush() {
    // Clear all cache entries with prefix
    try {
      const keys = await this.client.keys(this.options.keyPrefix + '*');
      if (keys.length > 0) {
        const deleted = await this.client.del(keys);
        this.stats.deletes += deleted;
        return deleted;
      }
      return 0;
    } catch (error) {
      this.stats.errors++;
      console.error('Redis flush error:', error.message);
      return false;
    }
  }

  // Cache warming methods
  async warmCache(dataProvider) {
    // Warm cache with frequently accessed data
    try {
      const warmupKeys = this.keys.getWarmupKeys();
      const promises = [];

      for (const key of warmupKeys) {
        promises.push(this.warmCacheKey(key, dataProvider));
      }

      const results = await Promise.allSettled(promises);
      const successful = results.filter(r => r.status === 'fulfilled').length;
      
      console.log(`Cache warming completed: ${successful}/${warmupKeys.length} keys warmed`);
      return successful;
    } catch (error) {
      console.error('Cache warming error:', error.message);
      return 0;
    }
  }

  async warmCacheKey(key, dataProvider) {
    // Warm specific cache key
    try {
      const exists = await this.exists(this.keys.removePrefix(key));
      if (exists) {
        return true; // Already cached
      }

      // Get data from provider
      const data = await dataProvider.getData(key);
      if (data) {
        await this.set(this.keys.removePrefix(key), data);
        return true;
      }

      return false;
    } catch (error) {
      console.error(`Cache warming error for key ${key}:`, error.message);
      return false;
    }
  }

  startWarmup() {
    // Start periodic cache warming
    if (this.warmupTimer) {
      clearInterval(this.warmupTimer);
    }

    this.warmupTimer = setInterval(() => {
      // Warmup will be triggered by external data provider
      console.log('Cache warmup interval triggered');
    }, this.options.warmupInterval);
  }

  stopWarmup() {
    // Stop cache warming
    if (this.warmupTimer) {
      clearInterval(this.warmupTimer);
      this.warmupTimer = null;
    }
  }

  // Cache invalidation methods
  async invalidateByPattern(pattern) {
    // Invalidate cache entries matching pattern with batching
    try {
      const fullPattern = this.keys.getKeysByPattern(pattern);
      const keys = await this.client.keys(fullPattern);

      if (keys.length === 0) {
        return 0;
      }

      // Process in batches to avoid blocking Redis
      const batchSize = 100;
      let totalDeleted = 0;

      for (let i = 0; i < keys.length; i += batchSize) {
        const batch = keys.slice(i, i + batchSize);
        const deleted = await this.client.del(batch);
        totalDeleted += deleted;
      }

      this.stats.deletes += totalDeleted;
      console.log(`Invalidated ${totalDeleted} cache entries matching pattern: ${pattern}`);
      return totalDeleted;
    } catch (error) {
      this.stats.errors++;
      console.error('Cache invalidation error:', error.message);
      return false;
    }
  }

  async invalidateByTags(tags) {
    // Invalidate cache entries by tags (requires tag tracking)
    try {
      const promises = tags.map(tag => this.invalidateByPattern(`*:${tag}:*`));
      const results = await Promise.all(promises);
      return results.reduce((sum, count) => sum + (count || 0), 0);
    } catch (error) {
      this.stats.errors++;
      console.error('Tag-based invalidation error:', error.message);
      return false;
    }
  }

  async invalidateExpired() {
    // Remove expired keys (Redis handles this automatically, but we can clean up manually)
    try {
      const allKeys = await this.client.keys(this.options.keyPrefix + '*');
      let expiredCount = 0;

      for (const key of allKeys) {
        const ttl = await this.client.ttl(key);
        if (ttl === -1) { // Key exists but has no expiry
          const keyWithoutPrefix = this.keys.removePrefix(key);
          if (this.keys.isExpired(keyWithoutPrefix, 24 * 60 * 60)) { // 24 hours
            await this.client.del(key);
            expiredCount++;
          }
        }
      }

      this.stats.deletes += expiredCount;
      return expiredCount;
    } catch (error) {
      this.stats.errors++;
      console.error('Expired key cleanup error:', error.message);
      return 0;
    }
  }

  // Utility methods
  async executeWithRetry(operation) {
    // Execute operation with retry logic
    let lastError;
    
    for (let attempt = 1; attempt <= this.options.maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;
        
        if (attempt < this.options.maxRetries) {
          const delay = this.options.retryDelay * Math.pow(2, attempt - 1); // Exponential backoff
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }
    
    throw lastError;
  }

  extractKeyType(key) {
    // Extract key type for TTL strategy
    const parts = key.split(':');
    if (parts.length >= 2) {
      return `${parts[0]}:${parts[1]}`;
    }
    return parts[0] || 'default';
  }

  isRealTimeData(value) {
    // Determine if data is real-time based on content
    if (typeof value === 'object' && value.timestamp) {
      const age = Date.now() - new Date(value.timestamp).getTime();
      return age < 5 * 60 * 1000; // Less than 5 minutes old
    }
    return false;
  }

  // Advanced caching methods
  async mget(keys) {
    // Get multiple cache values
    try {
      const fullKeys = keys.map(key => this.keys.addPrefix(key));
      const values = await this.client.mGet(fullKeys);
      
      return values.map((value, index) => {
        if (value) {
          this.stats.hits++;
          return JSON.parse(value);
        } else {
          this.stats.misses++;
          return null;
        }
      });
    } catch (error) {
      this.stats.errors++;
      console.error('Redis mget error:', error.message);
      return keys.map(() => null);
    }
  }

  async mset(keyValuePairs, ttl = null) {
    // Set multiple cache values
    try {
      const pipeline = this.client.multi();
      
      for (const [key, value] of keyValuePairs) {
        const fullKey = this.keys.addPrefix(key);
        const serializedValue = JSON.stringify(value);
        const expiry = ttl || this.keys.getTTL(this.extractKeyType(key));
        
        pipeline.setEx(fullKey, expiry, serializedValue);
      }
      
      await pipeline.exec();
      this.stats.sets += keyValuePairs.length;
      return true;
    } catch (error) {
      this.stats.errors++;
      console.error('Redis mset error:', error.message);
      return false;
    }
  }

  async lock(resource, ttl = 60) {
    // Distributed lock implementation
    try {
      const lockKey = this.keys.system.locks(resource);
      const lockValue = `${Date.now()}-${Math.random()}`;
      
      const result = await this.client.set(lockKey, lockValue, {
        PX: ttl * 1000,
        NX: true
      });
      
      return result === 'OK' ? lockValue : null;
    } catch (error) {
      console.error('Redis lock error:', error.message);
      return null;
    }
  }

  async unlock(resource, lockValue) {
    // Release distributed lock
    try {
      const lockKey = this.keys.system.locks(resource);
      const script = `
        if redis.call("get", KEYS[1]) == ARGV[1] then
          return redis.call("del", KEYS[1])
        else
          return 0
        end
      `;
      
      return await this.client.eval(script, 1, lockKey, lockValue);
    } catch (error) {
      console.error('Redis unlock error:', error.message);
      return false;
    }
  }

  // Statistics and monitoring
  getStats() {
    // Get cache statistics
    return {
      ...this.stats,
      hitRate: this.stats.hits / (this.stats.hits + this.stats.misses) || 0,
      connected: this.client.isReady,
      warmupEnabled: this.options.enableWarmup,
      keyPrefix: this.options.keyPrefix
    };
  }

  resetStats() {
    // Reset statistics
    this.stats = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      errors: 0
    };
  }

  // Cleanup
  async disconnect() {
    // Clean disconnect
    this.stopWarmup();
    if (this.client && this.client.isReady) {
      await this.client.quit();
    }
  }
}

module.exports = RedisCache;