# Tài <PERSON>ế<PERSON>ế

## Tổng <PERSON>uan

Bot Telegram dự đoán xổ số miền B<PERSON><PERSON> đ<PERSON> thiết kế theo kiến trúc microservice với các thành phần chính: <PERSON><PERSON>, Prediction Engine, Data Manager, và Scheduler. Hệ thống sử dụng MongoDB để lưu trữ dữ liệu lịch sử và Redis để cache, tích hợp mô hình LSTM/RNN cho dự đoán và OpenAI API cho báo cáo thông minh.

## Kiến Trúc

### Kiến Trúc Tổng Thể

```mermaid
graph TB
    TG[Telegram Users] --> BOT[Bo<PERSON> Handler]
    BOT --> PE[Prediction Engine]
    BOT --> DM[Data Manager]
    BOT --> LLM[LLM Service]

    PE --> ML[ML Models]
    PE --> STATS[Statistics Engine]

    DM --> MONGO[(MongoDB)]
    DM --> REDIS[(Redis Cache)]

    SCHED[Scheduler] --> BOT
    SCHED --> PE

    EXT[External APIs] --> DM

    subgraph "Core Services"
        BOT
        PE
        DM
        LLM
    end

    subgraph "Data Layer"
        MONGO
        REDIS
    end

    subgraph "ML Layer"
        ML
        STATS
    end
```

### Luồng Dữ Liệu

```mermaid
sequenceDiagram
    participant U as User
    participant B as Bot Handler
    participant P as Prediction Engine
    participant D as Data Manager
    participant L as LLM Service
    participant C as Cache

    U->>B: /dukienlo
    B->>C: Check cache
    alt Cache hit
        C->>B: Return cached result
    else Cache miss
        B->>P: Request prediction
        P->>D: Get historical data
        D->>P: Return data
        P->>P: Run ML model
        P->>B: Return prediction
        B->>C: Cache result
    end
    B->>L: Generate report
    L->>B: Return formatted report
    B->>U: Send prediction + report
```

## Thành Phần và Giao Diện

### 1. Bot Handler (`lib/bot/`)

**Chức năng:** Xử lý tương tác Telegram, routing lệnh, quản lý session

**Cấu trúc:**
```
lib/bot/
├── index.js              # Bot chính và khởi tạo
├── handlers/             # Xử lý các lệnh
│   ├── prediction.js     # /dukienlo, /dukiende
│   ├── history.js        # /lichsu
│   ├── trends.js         # /xuhuonglo, /xuhuongde
│   ├── analysis.js       # /number
│   └── help.js          # /help
├── middleware/           # Middleware cho bot
│   ├── auth.js          # Xác thực người dùng
│   ├── rateLimit.js     # Giới hạn tốc độ
│   └── logging.js       # Ghi log tương tác
└── utils/
    ├── formatter.js     # Format tin nhắn
    └── keyboard.js      # Inline keyboards
```

**Giao diện chính:**
```javascript
class BotHandler {
  constructor(token, options)
  start()
  stop()
  registerCommand(command, handler)
  sendMessage(chatId, message, options)
  sendScheduledMessage(groupIds, message)
}
```

### 2. Prediction Engine (`lib/prediction/`)

**Chức năng:** Thực hiện dự đoán sử dụng ML và thống kê

**Cấu trúc:**
```
lib/prediction/
├── index.js              # Engine chính
├── models/               # Mô hình ML
│   ├── lstm.js          # LSTM model
│   ├── statistical.js   # Thống kê cơ bản
│   └── ensemble.js      # Kết hợp nhiều mô hình
├── trainers/             # Huấn luyện mô hình
│   ├── dataPreprocessor.js
│   ├── modelTrainer.js
│   └── validator.js
└── analyzers/            # Phân tích xu hướng
    ├── hotCold.js       # Phân tích số nóng/lạnh
    ├── patterns.js      # Phát hiện mẫu
    └── frequency.js     # Tần suất xuất hiện
```

**Giao diện chính:**
```javascript
class PredictionEngine {
  predictLo(options)       // Dự đoán số lô
  predictDe(options)       // Dự đoán số đề
  analyzeNumber(number)    // Phân tích số cụ thể
  getTrends(type, period)  // Lấy xu hướng
  updateModel(newData)     // Cập nhật mô hình
  getAccuracy()           // Độ chính xác hiện tại
}
```

### 3. Data Manager (`lib/data/`)

**Chức năng:** Quản lý dữ liệu, cache, và tương tác database

**Cấu trúc:**
```
lib/data/
├── index.js              # Manager chính
├── repositories/         # Data access layer
│   ├── lotteryRepo.js   # Dữ liệu xổ số
│   ├── userRepo.js      # Dữ liệu người dùng
│   ├── groupRepo.js     # Dữ liệu nhóm
│   └── analyticsRepo.js # Dữ liệu phân tích
├── cache/                # Cache management
│   ├── redisCache.js    # Redis operations
│   └── cacheKeys.js     # Cache key definitions
└── collectors/           # Thu thập dữ liệu
    ├── lotteryCollector.js
    └── externalApi.js
```

**Giao diện chính:**
```javascript
class DataManager {
  getLotteryHistory(fromDate, toDate)
  saveLotteryResult(result)
  getUserStats(userId)
  getGroupSettings(groupId)
  cacheSet(key, value, ttl)
  cacheGet(key)
  collectLatestResults()
}
```

### 4. LLM Service (`lib/llm/`)

**Chức năng:** Tích hợp AI để tạo báo cáo và phân tích

**Cấu trúc:**
```
lib/llm/
├── index.js              # Service chính
├── providers/            # Các nhà cung cấp LLM
│   ├── openai.js        # OpenAI integration
│   └── fallback.js      # Fallback khi LLM không khả dụng
├── prompts/              # Template prompts
│   ├── prediction.js    # Prompts cho dự đoán
│   ├── analysis.js      # Prompts cho phân tích
│   └── trends.js        # Prompts cho xu hướng
└── formatters/           # Format output
    └── vietnamese.js     # Định dạng tiếng Việt
```

### 5. Scheduler (`lib/scheduler/`)

**Chức năng:** Quản lý tác vụ định kỳ và thông báo tự động

**Cấu trúc:**
```
lib/scheduler/
├── index.js              # Scheduler chính
├── jobs/                 # Định nghĩa jobs
│   ├── dailyPrediction.js
│   ├── weeklyReport.js
│   ├── dataCollection.js
│   └── modelRetraining.js
└── cron/                 # Cron job management
    └── cronManager.js
```

## Mô Hình Dữ Liệu

### MongoDB Collections

#### 1. LotteryResults
```javascript
{
  _id: ObjectId,
  date: Date,                    // Ngày quay
  region: String,                // "north", "central", "south"
  prizes: {
    special: String,             // Giải đặc biệt
    first: String,               // Giải nhất
    second: [String],            // Giải nhì
    third: [String],             // Giải ba
    fourth: [String],            // Giải tư
    fifth: [String],             // Giải năm
    sixth: [String],             // Giải sáu
    seventh: [String]            // Giải bảy
  },
  numbers: {
    lo: [String],                // Các số lô
    de: [String]                 // Các số đề
  },
  createdAt: Date,
  updatedAt: Date
}
```

#### 2. Predictions
```javascript
{
  _id: ObjectId,
  date: Date,                    // Ngày dự đoán
  type: String,                  // "lo" hoặc "de"
  predictions: [{
    number: String,              // Số dự đoán
    confidence: Number,          // Độ tin cậy (0-100)
    method: String,              // "lstm", "statistical", "ensemble"
    reasoning: String            // Lý do dự đoán
  }],
  accuracy: Number,              // Độ chính xác thực tế (sau khi có kết quả)
  modelVersion: String,          // Phiên bản mô hình
  createdAt: Date
}
```

#### 3. Users
```javascript
{
  _id: ObjectId,
  telegramId: Number,
  username: String,
  firstName: String,
  lastName: String,
  preferences: {
    notifications: Boolean,
    favoriteNumbers: [String],
    timezone: String
  },
  statistics: {
    totalQueries: Number,
    lastActive: Date,
    mostQueriedNumbers: [String]
  },
  createdAt: Date,
  updatedAt: Date
}
```

#### 4. Groups
```javascript
{
  _id: ObjectId,
  telegramId: Number,
  title: String,
  type: String,                  // "group", "supergroup", "channel"
  settings: {
    dailyPredictions: Boolean,
    predictionTime: String,      // "08:00"
    weeklyReports: Boolean,
    language: String             // "vi"
  },
  statistics: {
    memberCount: Number,
    totalMessages: Number,
    lastActivity: Date
  },
  createdAt: Date,
  updatedAt: Date
}
```

#### 5. Analytics
```javascript
{
  _id: ObjectId,
  date: Date,
  type: String,                  // "daily", "weekly", "monthly"
  metrics: {
    totalUsers: Number,
    activeUsers: Number,
    totalQueries: Number,
    popularNumbers: [{
      number: String,
      count: Number
    }],
    commandUsage: {
      dukienlo: Number,
      dukiende: Number,
      lichsu: Number,
      xuhuonglo: Number,
      xuhuongde: Number,
      number: Number
    }
  },
  createdAt: Date
}
```

### Redis Cache Structure

```
lottery:predictions:lo:YYYY-MM-DD     -> JSON (TTL: 24h)
lottery:predictions:de:YYYY-MM-DD     -> JSON (TTL: 24h)
lottery:trends:lo:weekly              -> JSON (TTL: 1h)
lottery:trends:de:weekly              -> JSON (TTL: 1h)
lottery:history:latest                -> JSON (TTL: 30m)
user:stats:${userId}                  -> JSON (TTL: 1h)
group:settings:${groupId}             -> JSON (TTL: 6h)
model:accuracy:current                -> Number (TTL: 1h)
```

## Xử Lý Lỗi

### Chiến Lược Xử Lý Lỗi

1. **Circuit Breaker Pattern:** Cho external APIs và database connections
2. **Retry Mechanism:** Với exponential backoff cho network operations
3. **Graceful Degradation:** Fallback khi services không khả dụng
4. **Error Logging:** Chi tiết với context và stack trace

### Các Loại Lỗi và Xử Lý

```javascript
// Database Connection Errors
class DatabaseError extends Error {
  constructor(message, operation, collection) {
    super(message)
    this.name = 'DatabaseError'
    this.operation = operation
    this.collection = collection
  }
}

// Prediction Model Errors
class ModelError extends Error {
  constructor(message, modelType, inputData) {
    super(message)
    this.name = 'ModelError'
    this.modelType = modelType
    this.inputData = inputData
  }
}

// Telegram API Errors
class TelegramError extends Error {
  constructor(message, chatId, method) {
    super(message)
    this.name = 'TelegramError'
    this.chatId = chatId
    this.method = method
  }
}
```

### Error Recovery Strategies

```javascript
const errorHandlers = {
  DatabaseError: async (error) => {
    // Retry with exponential backoff
    // Switch to backup database if available
    // Cache fallback for read operations
  },

  ModelError: async (error) => {
    // Use statistical fallback
    // Log for model retraining
    // Return cached predictions if available
  },

  TelegramError: async (error) => {
    // Retry message sending
    // Queue for later delivery
    // Notify admins if persistent
  }
}
```

## Chiến Lược Testing

### Unit Tests
- **Coverage:** Tối thiểu 80% cho core business logic
- **Framework:** Jest với mocking cho external dependencies
- **Focus Areas:**
  - Prediction algorithms
  - Data validation và transformation
  - Cache operations
  - Error handling

### Integration Tests
- **Database Operations:** Test với MongoDB test instance
- **Redis Cache:** Test cache operations và TTL
- **Telegram Bot:** Mock Telegram API responses
- **ML Models:** Test với sample data sets

### Performance Tests
- **Load Testing:** Simulate concurrent users
- **Memory Usage:** Monitor memory leaks
- **Response Time:** Ensure < 3 second response times
- **Database Performance:** Query optimization

### Test Structure
```
tests/
├── unit/
│   ├── prediction/
│   ├── data/
│   ├── bot/
│   └── llm/
├── integration/
│   ├── database/
│   ├── cache/
│   └── api/
├── performance/
│   ├── load/
│   └── stress/
└── fixtures/
    ├── lotteryData.js
    └── mockResponses.js
```

## Bảo Mật

### Authentication & Authorization
- **Bot Token Security:** Lưu trữ trong environment variables
- **API Keys:** Encryption at rest và in transit
- **User Permissions:** Role-based access cho admin commands
- **Rate Limiting:** Prevent spam và abuse

### Data Protection
- **Personal Data:** Anonymization cho analytics
- **Encryption:** Sensitive data encryption
- **Audit Logging:** Track admin actions
- **GDPR Compliance:** Data retention policies

### Security Headers & Validation
```javascript
const securityMiddleware = {
  validateInput: (input) => {
    // Sanitize user input
    // Prevent injection attacks
    // Validate data types
  },

  rateLimit: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100 // limit each IP to 100 requests per windowMs
  },

  encryption: {
    algorithm: 'aes-256-gcm',
    keyDerivation: 'pbkdf2'
  }
}
```

## Monitoring và Logging

### Logging Strategy
```javascript
const logLevels = {
  ERROR: 'error',    // System errors, exceptions
  WARN: 'warn',      // Performance issues, deprecations
  INFO: 'info',      // General application flow
  DEBUG: 'debug'     // Detailed debugging information
}

const logCategories = {
  BOT: 'bot',           // Telegram bot interactions
  PREDICTION: 'pred',   // ML predictions và accuracy
  DATA: 'data',         // Database operations
  CACHE: 'cache',       // Redis operations
  SCHEDULER: 'sched',   // Cron jobs và scheduling
  SECURITY: 'sec'       // Security events
}
```

### Metrics Collection
- **Response Times:** Average, P95, P99
- **Error Rates:** By category và severity
- **User Engagement:** Active users, command usage
- **Model Performance:** Accuracy trends, prediction confidence
- **System Health:** Memory, CPU, database connections

### Alerting Rules
```javascript
const alerts = {
  highErrorRate: {
    condition: 'error_rate > 5%',
    duration: '5m',
    action: 'notify_admins'
  },

  slowResponse: {
    condition: 'avg_response_time > 5s',
    duration: '2m',
    action: 'scale_up'
  },

  modelAccuracy: {
    condition: 'prediction_accuracy < 15%',
    duration: '1h',
    action: 'retrain_model'
  }
}
```