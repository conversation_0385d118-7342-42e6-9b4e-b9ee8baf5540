#!/usr/bin/env node

/**
 * Configuration Validation Script
 * 
 * This script validates that the ConfigManager can properly load all settings
 * from environment variables and provides a comprehensive report.
 */

const path = require('path');
const fs = require('fs');

// Load environment variables from .env.example for testing
require('dotenv').config({ path: path.join(__dirname, '../.env.example') });

// Import ConfigManager
const config = require('../lib/config');

console.log('🔍 CONFIGURATION VALIDATION REPORT');
console.log('=====================================\n');

// Test categories and their expected paths
const testCategories = {
  'Application Core': [
    'app.name',
    'app.port',
    'app.logLevel',
    'app.environment',
    'app.serviceName'
  ],
  'Telegram Bot': [
    'telegram.token',
    'telegram.botName',
    'telegram.botUsername',
    'telegram.botDescription'
  ],
  'Database - MongoDB': [
    'mongodb.uri',
    'mongodb.options.useNewUrlParser',
    'mongodb.options.useUnifiedTopology',
    'mongodb.options.maxPoolSize'
  ],
  'Database - Redis': [
    'redis.host',
    'redis.port',
    'redis.db',
    'redis.keyPrefix'
  ],
  'OpenAI': [
    'openai.apiKey',
    'openai.model',
    'openai.maxTokens',
    'openai.temperature'
  ],
  'Machine Learning - LSTM': [
    'models.lstm.enabled',
    'models.lstm.sequenceLength',
    'models.lstm.epochs',
    'models.lstm.batchSize'
  ],
  'Machine Learning - Statistical': [
    'models.statistical.enabled',
    'models.statistical.lookbackPeriod',
    'models.statistical.minFrequency'
  ],
  'Machine Learning - Ensemble': [
    'models.ensemble.enabled',
    'models.ensemble.models',
    'models.ensemble.weights.lstm'
  ],
  'ML Training': [
    'models.training.autoRetrain',
    'models.training.retrainThreshold',
    'models.training.minDataPoints'
  ],
  'ML Prediction': [
    'models.prediction.maxPredictions',
    'models.prediction.minConfidence',
    'models.prediction.cacheResults'
  ],
  'ML Analysis': [
    'models.analysis.hotColdNumbers.count',
    'models.analysis.patterns.enabled',
    'models.analysis.correlations.enabled'
  ],
  'ML Performance': [
    'models.performance.accuracyTracking',
    'models.performance.performanceMetrics',
    'models.performance.benchmarkInterval'
  ],
  'Bot Features': [
    'features.predictions.enabled',
    'features.trends.enabled',
    'features.history.enabled',
    'features.llmReports.enabled',
    'features.scheduling.enabled'
  ],
  'Rate Limiting': [
    'limits.userRequestsPerMinute',
    'limits.groupRequestsPerMinute',
    'limits.maxGroupsPerUser'
  ],
  'Bot Messages': [
    'messages.welcome',
    'messages.error',
    'messages.unauthorized'
  ],
  'Scheduler': [
    'scheduler.enabled',
    'scheduler.timezone',
    'scheduler.dailyPredictionTime'
  ],
  'External APIs': [
    'external.lotteryDataSource',
    'external.apiTimeout',
    'external.retryAttempts'
  ],
  'Email': [
    'email.service',
    'email.user',
    'email.alertRecipients'
  ],
  'Proxy Servers': [
    'proxy.pushNotify',
    'proxy.socketRealtime',
    'proxy.serverCrawl'
  ]
};

let totalTests = 0;
let passedTests = 0;
let failedTests = 0;
const failures = [];

// Test each category
Object.entries(testCategories).forEach(([category, paths]) => {
  console.log(`📋 ${category}`);
  console.log('-'.repeat(category.length + 3));
  
  paths.forEach(path => {
    totalTests++;
    try {
      const value = config.get(path);
      if (value !== undefined && value !== null) {
        console.log(`  ✅ ${path}: ${typeof value === 'object' ? JSON.stringify(value).substring(0, 50) + '...' : value}`);
        passedTests++;
      } else {
        console.log(`  ❌ ${path}: undefined/null`);
        failedTests++;
        failures.push(`${category}: ${path}`);
      }
    } catch (error) {
      console.log(`  ❌ ${path}: ERROR - ${error.message}`);
      failedTests++;
      failures.push(`${category}: ${path} - ${error.message}`);
    }
  });
  
  console.log('');
});

// Test environment detection
console.log('🌍 Environment Detection');
console.log('------------------------');
console.log(`  Environment: ${config.isDevelopment() ? 'Development' : config.isProduction() ? 'Production' : 'Unknown'}`);
console.log(`  isDevelopment(): ${config.isDevelopment()}`);
console.log(`  isProduction(): ${config.isProduction()}`);
console.log('');

// Test feature flags
console.log('🚀 Feature Flags');
console.log('----------------');
const features = ['predictions', 'trends', 'history', 'llmReports', 'scheduling'];
features.forEach(feature => {
  const enabled = config.isFeatureEnabled(feature);
  console.log(`  ${feature}: ${enabled ? '✅ Enabled' : '❌ Disabled'}`);
});
console.log('');

// Summary
console.log('📊 VALIDATION SUMMARY');
console.log('====================');
console.log(`Total Tests: ${totalTests}`);
console.log(`Passed: ${passedTests} (${Math.round(passedTests/totalTests*100)}%)`);
console.log(`Failed: ${failedTests} (${Math.round(failedTests/totalTests*100)}%)`);

if (failures.length > 0) {
  console.log('\n❌ FAILURES:');
  failures.forEach(failure => console.log(`  - ${failure}`));
}

// Test complete configuration object
console.log('\n🔧 Configuration Object Structure');
console.log('=================================');
const fullConfig = config.getAll();
const configKeys = Object.keys(fullConfig);
console.log(`Root level keys: ${configKeys.join(', ')}`);

// Check for required configurations
console.log('\n🔒 Required Configuration Check');
console.log('==============================');
const requiredConfigs = [
  { path: 'telegram.token', name: 'Telegram Bot Token' },
  { path: 'mongodb.uri', name: 'MongoDB URI' },
  { path: 'openai.apiKey', name: 'OpenAI API Key' }
];

requiredConfigs.forEach(({ path, name }) => {
  const value = config.get(path);
  const isSet = value && !value.includes('your_') && !value.includes('test_');
  console.log(`  ${name}: ${isSet ? '✅ Configured' : '⚠️  Using placeholder/default'}`);
});

console.log('\n✅ Configuration validation completed!');

// Exit with appropriate code
process.exit(failedTests > 0 ? 1 : 0);
