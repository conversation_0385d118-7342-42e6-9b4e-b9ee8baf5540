#!/usr/bin/env node

/**
 * Configuration Migration Script
 * 
 * This script helps migrate from JSON configuration files to environment variables
 * by reading existing config files and generating a .env file with current values.
 */

const fs = require('fs');
const path = require('path');

console.log('🔄 Configuration Migration Tool');
console.log('===============================\n');

// Configuration file mappings
const configMappings = {
  'config/default.json': {
    'port': 'PORT',
    'logLevel': 'LOG_LEVEL',
    'dirLog': 'LOG_DIR',
    'environment': 'ENVIRONMENT',
    'serviceName': 'SERVICE_NAME',
    'secretKey': 'SECRET_KEY',
    'mongodb.development.uri': 'MONGODB_URI',
    'mongodb.development.options.maxPoolSize': 'MONGODB_MAX_POOL_SIZE',
    'mongodb.production.uri': 'MONGODB_PRODUCTION_URI',
    'redis.connections.master.host': 'REDIS_LEGACY_HOST',
    'redis.connections.master.port': 'REDIS_LEGACY_PORT',
    'redis.connections.master.password': 'REDIS_LEGACY_PASSWORD',
    'redis.connections.master.database': 'REDIS_LEGACY_DATABASE',
    'models.lstm.enabled': 'ML_LSTM_ENABLED',
    'models.lstm.sequenceLength': 'ML_LSTM_SEQUENCE_LENGTH',
    'models.lstm.epochs': 'ML_LSTM_EPOCHS',
    'models.lstm.batchSize': 'ML_LSTM_BATCH_SIZE',
    'models.statistical.enabled': 'ML_STATISTICAL_ENABLED',
    'models.statistical.lookbackPeriod': 'ML_STATISTICAL_LOOKBACK_PERIOD',
    'models.ensemble.enabled': 'ML_ENSEMBLE_ENABLED',
    'listEmailAlert': 'EMAIL_ALERT_RECIPIENTS'
  },
  'config/bot.json': {
    'bot.name': 'TELEGRAM_BOT_NAME',
    'bot.username': 'TELEGRAM_BOT_USERNAME',
    'bot.description': 'TELEGRAM_BOT_DESCRIPTION',
    'features.predictions.enabled': 'FEATURE_PREDICTIONS_ENABLED',
    'features.predictions.maxPredictions': 'FEATURE_PREDICTIONS_MAX',
    'features.trends.enabled': 'FEATURE_TRENDS_ENABLED',
    'features.history.enabled': 'FEATURE_HISTORY_ENABLED',
    'limits.userRequestsPerMinute': 'LIMIT_USER_REQUESTS_PER_MINUTE',
    'limits.groupRequestsPerMinute': 'LIMIT_GROUP_REQUESTS_PER_MINUTE',
    'messages.welcome': 'BOT_MESSAGE_WELCOME',
    'messages.error': 'BOT_MESSAGE_ERROR'
  },
  'config/ml.json': {
    'training.autoRetrain': 'ML_TRAINING_AUTO_RETRAIN',
    'training.retrainThreshold': 'ML_TRAINING_RETRAIN_THRESHOLD',
    'prediction.maxPredictions': 'ML_PREDICTION_MAX_PREDICTIONS',
    'prediction.cacheResults': 'ML_PREDICTION_CACHE_RESULTS',
    'analysis.hotColdNumbers.count': 'ML_ANALYSIS_HOT_COLD_COUNT',
    'performance.accuracyTracking': 'ML_PERFORMANCE_ACCURACY_TRACKING'
  },
  'config/database.json': {
    'mongodb.development.uri': 'MONGODB_URI',
    'mongodb.production.uri': 'MONGODB_PRODUCTION_URI',
    'redis.development.host': 'REDIS_HOST',
    'redis.development.port': 'REDIS_PORT'
  }
};

/**
 * Get nested property value from object
 */
function getNestedValue(obj, path) {
  return path.split('.').reduce((current, key) => {
    return current && current[key] !== undefined ? current[key] : undefined;
  }, obj);
}

/**
 * Format value for .env file
 */
function formatEnvValue(value) {
  if (typeof value === 'string') {
    // Escape quotes and newlines
    if (value.includes('\n') || value.includes('"')) {
      return `"${value.replace(/"/g, '\\"').replace(/\n/g, '\\n')}"`;
    }
    return value;
  }
  if (Array.isArray(value)) {
    return JSON.stringify(value);
  }
  return String(value);
}

/**
 * Load and parse JSON config file
 */
function loadConfigFile(filePath) {
  try {
    if (fs.existsSync(filePath)) {
      const content = fs.readFileSync(filePath, 'utf8');
      return JSON.parse(content);
    }
  } catch (error) {
    console.log(`⚠️  Warning: Could not load ${filePath}: ${error.message}`);
  }
  return null;
}

// Generate .env content
let envContent = [];
envContent.push('# =============================================================================');
envContent.push('# MIGRATED CONFIGURATION FROM JSON FILES');
envContent.push('# =============================================================================');
envContent.push(`# Generated on: ${new Date().toISOString()}`);
envContent.push('# =============================================================================\n');

let migratedCount = 0;
let totalCount = 0;

// Process each config file
Object.entries(configMappings).forEach(([configFile, mappings]) => {
  const config = loadConfigFile(configFile);
  
  if (!config) {
    console.log(`❌ Skipping ${configFile} - file not found or invalid`);
    return;
  }
  
  console.log(`📄 Processing ${configFile}...`);
  envContent.push(`# From ${configFile}`);
  
  Object.entries(mappings).forEach(([jsonPath, envVar]) => {
    totalCount++;
    const value = getNestedValue(config, jsonPath);
    
    if (value !== undefined && value !== null) {
      envContent.push(`${envVar}=${formatEnvValue(value)}`);
      console.log(`  ✅ ${jsonPath} → ${envVar}`);
      migratedCount++;
    } else {
      envContent.push(`# ${envVar}=`);
      console.log(`  ⚠️  ${jsonPath} → ${envVar} (not found)`);
    }
  });
  
  envContent.push('');
});

// Add required but not found variables
const requiredVars = [
  'TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here',
  'OPENAI_API_KEY=your_openai_api_key_here'
];

envContent.push('# Required variables (please update these)');
requiredVars.forEach(varDef => {
  envContent.push(varDef);
});

// Write .env file
const envFilePath = '.env.migrated';
fs.writeFileSync(envFilePath, envContent.join('\n'));

console.log('\n📊 Migration Summary');
console.log('===================');
console.log(`Total mappings: ${totalCount}`);
console.log(`Successfully migrated: ${migratedCount}`);
console.log(`Missing values: ${totalCount - migratedCount}`);
console.log(`\n✅ Migration completed!`);
console.log(`📁 Generated file: ${envFilePath}`);
console.log('\nNext steps:');
console.log('1. Review the generated .env.migrated file');
console.log('2. Update placeholder values (bot token, API keys, etc.)');
console.log('3. Rename to .env when ready: mv .env.migrated .env');
console.log('4. Run validation: node scripts/validate-config.js');

// Check if .env already exists
if (fs.existsSync('.env')) {
  console.log('\n⚠️  Warning: .env file already exists');
  console.log('   The migrated config was saved as .env.migrated');
  console.log('   Please merge manually if needed');
}
