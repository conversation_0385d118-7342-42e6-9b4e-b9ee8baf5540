# =============================================================================
# LOTTERY PREDICTION BOT - ENVIRONMENT CONFIGURATION
# =============================================================================
# Generated from config files: default.json, bot.json, ml.json, database.json
# Last updated: 2024-01-15
# =============================================================================

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
APP_NAME=Lottery Prediction Bot
NODE_ENV=development
PORT=6996
LOG_LEVEL=info
LOG_DIR=logs
ENVIRONMENT=dev
SERVICE_NAME=lottery-forecast
SECRET_KEY=e9dc8985a328b0d11207642489dc7a32227bd941131a2990784b2a57aa485875

# =============================================================================
# TELEGRAM BOT CONFIGURATION
# =============================================================================
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
TELEGRAM_BOT_NAME=Lottery Prediction Bot
TELEGRAM_BOT_USERNAME=lottery_prediction_bot
TELEGRAM_BOT_DESCRIPTION=Bot dự đoán xổ số miền Bắc sử dụng AI và phân tích thống kê
TELEGRAM_WEBHOOK_URL=https://your-domain.com/webhook
TELEGRAM_POLLING=true
TELEGRAM_MAX_CONNECTIONS=40
TELEGRAM_ALLOWED_UPDATES=message,callback_query

# =============================================================================
# DATABASE CONFIGURATION - MONGODB
# =============================================================================
# Development
MONGODB_URI=mongodb://localhost:27017/lottery_bot_dev
MONGODB_DB_NAME=lottery_bot_dev
MONGODB_USE_NEW_URL_PARSER=true
MONGODB_USE_UNIFIED_TOPOLOGY=true
MONGODB_MAX_POOL_SIZE=10
MONGODB_SERVER_SELECTION_TIMEOUT=5000
MONGODB_SOCKET_TIMEOUT=45000
MONGODB_BUFFER_MAX_ENTRIES=0
MONGODB_BUFFER_COMMANDS=false

# Production (override for production)
MONGODB_PRODUCTION_URI=${MONGODB_URI}
MONGODB_PRODUCTION_MAX_POOL_SIZE=20
MONGODB_PRODUCTION_RETRY_WRITES=true
MONGODB_PRODUCTION_WRITE_CONCERN=majority

# Legacy Mongo (from default.json)
MONGO_LEGACY_HOST=localhost
MONGO_LEGACY_PORT=27017
MONGO_LEGACY_DATABASE=lottery-bot
MONGO_LEGACY_USER=
MONGO_LEGACY_PASS=

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================
# Development
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_KEY_PREFIX=lottery_dev:
REDIS_RETRY_DELAY=100
REDIS_MAX_RETRIES=3

# Production (override for production)
REDIS_URL=redis://localhost:6379
REDIS_PRODUCTION_PASSWORD=
REDIS_PRODUCTION_DB=0
REDIS_PRODUCTION_KEY_PREFIX=lottery:
REDIS_PRODUCTION_CONNECT_TIMEOUT=10000
REDIS_PRODUCTION_COMMAND_TIMEOUT=5000

# Legacy Redis (from default.json)
REDIS_LEGACY_HOST=localhost
REDIS_LEGACY_PORT=6379
REDIS_LEGACY_DATABASE=15
REDIS_LEGACY_PASSWORD=mmjjnnhh

# =============================================================================
# OPENAI CONFIGURATION
# =============================================================================
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-3.5-turbo
OPENAI_MAX_TOKENS=1000
OPENAI_TEMPERATURE=0.7
OPENAI_DAILY_BUDGET=50
OPENAI_URL=https://api.openai.com/v1
OPENAI_TIMEOUT=30000

# =============================================================================
# MACHINE LEARNING MODELS CONFIGURATION
# =============================================================================
# LSTM Model (from default.json and ml.json)
ML_LSTM_ENABLED=true
ML_LSTM_SEQUENCE_LENGTH=30
ML_LSTM_HIDDEN_UNITS=50
ML_LSTM_EPOCHS=100
ML_LSTM_BATCH_SIZE=32
ML_LSTM_LEARNING_RATE=0.001
ML_LSTM_DROPOUT=0.2
ML_LSTM_VALIDATION_SPLIT=0.2
ML_LSTM_PATIENCE=10
ML_LSTM_MIN_DELTA=0.001

# Statistical Model (from default.json and ml.json)
ML_STATISTICAL_ENABLED=true
ML_STATISTICAL_LOOKBACK_PERIOD=100
ML_STATISTICAL_MIN_FREQUENCY=3
ML_STATISTICAL_HOT_COLD_THRESHOLD=0.7
ML_STATISTICAL_TREND_PERIODS=[7,14,30,60,90]

# Ensemble Model (from default.json and ml.json)
ML_ENSEMBLE_ENABLED=true
ML_ENSEMBLE_MODELS=["lstm","statistical"]
ML_ENSEMBLE_LSTM_WEIGHT=0.6
ML_ENSEMBLE_STATISTICAL_WEIGHT=0.4
ML_ENSEMBLE_DYNAMIC_WEIGHTS=true
ML_ENSEMBLE_ACCURACY_WINDOW=50

# =============================================================================
# ML TRAINING CONFIGURATION (from ml.json)
# =============================================================================
ML_TRAINING_AUTO_RETRAIN=true
ML_TRAINING_RETRAIN_THRESHOLD=0.15
ML_TRAINING_MIN_DATA_POINTS=1000
ML_TRAINING_RETRAIN_INTERVAL=weekly
ML_TRAINING_BACKUP_MODELS=true
ML_TRAINING_MAX_MODEL_VERSIONS=5

# =============================================================================
# ML PREDICTION CONFIGURATION (from ml.json)
# =============================================================================
ML_PREDICTION_MAX_PREDICTIONS=10
ML_PREDICTION_MIN_CONFIDENCE=10
ML_PREDICTION_CACHE_RESULTS=true
ML_PREDICTION_CACHE_TTL=86400
ML_PREDICTION_INCLUDE_REASONING=true

# =============================================================================
# ML ANALYSIS CONFIGURATION (from ml.json)
# =============================================================================
# Hot/Cold Numbers Analysis
ML_ANALYSIS_HOT_COLD_COUNT=5
ML_ANALYSIS_HOT_COLD_PERIOD=30
ML_ANALYSIS_HOT_COLD_THRESHOLD=0.8

# Pattern Analysis
ML_ANALYSIS_PATTERNS_ENABLED=true
ML_ANALYSIS_PATTERNS_SEQUENCE_LENGTH=5
ML_ANALYSIS_PATTERNS_MIN_OCCURRENCES=3

# Correlation Analysis
ML_ANALYSIS_CORRELATIONS_ENABLED=true
ML_ANALYSIS_CORRELATIONS_MIN_CORRELATION=0.3
ML_ANALYSIS_CORRELATIONS_MAX_PAIRS=20

# =============================================================================
# ML PERFORMANCE CONFIGURATION (from ml.json)
# =============================================================================
ML_PERFORMANCE_ACCURACY_TRACKING=true
ML_PERFORMANCE_METRICS=["precision","recall","f1Score","accuracy"]
ML_PERFORMANCE_BENCHMARK_INTERVAL=daily
ML_PERFORMANCE_ALERT_THRESHOLD=0.1

# =============================================================================
# BOT FEATURES CONFIGURATION (from bot.json)
# =============================================================================
# Predictions Feature
FEATURE_PREDICTIONS_ENABLED=true
FEATURE_PREDICTIONS_MAX=10
FEATURE_PREDICTIONS_CONFIDENCE_THRESHOLD=10

# Trends Feature
FEATURE_TRENDS_ENABLED=true
FEATURE_TRENDS_HOT_COLD_COUNT=5
FEATURE_TRENDS_ANALYSIS_PERIOD=30

# History Feature
FEATURE_HISTORY_ENABLED=true
FEATURE_HISTORY_MAX_RESULTS=10

# LLM Reports Feature
FEATURE_LLM_REPORTS_ENABLED=true
FEATURE_LLM_FALLBACK_ENABLED=true

# Scheduling Feature
FEATURE_SCHEDULING_ENABLED=true
FEATURE_DAILY_PREDICTIONS_ENABLED=true
FEATURE_WEEKLY_REPORTS_ENABLED=true

# =============================================================================
# RATE LIMITING CONFIGURATION (from bot.json)
# =============================================================================
LIMIT_USER_REQUESTS_PER_MINUTE=10
LIMIT_GROUP_REQUESTS_PER_MINUTE=20
LIMIT_MAX_GROUPS_PER_USER=5
LIMIT_MAX_FAVORITE_NUMBERS=10

# =============================================================================
# BOT MESSAGES CONFIGURATION (from bot.json)
# =============================================================================
BOT_MESSAGE_WELCOME=🎯 Chào mừng bạn đến với Bot Dự Đoán Xổ Số!\n\nSử dụng /help để xem các lệnh có sẵn.
BOT_MESSAGE_ERROR=❌ Đã xảy ra lỗi. Vui lòng thử lại sau.
BOT_MESSAGE_UNAUTHORIZED=🚫 Bạn không có quyền sử dụng lệnh này.
BOT_MESSAGE_RATE_LIMIT=⏰ Bạn đang gửi yêu cầu quá nhanh. Vui lòng chờ một chút.
BOT_MESSAGE_MAINTENANCE=🔧 Bot đang bảo trì. Vui lòng thử lại sau.

# =============================================================================
# SCHEDULER CONFIGURATION
# =============================================================================
SCHEDULER_ENABLED=true
SCHEDULER_TIMEZONE=Asia/Ho_Chi_Minh
SCHEDULER_DAILY_PREDICTION_TIME=08:00
SCHEDULER_WEEKLY_REPORT_DAY=1
SCHEDULER_WEEKLY_REPORT_TIME=09:00
SCHEDULER_DATA_COLLECTION_TIME=19:00
SCHEDULER_MODEL_RETRAINING_TIME=02:00

# =============================================================================
# EXTERNAL APIS CONFIGURATION
# =============================================================================
EXTERNAL_LOTTERY_DATA_SOURCE=xoso.com.vn
EXTERNAL_API_TIMEOUT=30000
EXTERNAL_API_RETRY_ATTEMPTS=3
EXTERNAL_API_RETRY_DELAY=2000

# =============================================================================
# EMAIL CONFIGURATION (from default.json)
# =============================================================================
EMAIL_SERVICE=gmail
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your_email_password
EMAIL_FROM=<EMAIL>
EMAIL_ALERT_RECIPIENTS=["<EMAIL>"]

# Email Accounts (Multiple accounts for load balancing from default.json)
EMAIL_ACCOUNT_1_USER=<EMAIL>
EMAIL_ACCOUNT_1_PASS=pnoimjonjkwtvbxp
EMAIL_ACCOUNT_2_USER=<EMAIL>
EMAIL_ACCOUNT_2_PASS=uoxozzqkqbzehbvf
EMAIL_ACCOUNT_3_USER=<EMAIL>
EMAIL_ACCOUNT_3_PASS=qngntsssrviwwwtz
EMAIL_ACCOUNT_4_USER=<EMAIL>
EMAIL_ACCOUNT_4_PASS=ygoljpqkjlbpxexw
EMAIL_ACCOUNT_5_USER=<EMAIL>
EMAIL_ACCOUNT_5_PASS=jrusfbpoocqvzdft

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
JWT_SECRET=your_jwt_secret_here
ENCRYPTION_KEY=your_32_character_encryption_key
SESSION_TIMEOUT=86400
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION=900

# =============================================================================
# PERFORMANCE CONFIGURATION
# =============================================================================
CACHE_ENABLED=true
CACHE_TTL=3600
MAX_CACHE_SIZE=1000
COMPRESSION_ENABLED=true
REQUEST_TIMEOUT=30000

# =============================================================================
# DATABASE COLLECTIONS INDEXES (from database.json)
# =============================================================================
# These are used by the application to create proper indexes
DB_COLLECTIONS_LOTTERY_RESULTS_INDEXES=true
DB_COLLECTIONS_PREDICTIONS_INDEXES=true
DB_COLLECTIONS_USERS_INDEXES=true
DB_COLLECTIONS_GROUPS_INDEXES=true
DB_COLLECTIONS_ANALYTICS_INDEXES=true

# =============================================================================
# TEST CONFIGURATION (from test.json)
# =============================================================================
TEST_DATABASE_HOST=localhost
TEST_DATABASE_PORT=27017
TEST_DATABASE_NAME=lottery-test
TEST_REDIS_HOST=localhost
TEST_REDIS_PORT=6379
TEST_BOT_TOKEN=test_token
TEST_OPENAI_API_KEY=test_key
