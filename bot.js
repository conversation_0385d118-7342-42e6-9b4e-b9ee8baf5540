// Main entry point for Lottery Prediction Bot
require('dotenv').config();

const LotteryPredictionApp = require('./lib/app');
const config = require('./lib/config');

// Application configuration from environment variables
const appConfig = config.getAll();

// Create and initialize application
const app = new LotteryPredictionApp(appConfig);

// Graceful shutdown handling
process.on('SIGINT', async () => {
  console.log('\n🛑 Received SIGINT, shutting down gracefully...');
  try {
    await app.stop();
    process.exit(0);
  } catch (error) {
    console.error('Error during shutdown:', error.message);
    process.exit(1);
  }
});

process.on('SIGTERM', async () => {
  console.log('\n🛑 Received SIGTERM, shutting down gracefully...');
  try {
    await app.stop();
    process.exit(0);
  } catch (error) {
    console.error('Error during shutdown:', error.message);
    process.exit(1);
  }
});

// Unhandled error handling
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  process.exit(1);
});

// Start the application
async function startApp() {
  try {
    console.log('🎯 Starting Lottery Prediction Bot...');
    console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);

    // Configuration validation is handled by ConfigManager
    console.log(`🔧 Configuration loaded for ${config.getEnvironment()} environment`);

    await app.initialize();
    await app.start();

    // Log application status
    const status = app.getStatus();
    console.log('📈 Application Status:', JSON.stringify(status, null, 2));

  } catch (error) {
    console.error('❌ Failed to start application:', error.message);
    process.exit(1);
  }
}

// Start the application
startApp();