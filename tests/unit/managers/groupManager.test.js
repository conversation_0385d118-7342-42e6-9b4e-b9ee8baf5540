// Unit tests for GroupManager
// Tests group registration, settings management, admin controls, and analytics

const GroupManager = require('../../../lib/managers/groupManager');
const GroupRepository = require('../../../lib/data/repositories/groupRepo');

// Mock dependencies
jest.mock('../../../lib/connections/mongo');
jest.mock('../../../lib/connections/redis');
jest.mock('../../../lib/data/repositories/groupRepo');
jest.mock('../../../lib/logger');

describe('GroupManager', () => {
  let groupManager;
  let mockGroupRepo;
  let mockCache;
  let mockMongoConnection;
  let mockRedisConnection;

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();

    // Mock GroupRepository
    mockGroupRepo = {
      createIndexes: jest.fn().mockResolvedValue(),
      findByTelegramId: jest.fn(),
      createGroup: jest.fn(),
      updateSettings: jest.fn(),
      getSettings: jest.fn(),
      updateStats: jest.fn(),
      addAdmin: jest.fn(),
      removeAdmin: jest.fn(),
      isAdmin: jest.fn(),
      getGroupsWithDailyPredictions: jest.fn(),
      getGroupsForWeeklyReports: jest.fn(),
      getGroupAnalytics: jest.fn(),
      getCommandUsageStats: jest.fn(),
      deactivateGroup: jest.fn(),
      reactivateGroup: jest.fn()
    };
    GroupRepository.mockImplementation(() => mockGroupRepo);

    // Mock Redis cache
    mockCache = {
      connect: jest.fn().mockResolvedValue(),
      get: jest.fn(),
      setEx: jest.fn(),
      del: jest.fn(),
      quit: jest.fn(),
      isOpen: true
    };

    // Mock MongoDB connection
    mockMongoConnection = {
      db: { collection: jest.fn() }
    };

    // Mock connections
    require('../../../lib/connections/mongo').mockReturnValue(mockMongoConnection);
    require('../../../lib/connections/redis').mockReturnValue({
      getConnection: () => mockCache
    });

    groupManager = new GroupManager({
      cacheEnabled: true,
      cacheTTL: 21600,
      maxGroupsPerUser: 50
    });
  });

  afterEach(async () => {
    if (groupManager.isInitialized) {
      await groupManager.cleanup();
    }
  });

  describe('initialization', () => {
    test('should initialize successfully with valid connections', async () => {
      await groupManager.initialize();

      expect(groupManager.isInitialized).toBe(true);
      expect(mockGroupRepo.createIndexes).toHaveBeenCalled();
      expect(mockCache.connect).toHaveBeenCalled();
    });

    test('should handle MongoDB connection failure', async () => {
      require('../../../lib/connections/mongo').mockReturnValue(null);

      await expect(groupManager.initialize()).rejects.toThrow('MongoDB connection not available');
      expect(groupManager.isInitialized).toBe(false);
    });

    test('should work without Redis cache', async () => {
      require('../../../lib/connections/redis').mockReturnValue(null);

      await groupManager.initialize();

      expect(groupManager.isInitialized).toBe(true);
      expect(groupManager.options.cacheEnabled).toBe(false);
    });
  });

  describe('group registration', () => {
    beforeEach(async () => {
      await groupManager.initialize();
    });

    test('should register new group successfully', async () => {
      const telegramGroupData = {
        id: -123456789,
        title: 'Test Group',
        type: 'supergroup',
        username: 'testgroup',
        description: 'A test group',
        member_count: 50
      };

      const expectedGroup = {
        telegramId: -123456789,
        title: 'Test Group',
        type: 'supergroup',
        username: 'testgroup'
      };

      mockGroupRepo.findByTelegramId.mockResolvedValue(null);
      mockGroupRepo.createGroup.mockResolvedValue(expectedGroup);
      mockCache.setEx.mockResolvedValue();

      const result = await groupManager.registerGroup(telegramGroupData);

      expect(mockGroupRepo.findByTelegramId).toHaveBeenCalledWith(-123456789);
      expect(mockGroupRepo.createGroup).toHaveBeenCalledWith(
        expect.objectContaining({
          telegramId: -123456789,
          title: 'Test Group',
          type: 'supergroup',
          username: 'testgroup',
          description: 'A test group'
        })
      );
      expect(result).toEqual(expectedGroup);
    });

    test('should reactivate existing deactivated group', async () => {
      const telegramGroupData = { id: -123456789, title: 'Test', type: 'group' };
      const existingGroup = { telegramId: -123456789, isActive: false };

      mockGroupRepo.findByTelegramId.mockResolvedValue(existingGroup);
      mockGroupRepo.reactivateGroup.mockResolvedValue();

      const result = await groupManager.registerGroup(telegramGroupData);

      expect(mockGroupRepo.reactivateGroup).toHaveBeenCalledWith(-123456789);
      expect(result).toEqual(existingGroup);
    });

    test('should validate group data', async () => {
      const invalidGroupData = {
        id: 'invalid', // should be number
        title: null, // should be string
        type: 'invalid' // should be valid type
      };

      await expect(groupManager.registerGroup(invalidGroupData))
        .rejects.toThrow();
    });
  });

  describe('group settings', () => {
    beforeEach(async () => {
      await groupManager.initialize();
    });

    test('should get group settings from cache if available', async () => {
      const cachedSettings = { dailyPredictions: true, language: 'vi' };
      mockCache.get.mockResolvedValue(JSON.stringify(cachedSettings));

      const result = await groupManager.getGroupSettings(-123456789);

      expect(mockCache.get).toHaveBeenCalledWith('group:settings:-123456789');
      expect(mockGroupRepo.getSettings).not.toHaveBeenCalled();
      expect(result).toEqual(cachedSettings);
    });

    test('should get group settings from database if not cached', async () => {
      const dbSettings = { dailyPredictions: false, language: 'en' };
      mockCache.get.mockResolvedValue(null);
      mockGroupRepo.getSettings.mockResolvedValue(dbSettings);
      mockCache.setEx.mockResolvedValue();

      const result = await groupManager.getGroupSettings(-123456789);

      expect(mockGroupRepo.getSettings).toHaveBeenCalledWith(-123456789);
      expect(mockCache.setEx).toHaveBeenCalledWith(
        'group:settings:-123456789',
        21600,
        JSON.stringify(dbSettings)
      );
      expect(result).toEqual(dbSettings);
    });

    test('should return default settings if group not found', async () => {
      mockCache.get.mockResolvedValue(null);
      mockGroupRepo.getSettings.mockResolvedValue(null);

      const result = await groupManager.getGroupSettings(-123456789);

      expect(result).toEqual(groupManager.options.defaultSettings);
    });

    test('should update group settings successfully', async () => {
      const settings = {
        dailyPredictions: true,
        predictionTime: '09:00',
        weeklyReports: true,
        language: 'en'
      };

      mockGroupRepo.updateSettings.mockResolvedValue();
      mockCache.del.mockResolvedValue();

      const result = await groupManager.updateGroupSettings(-123456789, settings);

      expect(mockGroupRepo.updateSettings).toHaveBeenCalledWith(-123456789, settings);
      expect(mockCache.del).toHaveBeenCalledWith('group:settings:-123456789');
      expect(result).toBe(true);
    });

    test('should validate admin permissions before updating settings', async () => {
      const settings = { dailyPredictions: true };
      mockGroupRepo.isAdmin.mockResolvedValue(false);

      await expect(groupManager.updateGroupSettings(-123456789, settings, 123456))
        .rejects.toThrow('User is not authorized to change group settings');

      expect(mockGroupRepo.updateSettings).not.toHaveBeenCalled();
    });

    test('should validate settings before updating', async () => {
      const invalidSettings = {
        dailyPredictions: 'invalid', // should be boolean
        predictionTime: '25:00', // invalid time format
        weeklyReportDay: 8, // invalid day (0-6)
        maxPredictionsPerMessage: 25 // too high (max 20)
      };

      mockGroupRepo.updateSettings.mockResolvedValue();

      await groupManager.updateGroupSettings(-123456789, invalidSettings);

      expect(mockGroupRepo.updateSettings).toHaveBeenCalledWith(-123456789, {
        dailyPredictions: false // converted to boolean
        // other invalid fields should be omitted
      });
    });
  });

  describe('admin management', () => {
    beforeEach(async () => {
      await groupManager.initialize();
    });

    test('should add group admin successfully', async () => {
      const adminData = {
        telegramId: 123456,
        username: 'admin',
        firstName: 'Admin'
      };

      mockGroupRepo.isAdmin.mockResolvedValue(true); // requester is admin
      mockGroupRepo.addAdmin.mockResolvedValue();
      mockCache.del.mockResolvedValue();

      const result = await groupManager.addGroupAdmin(-123456789, adminData, 987654);

      expect(mockGroupRepo.isAdmin).toHaveBeenCalledWith(-123456789, 987654);
      expect(mockGroupRepo.addAdmin).toHaveBeenCalledWith(-123456789, adminData);
      expect(result).toBe(true);
    });

    test('should reject adding admin if requester is not admin', async () => {
      const adminData = { telegramId: 123456 };
      mockGroupRepo.isAdmin.mockResolvedValue(false);

      await expect(groupManager.addGroupAdmin(-123456789, adminData, 987654))
        .rejects.toThrow('User is not authorized to add admins');

      expect(mockGroupRepo.addAdmin).not.toHaveBeenCalled();
    });

    test('should remove group admin successfully', async () => {
      mockGroupRepo.isAdmin.mockResolvedValue(true);
      mockGroupRepo.removeAdmin.mockResolvedValue();
      mockCache.del.mockResolvedValue();

      const result = await groupManager.removeGroupAdmin(-123456789, 123456, 987654);

      expect(mockGroupRepo.removeAdmin).toHaveBeenCalledWith(-123456789, 123456);
      expect(result).toBe(true);
    });

    test('should allow admin to remove themselves', async () => {
      mockGroupRepo.removeAdmin.mockResolvedValue();

      const result = await groupManager.removeGroupAdmin(-123456789, 123456, 123456);

      expect(mockGroupRepo.isAdmin).not.toHaveBeenCalled(); // Skip permission check
      expect(mockGroupRepo.removeAdmin).toHaveBeenCalledWith(-123456789, 123456);
      expect(result).toBe(true);
    });

    test('should check admin status', async () => {
      mockGroupRepo.isAdmin.mockResolvedValue(true);

      const result = await groupManager.isGroupAdmin(-123456789, 123456);

      expect(mockGroupRepo.isAdmin).toHaveBeenCalledWith(-123456789, 123456);
      expect(result).toBe(true);
    });
  });

  describe('interaction logging', () => {
    beforeEach(async () => {
      await groupManager.initialize();
    });

    test('should log group interaction successfully', async () => {
      mockGroupRepo.updateStats.mockResolvedValue();
      mockCache.del.mockResolvedValue();

      await groupManager.logGroupInteraction(-123456789, 'dukienlo', 50);

      expect(mockGroupRepo.updateStats).toHaveBeenCalledWith(-123456789, {
        command: 'dukienlo',
        memberCount: 50
      });
      expect(mockCache.del).toHaveBeenCalledWith('group:settings:-123456789');
    });

    test('should not throw error if logging fails', async () => {
      mockGroupRepo.updateStats.mockRejectedValue(new Error('Database error'));

      // Should not throw
      await expect(groupManager.logGroupInteraction(-123456789, 'dukienlo'))
        .resolves.toBeUndefined();
    });
  });

  describe('scheduled operations', () => {
    beforeEach(async () => {
      await groupManager.initialize();
    });

    test('should get groups for daily predictions', async () => {
      const groups = [
        { telegramId: -111, settings: { dailyPredictions: true } },
        { telegramId: -222, settings: { dailyPredictions: true } }
      ];
      mockGroupRepo.getGroupsWithDailyPredictions.mockResolvedValue(groups);

      const result = await groupManager.getGroupsForDailyPredictions();

      expect(mockGroupRepo.getGroupsWithDailyPredictions).toHaveBeenCalled();
      expect(result).toEqual(groups);
    });

    test('should get groups for weekly reports', async () => {
      const groups = [
        { telegramId: -111, settings: { weeklyReports: true, weeklyReportDay: 1 } }
      ];
      mockGroupRepo.getGroupsForWeeklyReports.mockResolvedValue(groups);

      const result = await groupManager.getGroupsForWeeklyReports(1);

      expect(mockGroupRepo.getGroupsForWeeklyReports).toHaveBeenCalledWith(1);
      expect(result).toEqual(groups);
    });

    test('should use current day if no day specified for weekly reports', async () => {
      const today = new Date().getDay();
      mockGroupRepo.getGroupsForWeeklyReports.mockResolvedValue([]);

      await groupManager.getGroupsForWeeklyReports();

      expect(mockGroupRepo.getGroupsForWeeklyReports).toHaveBeenCalledWith(today);
    });
  });

  describe('analytics', () => {
    beforeEach(async () => {
      await groupManager.initialize();
    });

    test('should get group analytics from cache if available', async () => {
      const cachedAnalytics = { totalGroups: 10, totalMembers: 500 };
      mockCache.get.mockResolvedValue(JSON.stringify(cachedAnalytics));

      const result = await groupManager.getGroupAnalytics();

      expect(mockCache.get).toHaveBeenCalledWith('analytics:groups:global');
      expect(mockGroupRepo.getGroupAnalytics).not.toHaveBeenCalled();
      expect(result).toEqual(cachedAnalytics);
    });

    test('should get group analytics from database if not cached', async () => {
      const dbAnalytics = { totalGroups: 10, totalMembers: 500 };
      mockCache.get.mockResolvedValue(null);
      mockGroupRepo.getGroupAnalytics.mockResolvedValue(dbAnalytics);
      mockCache.setEx.mockResolvedValue();

      const result = await groupManager.getGroupAnalytics();

      expect(mockGroupRepo.getGroupAnalytics).toHaveBeenCalled();
      expect(mockCache.setEx).toHaveBeenCalledWith('analytics:groups:global', 900, JSON.stringify(dbAnalytics));
      expect(result).toEqual(dbAnalytics);
    });

    test('should get command usage statistics', async () => {
      const commandStats = {
        totalDukienlo: 100,
        totalDukiende: 80,
        totalLichsu: 50
      };
      mockGroupRepo.getCommandUsageStats.mockResolvedValue(commandStats);

      const result = await groupManager.getCommandUsageStats();

      expect(mockGroupRepo.getCommandUsageStats).toHaveBeenCalled();
      expect(result).toEqual(commandStats);
    });
  });

  describe('group lifecycle', () => {
    beforeEach(async () => {
      await groupManager.initialize();
    });

    test('should deactivate group successfully', async () => {
      mockGroupRepo.deactivateGroup.mockResolvedValue();
      mockCache.del.mockResolvedValue();

      const result = await groupManager.deactivateGroup(-123456789, 'bot_removed');

      expect(mockGroupRepo.deactivateGroup).toHaveBeenCalledWith(-123456789, 'bot_removed');
      expect(mockCache.del).toHaveBeenCalledWith('group:settings:-123456789');
      expect(result).toBe(true);
    });
  });

  describe('cleanup', () => {
    test('should cleanup resources properly', async () => {
      await groupManager.initialize();
      
      await groupManager.cleanup();

      expect(mockCache.quit).toHaveBeenCalled();
      expect(groupManager.isInitialized).toBe(false);
    });

    test('should handle cleanup errors gracefully', async () => {
      await groupManager.initialize();
      mockCache.quit.mockRejectedValue(new Error('Cleanup error'));

      // Should not throw
      await expect(groupManager.cleanup()).resolves.toBeUndefined();
      expect(groupManager.isInitialized).toBe(false);
    });
  });
});
