// Unit tests for UserManager
// Tests user registration, preferences, analytics, and interaction logging

const UserManager = require('../../../lib/managers/userManager');
const UserRepository = require('../../../lib/data/repositories/userRepo');

// Mock dependencies
jest.mock('../../../lib/connections/mongo');
jest.mock('../../../lib/connections/redis');
jest.mock('../../../lib/logger');

describe('UserManager', () => {
  let userManager;
  let mockUserRepo;
  let mockCache;
  let mockMongoConnection;
  let mockRedisConnection;

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();

    // Mock UserRepository
    mockUserRepo = {
      createIndexes: jest.fn().mockResolvedValue(),
      findByTelegramId: jest.fn(),
      create: jest.fn(),
      updatePreferences: jest.fn(),
      updateStats: jest.fn(),
      logInteraction: jest.fn(),
      getStats: jest.fn(),
      getUserAnalytics: jest.fn(),
      getActiveUsers: jest.fn(),
      getInteractionHistory: jest.fn()
    };

    // Mock the UserRepository constructor
    jest.doMock('../../../lib/data/repositories/userRepo', () => {
      return jest.fn().mockImplementation(() => mockUserRepo);
    });

    // Mock Redis cache
    mockCache = {
      connect: jest.fn().mockResolvedValue(),
      get: jest.fn(),
      setEx: jest.fn(),
      del: jest.fn(),
      quit: jest.fn(),
      isOpen: true
    };

    // Mock MongoDB connection
    mockMongoConnection = {
      db: { collection: jest.fn() }
    };

    // Mock connections
    require('../../../lib/connections/mongo').mockReturnValue(mockMongoConnection);
    require('../../../lib/connections/redis').mockReturnValue({
      getConnection: () => mockCache
    });

    userManager = new UserManager({
      cacheEnabled: true,
      cacheTTL: 3600,
      privacyCompliant: true
    });
  });

  afterEach(async () => {
    if (userManager.isInitialized) {
      await userManager.cleanup();
    }
  });

  describe('initialization', () => {
    test('should initialize successfully with valid connections', async () => {
      await userManager.initialize();

      expect(userManager.isInitialized).toBe(true);
      expect(mockUserRepo.createIndexes).toHaveBeenCalled();
      expect(mockCache.connect).toHaveBeenCalled();
    });

    test('should handle MongoDB connection failure', async () => {
      require('../../../lib/connections/mongo').mockReturnValue(null);

      await expect(userManager.initialize()).rejects.toThrow('MongoDB connection not available');
      expect(userManager.isInitialized).toBe(false);
    });

    test('should work without Redis cache', async () => {
      require('../../../lib/connections/redis').mockReturnValue(null);

      await userManager.initialize();

      expect(userManager.isInitialized).toBe(true);
      expect(userManager.options.cacheEnabled).toBe(false);
    });
  });

  describe('user registration', () => {
    beforeEach(async () => {
      await userManager.initialize();
    });

    test('should register new user successfully', async () => {
      const telegramUserData = {
        id: 123456789,
        username: 'testuser',
        first_name: 'Test',
        last_name: 'User',
        language_code: 'vi'
      };

      const expectedUser = {
        telegramId: 123456789,
        username: 'testuser',
        firstName: 'Test',
        lastName: 'User',
        languageCode: 'vi'
      };

      mockUserRepo.findByTelegramId.mockResolvedValue(null);
      mockUserRepo.create.mockResolvedValue(expectedUser);
      mockCache.setEx.mockResolvedValue();

      const result = await userManager.registerUser(telegramUserData);

      expect(mockUserRepo.findByTelegramId).toHaveBeenCalledWith(123456789);
      expect(mockUserRepo.create).toHaveBeenCalledWith(
        expect.objectContaining({
          telegramId: 123456789,
          username: 'testuser',
          firstName: 'Test',
          lastName: 'User',
          languageCode: 'vi'
        })
      );
      expect(result).toEqual(expectedUser);
    });

    test('should return existing user if already registered', async () => {
      const telegramUserData = { id: 123456789 };
      const existingUser = { telegramId: 123456789, username: 'existing' };

      mockUserRepo.findByTelegramId.mockResolvedValue(existingUser);
      mockUserRepo.updateStats.mockResolvedValue();

      const result = await userManager.registerUser(telegramUserData);

      expect(mockUserRepo.findByTelegramId).toHaveBeenCalledWith(123456789);
      expect(mockUserRepo.create).not.toHaveBeenCalled();
      expect(mockUserRepo.updateStats).toHaveBeenCalledWith(123456789, {});
      expect(result).toEqual(existingUser);
    });

    test('should throw error if not initialized', async () => {
      userManager.isInitialized = false;

      await expect(userManager.registerUser({ id: 123 }))
        .rejects.toThrow('UserManager not initialized');
    });
  });

  describe('user retrieval', () => {
    beforeEach(async () => {
      await userManager.initialize();
    });

    test('should get user from cache if available', async () => {
      const cachedUser = { telegramId: 123, username: 'cached' };
      mockCache.get.mockResolvedValue(JSON.stringify(cachedUser));

      const result = await userManager.getUserById(123);

      expect(mockCache.get).toHaveBeenCalledWith('user:123');
      expect(mockUserRepo.findByTelegramId).not.toHaveBeenCalled();
      expect(result).toEqual(cachedUser);
    });

    test('should get user from database if not cached', async () => {
      const dbUser = { telegramId: 123, username: 'fromdb' };
      mockCache.get.mockResolvedValue(null);
      mockUserRepo.findByTelegramId.mockResolvedValue(dbUser);
      mockCache.setEx.mockResolvedValue();

      const result = await userManager.getUserById(123);

      expect(mockCache.get).toHaveBeenCalledWith('user:123');
      expect(mockUserRepo.findByTelegramId).toHaveBeenCalledWith(123);
      expect(mockCache.setEx).toHaveBeenCalledWith('user:123', 3600, JSON.stringify(dbUser));
      expect(result).toEqual(dbUser);
    });

    test('should return null if user not found', async () => {
      mockCache.get.mockResolvedValue(null);
      mockUserRepo.findByTelegramId.mockResolvedValue(null);

      const result = await userManager.getUserById(123);

      expect(result).toBeNull();
    });
  });

  describe('user preferences', () => {
    beforeEach(async () => {
      await userManager.initialize();
    });

    test('should update user preferences successfully', async () => {
      const preferences = {
        notifications: false,
        favoriteNumbers: ['12', '34', '56'],
        timezone: 'Asia/Bangkok',
        language: 'en'
      };

      mockUserRepo.updatePreferences.mockResolvedValue();
      mockCache.del.mockResolvedValue();

      const result = await userManager.updateUserPreferences(123, preferences);

      expect(mockUserRepo.updatePreferences).toHaveBeenCalledWith(123, preferences);
      expect(mockCache.del).toHaveBeenCalledWith('user:123');
      expect(mockCache.del).toHaveBeenCalledWith('user:stats:123');
      expect(result).toBe(true);
    });

    test('should validate preferences before updating', async () => {
      const invalidPreferences = {
        notifications: 'invalid',
        favoriteNumbers: ['12', '34', 'invalid', '56', '78', '90', '11', '22', '33', '44', '55', '66'], // too many
        timezone: 123,
        language: null
      };

      mockUserRepo.updatePreferences.mockResolvedValue();

      await userManager.updateUserPreferences(123, invalidPreferences);

      expect(mockUserRepo.updatePreferences).toHaveBeenCalledWith(123, {
        notifications: false, // converted to boolean
        favoriteNumbers: ['12', '34', '56', '78', '90', '11', '22', '33', '44', '55'], // limited to 10, invalid removed
        // timezone and language should be omitted due to invalid types
      });
    });
  });

  describe('interaction logging', () => {
    beforeEach(async () => {
      await userManager.initialize();
    });

    test('should log user interaction successfully', async () => {
      const metadata = {
        command: 'dukienlo',
        queriedNumber: '12',
        success: true,
        ip: '***********' // should be removed for privacy
      };

      mockUserRepo.logInteraction.mockResolvedValue();
      mockCache.del.mockResolvedValue();

      await userManager.logUserInteraction(123, 'dukienlo', metadata);

      expect(mockUserRepo.logInteraction).toHaveBeenCalledWith(123, 'dukienlo', {
        command: 'dukienlo',
        queriedNumber: '12',
        success: true,
        timestamp: expect.any(Date)
        // ip should be removed
      });
      expect(mockCache.del).toHaveBeenCalledWith('user:123');
    });

    test('should not throw error if logging fails', async () => {
      mockUserRepo.logInteraction.mockRejectedValue(new Error('Database error'));

      // Should not throw
      await expect(userManager.logUserInteraction(123, 'dukienlo', {}))
        .resolves.toBeUndefined();
    });
  });

  describe('user statistics', () => {
    beforeEach(async () => {
      await userManager.initialize();
    });

    test('should get user stats from cache if available', async () => {
      const cachedStats = { totalQueries: 10, lastActive: new Date() };
      mockCache.get.mockResolvedValue(JSON.stringify(cachedStats));

      const result = await userManager.getUserStats(123);

      expect(mockCache.get).toHaveBeenCalledWith('user:stats:123');
      expect(mockUserRepo.getStats).not.toHaveBeenCalled();
      expect(result).toEqual(cachedStats);
    });

    test('should get user stats from database if not cached', async () => {
      const dbStats = { totalQueries: 5, lastActive: new Date() };
      mockCache.get.mockResolvedValue(null);
      mockUserRepo.getStats.mockResolvedValue(dbStats);
      mockCache.setEx.mockResolvedValue();

      const result = await userManager.getUserStats(123);

      expect(mockUserRepo.getStats).toHaveBeenCalledWith(123);
      expect(mockCache.setEx).toHaveBeenCalledWith('user:stats:123', 3600, JSON.stringify(dbStats));
      expect(result).toEqual(dbStats);
    });
  });

  describe('analytics', () => {
    beforeEach(async () => {
      await userManager.initialize();
    });

    test('should get user analytics from cache if available', async () => {
      const cachedAnalytics = { totalUsers: 100, activeToday: 20 };
      mockCache.get.mockResolvedValue(JSON.stringify(cachedAnalytics));

      const result = await userManager.getUserAnalytics();

      expect(mockCache.get).toHaveBeenCalledWith('analytics:users:global');
      expect(mockUserRepo.getUserAnalytics).not.toHaveBeenCalled();
      expect(result).toEqual(cachedAnalytics);
    });

    test('should get user analytics from database if not cached', async () => {
      const dbAnalytics = { totalUsers: 100, activeToday: 20 };
      mockCache.get.mockResolvedValue(null);
      mockUserRepo.getUserAnalytics.mockResolvedValue(dbAnalytics);
      mockCache.setEx.mockResolvedValue();

      const result = await userManager.getUserAnalytics();

      expect(mockUserRepo.getUserAnalytics).toHaveBeenCalled();
      expect(mockCache.setEx).toHaveBeenCalledWith('analytics:users:global', 900, JSON.stringify(dbAnalytics));
      expect(result).toEqual(dbAnalytics);
    });

    test('should get active users count', async () => {
      mockUserRepo.getActiveUsers.mockResolvedValue(25);

      const result = await userManager.getActiveUsers(7);

      expect(mockUserRepo.getActiveUsers).toHaveBeenCalledWith(7);
      expect(result).toBe(25);
    });

    test('should get user interaction history', async () => {
      const history = [
        { command: 'dukienlo', timestamp: new Date() },
        { command: 'dukiende', timestamp: new Date() }
      ];
      mockUserRepo.getInteractionHistory.mockResolvedValue(history);

      const result = await userManager.getUserInteractionHistory(123, 10);

      expect(mockUserRepo.getInteractionHistory).toHaveBeenCalledWith(123, 10);
      expect(result).toEqual(history);
    });
  });

  describe('cleanup', () => {
    test('should cleanup resources properly', async () => {
      await userManager.initialize();

      await userManager.cleanup();

      expect(mockCache.quit).toHaveBeenCalled();
      expect(userManager.isInitialized).toBe(false);
    });

    test('should handle cleanup errors gracefully', async () => {
      await userManager.initialize();
      mockCache.quit.mockRejectedValue(new Error('Cleanup error'));

      // Should not throw
      await expect(userManager.cleanup()).resolves.toBeUndefined();
      expect(userManager.isInitialized).toBe(false);
    });
  });
});
