const PredictionPrompts = require('../../../lib/llm/prompts/prediction');
const AnalysisPrompts = require('../../../lib/llm/prompts/analysis');
const TrendsPrompts = require('../../../lib/llm/prompts/trends');

describe('Prompt Templates', () => {
  describe('PredictionPrompts', () => {
    const samplePredictions = [
      { number: '12', confidence: 75, method: 'lstm' },
      { number: '34', confidence: 68, method: 'statistical' }
    ];

    const sampleHistoricalData = [
      {
        date: '2024-01-01',
        numbers: { lo: ['12', '34', '56'], de: ['78'] }
      },
      {
        date: '2024-01-02',
        numbers: { lo: ['23', '45', '67'], de: ['89'] }
      }
    ];

    describe('generatePredictionPrompt', () => {
      it('should generate prediction prompt for lô', () => {
        const prompt = PredictionPrompts.generatePredictionPrompt(
          samplePredictions,
          sampleHistoricalData,
          'lo'
        );

        expect(prompt).toContain('số lô');
        expect(prompt).toContain('Số 12 - Độ tin cậy: 75%');
        expect(prompt).toContain('Số 34 - Độ tin cậy: 68%');
        expect(prompt).toContain('1/1/2024');
        expect(prompt).toContain('Lô [12, 34, 56]');
        expect(prompt).toContain('Đánh giá độ tin cậy');
        expect(prompt).toContain('Phân tích xu hướng');
        expect(prompt).toContain('Lời khuyên thực tế');
        expect(prompt).toContain('Cảnh báo rủi ro');
      });

      it('should generate prediction prompt for đề', () => {
        const prompt = PredictionPrompts.generatePredictionPrompt(
          samplePredictions,
          sampleHistoricalData,
          'de'
        );

        expect(prompt).toContain('số đề');
        expect(prompt).toContain('Đề [78]');
      });

      it('should handle empty predictions', () => {
        const prompt = PredictionPrompts.generatePredictionPrompt(
          [],
          sampleHistoricalData,
          'lo'
        );

        expect(prompt).toContain('Không có dự đoán');
      });

      it('should handle empty historical data', () => {
        const prompt = PredictionPrompts.generatePredictionPrompt(
          samplePredictions,
          [],
          'lo'
        );

        expect(prompt).toContain('Không có dữ liệu gần đây');
      });
    });

    describe('generateAccuracyAnalysisPrompt', () => {
      const actualResult = {
        numbers: { lo: ['12', '56', '78'], de: ['34'] }
      };

      it('should generate accuracy analysis prompt', () => {
        const prompt = PredictionPrompts.generateAccuracyAnalysisPrompt(
          samplePredictions,
          actualResult,
          'lo'
        );

        expect(prompt).toContain('độ chính xác');
        expect(prompt).toContain('12, 56, 78');
        expect(prompt).toContain('Số lượng dự đoán trúng');
        expect(prompt).toContain('Tỷ lệ chính xác');
        expect(prompt).toContain('Phân tích sai lệch');
      });
    });

    describe('generateExplanationPrompt', () => {
      it('should generate explanation prompt', () => {
        const prediction = { number: '12', confidence: 75, method: 'lstm' };
        const supportingData = {
          frequency: 5,
          lastSeen: '2024-01-01',
          trend: 'increasing'
        };

        const prompt = PredictionPrompts.generateExplanationPrompt(
          prediction,
          supportingData
        );

        expect(prompt).toContain('số 12');
        expect(prompt).toContain('75%');
        expect(prompt).toContain('lstm');
        expect(prompt).toContain('Tần suất xuất hiện gần đây: 5');
        expect(prompt).toContain('2024-01-01');
        expect(prompt).toContain('increasing');
      });
    });

    describe('formatPredictions', () => {
      it('should format predictions correctly', () => {
        const formatted = PredictionPrompts.formatPredictions(samplePredictions);

        expect(formatted).toContain('1. Số 12 - Độ tin cậy: 75% (lstm)');
        expect(formatted).toContain('2. Số 34 - Độ tin cậy: 68% (statistical)');
      });

      it('should handle empty predictions', () => {
        const formatted = PredictionPrompts.formatPredictions([]);
        expect(formatted).toBe('Không có dự đoán');
      });

      it('should handle null predictions', () => {
        const formatted = PredictionPrompts.formatPredictions(null);
        expect(formatted).toBe('Không có dự đoán');
      });
    });

    describe('formatRecentResults', () => {
      it('should format recent results correctly', () => {
        const formatted = PredictionPrompts.formatRecentResults(sampleHistoricalData);

        expect(formatted).toContain('1/1/2024');
        expect(formatted).toContain('Lô [12, 34, 56]');
        expect(formatted).toContain('Đề [78]');
      });

      it('should handle empty results', () => {
        const formatted = PredictionPrompts.formatRecentResults([]);
        expect(formatted).toBe('Không có dữ liệu gần đây');
      });
    });
  });

  describe('AnalysisPrompts', () => {
    const sampleFrequency = {
      frequency: 5,
      period: 30,
      lastSeen: '2024-01-01',
      average: 2.5,
      trend: 'increasing'
    };

    const samplePatterns = [
      { type: 'sequence', description: 'Xuất hiện liên tiếp', confidence: 80 },
      { type: 'pair', description: 'Thường đi cùng số 34', confidence: 65 }
    ];

    const sampleCorrelations = {
      '34': { strength: 75, frequency: 3 },
      '56': { strength: 60, frequency: 2 }
    };

    describe('generateNumberAnalysisPrompt', () => {
      it('should generate number analysis prompt', () => {
        const prompt = AnalysisPrompts.generateNumberAnalysisPrompt(
          '12',
          sampleFrequency,
          samplePatterns,
          sampleCorrelations
        );

        expect(prompt).toContain('số 12');
        expect(prompt).toContain('5 lần trong 30 ngày');
        expect(prompt).toContain('1/1/2024');
        expect(prompt).toContain('increasing');
        expect(prompt).toContain('Xuất hiện liên tiếp');
        expect(prompt).toContain('Với số 34: 75%');
        expect(prompt).toContain('Đánh giá tần suất');
        expect(prompt).toContain('Phân tích mẫu');
        expect(prompt).toContain('Dự báo khả năng');
      });

      it('should handle missing data', () => {
        const prompt = AnalysisPrompts.generateNumberAnalysisPrompt(
          '12',
          null,
          [],
          {}
        );

        expect(prompt).toContain('Không có dữ liệu tần suất');
        expect(prompt).toContain('Không phát hiện mẫu đặc biệt');
        expect(prompt).toContain('Không có dữ liệu tương quan');
      });
    });

    describe('generatePatternAnalysisPrompt', () => {
      const detailedPatterns = [
        {
          name: 'Sequence Pattern',
          type: 'sequence',
          description: 'Numbers appear in sequence',
          frequency: 5,
          confidence: 80,
          lastOccurrence: '2024-01-01'
        }
      ];

      it('should generate pattern analysis prompt', () => {
        const prompt = AnalysisPrompts.generatePatternAnalysisPrompt(
          detailedPatterns,
          '30 ngày'
        );

        expect(prompt).toContain('30 ngày');
        expect(prompt).toContain('Sequence Pattern');
        expect(prompt).toContain('sequence');
        expect(prompt).toContain('5 lần');
        expect(prompt).toContain('80%');
        expect(prompt).toContain('1/1/2024');
      });
    });

    describe('generateCorrelationAnalysisPrompt', () => {
      it('should generate correlation analysis prompt', () => {
        const correlations = {
          '12': {
            strongest: '34 (75%)',
            weakest: '78 (25%)',
            average: 50
          }
        };
        const targetNumbers = ['12'];

        const prompt = AnalysisPrompts.generateCorrelationAnalysisPrompt(
          correlations,
          targetNumbers
        );

        expect(prompt).toContain('Số 12');
        expect(prompt).toContain('34 (75%)');
        expect(prompt).toContain('78 (25%)');
        expect(prompt).toContain('50%');
      });
    });

    describe('formatting methods', () => {
      it('should format frequency data', () => {
        const formatted = AnalysisPrompts.formatFrequencyData(sampleFrequency);

        expect(formatted).toContain('5 lần trong 30 ngày');
        expect(formatted).toContain('1/1/2024');
        expect(formatted).toContain('2.5 lần/tháng');
        expect(formatted).toContain('increasing');
      });

      it('should format patterns', () => {
        const formatted = AnalysisPrompts.formatPatterns(samplePatterns);

        expect(formatted).toContain('1. sequence: Xuất hiện liên tiếp (Độ tin cậy: 80%)');
        expect(formatted).toContain('2. pair: Thường đi cùng số 34 (Độ tin cậy: 65%)');
      });

      it('should format correlations', () => {
        const formatted = AnalysisPrompts.formatCorrelations(sampleCorrelations);

        expect(formatted).toContain('Với số 34: 75% (3 lần cùng xuất hiện)');
        expect(formatted).toContain('Với số 56: 60% (2 lần cùng xuất hiện)');
      });
    });
  });

  describe('TrendsPrompts', () => {
    const sampleTrends = {
      hot: [
        { number: '12', frequency: 8, percentage: 26.7 },
        { number: '34', frequency: 6, percentage: 20.0 }
      ],
      cold: [
        { number: '78', frequency: 1, percentage: 3.3 },
        { number: '90', frequency: 2, percentage: 6.7 }
      ],
      neutral: [
        { number: '56', frequency: 4, percentage: 13.3 }
      ]
    };

    describe('generateTrendAnalysisPrompt', () => {
      it('should generate trend analysis prompt', () => {
        const prompt = TrendsPrompts.generateTrendAnalysisPrompt(
          sampleTrends,
          'lo',
          '30 ngày'
        );

        expect(prompt).toContain('số lô');
        expect(prompt).toContain('30 ngày');
        expect(prompt).toContain('1. Số 12: 8 lần (26.7%)');
        expect(prompt).toContain('1. Số 78: 1 lần (3.3%)');
        expect(prompt).toContain('1. Số 56: 4 lần (13.3%)');
        expect(prompt).toContain('Đánh giá xu hướng nóng');
        expect(prompt).toContain('Phân tích số lạnh');
        expect(prompt).toContain('Xu hướng tổng thể');
        expect(prompt).toContain('Chiến lược ứng dụng');
      });

      it('should handle empty trends', () => {
        const emptyTrends = { hot: [], cold: [], neutral: [] };
        const prompt = TrendsPrompts.generateTrendAnalysisPrompt(emptyTrends, 'lo');

        expect(prompt).toContain('Không có số nóng');
        expect(prompt).toContain('Không có số lạnh');
        expect(prompt).toContain('Không có số trung bình');
      });
    });

    describe('generateWeeklyTrendPrompt', () => {
      const weeklyData = [
        {
          startDate: '2024-01-01',
          endDate: '2024-01-07',
          topNumbers: [{ number: '12' }, { number: '34' }],
          totalDraws: 7,
          trend: 'increasing'
        }
      ];

      it('should generate weekly trend prompt', () => {
        const prompt = TrendsPrompts.generateWeeklyTrendPrompt(weeklyData, 'lo');

        expect(prompt).toContain('số lô');
        expect(prompt).toContain('1/1/2024 - 7/1/2024');
        expect(prompt).toContain('12, 34');
        expect(prompt).toContain('7');
        expect(prompt).toContain('increasing');
        expect(prompt).toContain('Tóm tắt tuần');
        expect(prompt).toContain('Dự báo tuần tới');
      });
    });

    describe('generateMonthlyTrendPrompt', () => {
      const monthlyData = {
        month: 1,
        year: 2024,
        totalDraws: 31,
        topNumbers: [
          { number: '12', frequency: 8 },
          { number: '34', frequency: 6 }
        ],
        bottomNumbers: [
          { number: '78', frequency: 1 },
          { number: '90', frequency: 2 }
        ],
        averagePerNumber: 3.1,
        standardDeviation: 2.5
      };

      it('should generate monthly trend prompt', () => {
        const prompt = TrendsPrompts.generateMonthlyTrendPrompt(monthlyData, 'lo');

        expect(prompt).toContain('số lô');
        expect(prompt).toContain('tháng 1/2024');
        expect(prompt).toContain('31');
        expect(prompt).toContain('12 (8 lần), 34 (6 lần)');
        expect(prompt).toContain('78 (1 lần), 90 (2 lần)');
        expect(prompt).toContain('3.1');
        expect(prompt).toContain('2.5');
      });
    });

    describe('generateTrendComparisonPrompt', () => {
      const currentTrends = {
        hot: [{ number: '12' }],
        cold: [{ number: '78' }],
        totalDraws: 30,
        period: 'Current month'
      };

      const previousTrends = {
        hot: [{ number: '34' }],
        cold: [{ number: '90' }],
        totalDraws: 31,
        period: 'Previous month'
      };

      it('should generate trend comparison prompt', () => {
        const prompt = TrendsPrompts.generateTrendComparisonPrompt(
          currentTrends,
          previousTrends,
          'lo'
        );

        expect(prompt).toContain('số lô');
        expect(prompt).toContain('Số nóng: 12');
        expect(prompt).toContain('Số lạnh: 78');
        expect(prompt).toContain('Số nóng: 34');
        expect(prompt).toContain('Số lạnh: 90');
        expect(prompt).toContain('Thay đổi xu hướng');
        expect(prompt).toContain('Phân tích nguyên nhân');
      });
    });

    describe('formatting methods', () => {
      it('should format trend numbers', () => {
        const formatted = TrendsPrompts.formatTrendNumbers(sampleTrends.hot, 'nóng');

        expect(formatted).toContain('1. Số 12: 8 lần (26.7%)');
        expect(formatted).toContain('2. Số 34: 6 lần (20.0%)');
      });

      it('should handle empty trend numbers', () => {
        const formatted = TrendsPrompts.formatTrendNumbers([], 'nóng');
        expect(formatted).toBe('Không có số nóng');
      });

      it('should format weekly data', () => {
        const weeklyData = [
          {
            startDate: '2024-01-01',
            endDate: '2024-01-07',
            topNumbers: [{ number: '12' }],
            totalDraws: 7,
            trend: 'stable'
          }
        ];

        const formatted = TrendsPrompts.formatWeeklyData(weeklyData);

        expect(formatted).toContain('1/1/2024 - 7/1/2024');
        expect(formatted).toContain('12');
        expect(formatted).toContain('7');
        expect(formatted).toContain('stable');
      });
    });
  });
});