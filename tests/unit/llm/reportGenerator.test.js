const ReportGenerator = require('../../../lib/llm/reportGenerator');

describe('ReportGenerator', () => {
  let reportGenerator;
  let mockLLMService;

  beforeEach(() => {
    mockLLMService = {
      generatePredictionReport: jest.fn().mockResolvedValue('Mock prediction report'),
      generateTrendAnalysis: jest.fn().mockResolvedValue('Mock trend analysis'),
      generateNumberAnalysis: jest.fn().mockResolvedValue('Mock number analysis'),
      generateWeeklyReport: jest.fn().mockResolvedValue('Mock weekly report'),
      getUsageStats: jest.fn().mockReturnValue({
        dailyUsage: 5.50,
        dailyBudget: 50,
        budgetRemaining: 44.50
      }),
      options: {
        model: 'gpt-3.5-turbo'
      }
    };

    reportGenerator = new ReportGenerator(mockLLMService, {
      enableCostTracking: true,
      maxReportLength: 4000
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('constructor', () => {
    it('should initialize with default options', () => {
      const generator = new ReportGenerator(mockLLMService);

      expect(generator.llmService).toBe(mockLLMService);
      expect(generator.options.defaultLanguage).toBe('vi');
      expect(generator.options.maxReportLength).toBe(4000);
      expect(generator.options.enableCostTracking).toBe(true);
    });

    it('should initialize with custom options', () => {
      const customOptions = {
        defaultLanguage: 'en',
        maxReportLength: 2000,
        enableCostTracking: false
      };

      const generator = new ReportGenerator(mockLLMService, customOptions);

      expect(generator.options.defaultLanguage).toBe('en');
      expect(generator.options.maxReportLength).toBe(2000);
      expect(generator.options.enableCostTracking).toBe(false);
    });

    it('should initialize cost tracking maps', () => {
      expect(reportGenerator.dailyCosts).toBeInstanceOf(Map);
      expect(reportGenerator.monthlyCosts).toBeInstanceOf(Map);
      expect(reportGenerator.reportCounts).toBeInstanceOf(Map);
    });

    it('should initialize report templates', () => {
      expect(reportGenerator.templates).toHaveProperty('prediction');
      expect(reportGenerator.templates).toHaveProperty('trend');
      expect(reportGenerator.templates).toHaveProperty('analysis');
      expect(reportGenerator.templates).toHaveProperty('weekly');
      expect(reportGenerator.templates).toHaveProperty('monthly');
    });
  });

  describe('generatePredictionReport', () => {
    const samplePredictions = [
      { number: '12', confidence: 75, method: 'lstm' },
      { number: '34', confidence: 68, method: 'statistical' }
    ];

    const sampleHistoricalData = [
      { date: '2024-01-01', numbers: { lo: ['12', '34'], de: ['56'] } }
    ];

    it('should generate prediction report successfully', async () => {
      const result = await reportGenerator.generatePredictionReport(
        samplePredictions,
        sampleHistoricalData,
        'lo'
      );

      expect(result).toHaveProperty('content');
      expect(result).toHaveProperty('metadata');
      expect(result.metadata.type).toBe('prediction');
      expect(result.metadata.subtype).toBe('lo');
      expect(result.metadata.predictionsCount).toBe(2);
      expect(result.metadata.historicalDataPoints).toBe(1);
      expect(result.metadata.language).toBe('vi');
      expect(result.metadata.isFallback).toBeUndefined();

      expect(mockLLMService.generatePredictionReport).toHaveBeenCalledWith(
        samplePredictions,
        sampleHistoricalData,
        'lo'
      );
    });

    it('should handle LLM service errors gracefully', async () => {
      mockLLMService.generatePredictionReport.mockRejectedValue(new Error('LLM Error'));

      const result = await reportGenerator.generatePredictionReport(
        samplePredictions,
        sampleHistoricalData,
        'lo'
      );

      expect(result.metadata.isFallback).toBe(true);
      expect(result.metadata.error).toBe('LLM Error');
      expect(result.content).toContain('Báo Cáo Dự Đoán');
    });

    it('should track report generation', async () => {
      const trackSpy = jest.spyOn(reportGenerator, 'trackReportGeneration');

      await reportGenerator.generatePredictionReport(
        samplePredictions,
        sampleHistoricalData,
        'lo'
      );

      expect(trackSpy).toHaveBeenCalledWith('prediction', 'lo');
    });

    it('should track costs when enabled', async () => {
      const trackCostSpy = jest.spyOn(reportGenerator, 'trackReportCost');

      await reportGenerator.generatePredictionReport(
        samplePredictions,
        sampleHistoricalData,
        'lo'
      );

      expect(trackCostSpy).toHaveBeenCalled();
    });

    it('should not track costs when disabled', async () => {
      reportGenerator.options.enableCostTracking = false;
      const trackCostSpy = jest.spyOn(reportGenerator, 'trackReportCost');

      await reportGenerator.generatePredictionReport(
        samplePredictions,
        sampleHistoricalData,
        'lo'
      );

      expect(trackCostSpy).not.toHaveBeenCalled();
    });

    it('should validate content quality', async () => {
      const result = await reportGenerator.generatePredictionReport(
        samplePredictions,
        sampleHistoricalData,
        'lo'
      );

      expect(result.metadata.validation).toHaveProperty('isValid');
      expect(result.metadata.validation).toHaveProperty('issues');
      expect(result.metadata.validation).toHaveProperty('length');
    });
  });

  describe('generateTrendReport', () => {
    const sampleTrends = {
      hot: [{ number: '12', frequency: 8 }],
      cold: [{ number: '78', frequency: 1 }]
    };

    it('should generate trend report successfully', async () => {
      const result = await reportGenerator.generateTrendReport(
        sampleTrends,
        'lo',
        '30 ngày'
      );

      expect(result).toHaveProperty('content');
      expect(result).toHaveProperty('metadata');
      expect(result.metadata.type).toBe('trend');
      expect(result.metadata.subtype).toBe('lo');
      expect(result.metadata.period).toBe('30 ngày');
      expect(result.metadata.hotNumbers).toBe(1);
      expect(result.metadata.coldNumbers).toBe(1);

      expect(mockLLMService.generateTrendAnalysis).toHaveBeenCalledWith(
        sampleTrends,
        'lo',
        '30 ngày'
      );
    });

    it('should handle errors with fallback', async () => {
      mockLLMService.generateTrendAnalysis.mockRejectedValue(new Error('Trend Error'));

      const result = await reportGenerator.generateTrendReport(sampleTrends, 'lo');

      expect(result.metadata.isFallback).toBe(true);
      expect(result.metadata.error).toBe('Trend Error');
      expect(result.content).toContain('Phân Tích Xu Hướng');
    });
  });

  describe('generateNumberAnalysisReport', () => {
    const sampleFrequency = { frequency: 5, period: 30, lastSeen: '2024-01-01' };
    const samplePatterns = [{ type: 'sequence', confidence: 80 }];
    const sampleCorrelations = { '34': { strength: 75, frequency: 3 } };

    it('should generate number analysis report successfully', async () => {
      const result = await reportGenerator.generateNumberAnalysisReport(
        '12',
        sampleFrequency,
        samplePatterns,
        sampleCorrelations
      );

      expect(result).toHaveProperty('content');
      expect(result).toHaveProperty('metadata');
      expect(result.metadata.type).toBe('analysis');
      expect(result.metadata.subtype).toBe('number');
      expect(result.metadata.number).toBe('12');
      expect(result.metadata.frequency).toBe(5);
      expect(result.metadata.patternsCount).toBe(1);
      expect(result.metadata.correlationsCount).toBe(1);

      expect(mockLLMService.generateNumberAnalysis).toHaveBeenCalledWith(
        '12',
        sampleFrequency,
        samplePatterns,
        sampleCorrelations
      );
    });

    it('should handle errors with fallback', async () => {
      mockLLMService.generateNumberAnalysis.mockRejectedValue(new Error('Analysis Error'));

      const result = await reportGenerator.generateNumberAnalysisReport(
        '12',
        sampleFrequency,
        samplePatterns
      );

      expect(result.metadata.isFallback).toBe(true);
      expect(result.metadata.error).toBe('Analysis Error');
      expect(result.content).toContain('Phân Tích Số 12');
    });
  });

  describe('generateWeeklyReport', () => {
    const sampleWeeklyData = [
      {
        startDate: '2024-01-01',
        endDate: '2024-01-07',
        topNumbers: [{ number: '12', frequency: 3 }],
        totalDraws: 7
      }
    ];

    it('should generate weekly report successfully', async () => {
      const result = await reportGenerator.generateWeeklyReport(sampleWeeklyData, 'lo');

      expect(result).toHaveProperty('content');
      expect(result).toHaveProperty('metadata');
      expect(result.metadata.type).toBe('weekly');
      expect(result.metadata.subtype).toBe('lo');
      expect(result.metadata.weeksAnalyzed).toBe(1);

      expect(mockLLMService.generateWeeklyReport).toHaveBeenCalledWith(
        sampleWeeklyData,
        'lo'
      );
    });

    it('should handle errors with fallback', async () => {
      mockLLMService.generateWeeklyReport.mockRejectedValue(new Error('Weekly Error'));

      const result = await reportGenerator.generateWeeklyReport(sampleWeeklyData, 'lo');

      expect(result.metadata.isFallback).toBe(true);
      expect(result.metadata.error).toBe('Weekly Error');
      expect(result.content).toContain('Báo Cáo Tuần');
    });
  });

  describe('cost tracking', () => {
    beforeEach(() => {
      // Clear any existing data
      reportGenerator.dailyCosts.clear();
      reportGenerator.monthlyCosts.clear();
      reportGenerator.reportCounts.clear();
    });

    it('should track report generation counts', () => {
      reportGenerator.trackReportGeneration('prediction', 'lo');
      reportGenerator.trackReportGeneration('prediction', 'lo');
      reportGenerator.trackReportGeneration('trend', 'de');

      const today = new Date().toDateString();
      const dailyCounts = reportGenerator.reportCounts.get(today);

      expect(dailyCounts.get('prediction_lo')).toBe(2);
      expect(dailyCounts.get('trend_de')).toBe(1);
    });

    it('should estimate report costs correctly', () => {
      const report = {
        content: 'A'.repeat(2000),
        metadata: { generationTime: 3000 }
      };

      const cost = reportGenerator.estimateReportCost(report, 'prediction');
      expect(cost).toBeGreaterThan(0);
      expect(typeof cost).toBe('number');
    });

    it('should track daily costs', async () => {
      const samplePredictions = [{ number: '12', confidence: 75 }];
      const sampleHistoricalData = [];

      await reportGenerator.generatePredictionReport(
        samplePredictions,
        sampleHistoricalData,
        'lo'
      );

      const today = new Date().toDateString();
      const dailyData = reportGenerator.dailyCosts.get(today);

      expect(dailyData).toBeDefined();
      expect(dailyData.total).toBeGreaterThan(0);
      expect(dailyData.reports).toHaveLength(1);
      expect(dailyData.reports[0].type).toBe('prediction');
    });

    it('should track monthly costs', async () => {
      const samplePredictions = [{ number: '12', confidence: 75 }];
      const sampleHistoricalData = [];

      await reportGenerator.generatePredictionReport(
        samplePredictions,
        sampleHistoricalData,
        'lo'
      );

      const month = new Date().toISOString().substring(0, 7);
      const monthlyData = reportGenerator.monthlyCosts.get(month);

      expect(monthlyData).toBeDefined();
      expect(monthlyData.total).toBeGreaterThan(0);
      expect(monthlyData.dailyBreakdown).toBeInstanceOf(Map);
    });

    it('should get daily cost stats', async () => {
      const samplePredictions = [{ number: '12', confidence: 75 }];
      await reportGenerator.generatePredictionReport(samplePredictions, [], 'lo');

      const stats = reportGenerator.getCostStats('daily');

      expect(stats).toHaveProperty('date');
      expect(stats).toHaveProperty('costs');
      expect(stats).toHaveProperty('reportCounts');
      expect(stats.costs.total).toBeGreaterThan(0);
    });

    it('should get monthly cost stats', async () => {
      const samplePredictions = [{ number: '12', confidence: 75 }];
      await reportGenerator.generatePredictionReport(samplePredictions, [], 'lo');

      const stats = reportGenerator.getCostStats('monthly');

      expect(stats).toHaveProperty('month');
      expect(stats).toHaveProperty('costs');
      expect(stats).toHaveProperty('totalReports');
      expect(stats.totalReports).toBeGreaterThanOrEqual(0); // Changed to >= 0 since the count might be calculated differently
    });

    it('should get all cost stats', () => {
      const stats = reportGenerator.getCostStats('all');

      expect(stats).toHaveProperty('dailyCosts');
      expect(stats).toHaveProperty('monthlyCosts');
      expect(stats).toHaveProperty('reportCounts');
    });

    it('should reset cost tracking data', async () => {
      const samplePredictions = [{ number: '12', confidence: 75 }];
      await reportGenerator.generatePredictionReport(samplePredictions, [], 'lo');

      // Verify data exists
      expect(reportGenerator.dailyCosts.size).toBeGreaterThan(0);

      // Reset daily data
      reportGenerator.resetCostTracking('daily');

      const today = new Date().toDateString();
      expect(reportGenerator.dailyCosts.has(today)).toBe(false);
      expect(reportGenerator.reportCounts.has(today)).toBe(false);
    });

    it('should reset all cost tracking data', async () => {
      const samplePredictions = [{ number: '12', confidence: 75 }];
      await reportGenerator.generatePredictionReport(samplePredictions, [], 'lo');

      reportGenerator.resetCostTracking('all');

      expect(reportGenerator.dailyCosts.size).toBe(0);
      expect(reportGenerator.monthlyCosts.size).toBe(0);
      expect(reportGenerator.reportCounts.size).toBe(0);
    });
  });

  describe('template system', () => {
    it('should have prediction templates', () => {
      const templates = reportGenerator.getPredictionTemplates();
      expect(templates).toBeInstanceOf(Array);
      expect(templates.length).toBeGreaterThan(0);
      expect(templates[0]).toContain('Báo Cáo Dự Đoán');
    });

    it('should have trend templates', () => {
      const templates = reportGenerator.getTrendTemplates();
      expect(templates).toBeInstanceOf(Array);
      expect(templates.length).toBeGreaterThan(0);
      expect(templates[0]).toContain('Phân Tích Xu Hướng');
    });

    it('should have analysis templates', () => {
      const templates = reportGenerator.getAnalysisTemplates();
      expect(templates).toBeInstanceOf(Array);
      expect(templates.length).toBeGreaterThan(0);
      expect(templates[0]).toContain('Phân Tích Số');
    });

    it('should have weekly templates', () => {
      const templates = reportGenerator.getWeeklyTemplates();
      expect(templates).toBeInstanceOf(Array);
      expect(templates.length).toBeGreaterThan(0);
      expect(templates[0]).toContain('Báo Cáo Tuần');
    });

    it('should have monthly templates', () => {
      const templates = reportGenerator.getMonthlyTemplates();
      expect(templates).toBeInstanceOf(Array);
      expect(templates.length).toBeGreaterThan(0);
      expect(templates[0]).toContain('Báo Cáo Tháng');
    });

    it('should fill templates with data', () => {
      const template = 'Báo cáo {type} ngày {date} cho số {number}';
      const data = {
        type: 'lo',
        number: '12',
        frequency: { frequency: 5, period: 30 }
      };

      const filled = reportGenerator.fillTemplate(template, data);

      expect(filled).toContain('Báo cáo Lô');
      expect(filled).toContain('cho số 12');
      expect(filled).toMatch(/ngày \d{1,2}\/\d{1,2}\/\d{4}/); // Date format
    });

    it('should fill prediction template with predictions list', () => {
      const template = 'Dự đoán: {predictions}';
      const data = {
        predictions: [
          { number: '12', confidence: 75 },
          { number: '34', confidence: 68 }
        ]
      };

      const filled = reportGenerator.fillTemplate(template, data);

      expect(filled).toContain('1. **12** - Độ tin cậy: 75%');
      expect(filled).toContain('2. **34** - Độ tin cậy: 68%');
    });

    it('should fill trend template with hot and cold numbers', () => {
      const template = 'Nóng: {hotNumbers}, Lạnh: {coldNumbers}';
      const data = {
        trends: {
          hot: [{ number: '12' }, { number: '34' }],
          cold: [{ number: '78' }, { number: '90' }]
        }
      };

      const filled = reportGenerator.fillTemplate(template, data);

      expect(filled).toContain('Nóng: 12, 34');
      expect(filled).toContain('Lạnh: 78, 90');
    });
  });

  describe('fallback reports', () => {
    it('should generate fallback prediction report', () => {
      const predictions = [{ number: '12', confidence: 75 }];
      const result = reportGenerator.generateFallbackPredictionReport(predictions, 'lo');

      expect(result).toContain('Báo Cáo Dự Đoán Lô');
      expect(result).toContain('**12** - Độ tin cậy: 75%');
    });

    it('should generate fallback trend report', () => {
      const trends = {
        hot: [{ number: '12' }],
        cold: [{ number: '78' }]
      };
      const result = reportGenerator.generateFallbackTrendReport(trends, 'lo');

      expect(result).toContain('Phân Tích Xu Hướng Lô');
      expect(result).toContain('12');
      expect(result).toContain('78');
    });

    it('should generate fallback number analysis', () => {
      const frequency = { frequency: 5, period: 30 };
      const result = reportGenerator.generateFallbackNumberAnalysis('12', frequency);

      expect(result).toContain('Phân Tích Số 12');
      expect(result).toContain('5 lần trong 30 ngày');
    });

    it('should generate fallback weekly report', () => {
      const weeklyData = [{ startDate: '2024-01-01', endDate: '2024-01-07' }];
      const result = reportGenerator.generateFallbackWeeklyReport(weeklyData, 'lo');

      expect(result).toContain('Báo Cáo Tuần Lô');
    });
  });

  describe('error handling', () => {
    it('should handle cost tracking errors gracefully', async () => {
      // Mock a cost tracking error
      const originalEstimate = reportGenerator.estimateReportCost;
      reportGenerator.estimateReportCost = jest.fn().mockImplementation(() => {
        throw new Error('Cost estimation error');
      });

      const samplePredictions = [{ number: '12', confidence: 75 }];

      // Should not throw error
      const result = await reportGenerator.generatePredictionReport(samplePredictions, [], 'lo');

      expect(result).toHaveProperty('content');
      expect(result).toHaveProperty('metadata');

      // Restore original method
      reportGenerator.estimateReportCost = originalEstimate;
    });

    it('should handle template filling errors', () => {
      const template = 'Test {invalidPlaceholder}';
      const data = { type: 'lo' };

      // Should not throw error
      const result = reportGenerator.fillTemplate(template, data);
      expect(result).toContain('Test {invalidPlaceholder}'); // Unchanged
    });
  });
});