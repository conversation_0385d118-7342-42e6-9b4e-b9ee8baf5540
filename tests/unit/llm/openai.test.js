const OpenAIProvider = require('../../../lib/llm/providers/openai');

// Mock OpenAI
jest.mock('openai', () => {
  return jest.fn().mockImplementation(() => ({
    chat: {
      completions: {
        create: jest.fn()
      }
    }
  }));
});

describe('OpenAIProvider', () => {
  let provider;
  let mockOpenAI;

  beforeEach(() => {
    // Reset environment variables
    process.env.OPENAI_API_KEY = 'test-api-key';
    process.env.OPENAI_MODEL = 'gpt-3.5-turbo';
    process.env.OPENAI_MAX_TOKENS = '1000';

    // Create mock OpenAI instance
    mockOpenAI = {
      chat: {
        completions: {
          create: jest.fn()
        }
      }
    };

    provider = new OpenAIProvider({
      apiKey: 'test-api-key',
      model: 'gpt-3.5-turbo',
      maxTokens: 1000
    });

    // Replace the client with our mock
    provider.client = mockOpenAI;
  });

  afterEach(() => {
    jest.clearAllMocks();
    delete process.env.OPENAI_API_KEY;
    delete process.env.OPENAI_MODEL;
    delete process.env.OPENAI_MAX_TOKENS;
  });

  describe('constructor', () => {
    it('should initialize with default values', () => {
      const provider = new OpenAIProvider({ apiKey: 'test-key' });

      expect(provider.apiKey).toBe('test-key');
      expect(provider.model).toBe('gpt-3.5-turbo');
      expect(provider.maxTokens).toBe(1000);
      expect(provider.temperature).toBe(0.7);
    });

    it('should use environment variables', () => {
      process.env.OPENAI_API_KEY = 'env-key';
      process.env.OPENAI_MODEL = 'gpt-4';
      process.env.OPENAI_MAX_TOKENS = '2000';

      const provider = new OpenAIProvider();

      expect(provider.apiKey).toBe('env-key');
      expect(provider.model).toBe('gpt-4');
      expect(provider.maxTokens).toBe(2000);
    });

    it('should throw error if no API key provided', () => {
      delete process.env.OPENAI_API_KEY;

      expect(() => {
        new OpenAIProvider();
      }).toThrow('OpenAI API key is required');
    });
  });

  describe('generateCompletion', () => {
    it('should generate completion successfully', async () => {
      const mockResponse = {
        choices: [{
          message: { content: 'Test response' },
          finish_reason: 'stop'
        }],
        usage: {
          prompt_tokens: 50,
          completion_tokens: 25,
          total_tokens: 75
        },
        model: 'gpt-3.5-turbo'
      };

      mockOpenAI.chat.completions.create.mockResolvedValue(mockResponse);

      const result = await provider.generateCompletion('Test prompt');

      expect(result).toEqual({
        content: 'Test response',
        usage: {
          promptTokens: 50,
          completionTokens: 25,
          totalTokens: 75,
          cost: expect.any(Number)
        },
        model: 'gpt-3.5-turbo',
        finishReason: 'stop'
      });

      expect(mockOpenAI.chat.completions.create).toHaveBeenCalledWith({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: expect.stringContaining('chuyên gia phân tích xổ số')
          },
          {
            role: 'user',
            content: 'Test prompt'
          }
        ],
        max_tokens: 1000,
        temperature: 0.7,
        top_p: 1,
        frequency_penalty: 0,
        presence_penalty: 0
      });
    });

    it('should handle API errors', async () => {
      const error = new Error('API Error');
      error.status = 429;
      mockOpenAI.chat.completions.create.mockRejectedValue(error);

      await expect(provider.generateCompletion('Test prompt'))
        .rejects.toThrow('OpenAI API rate limit exceeded');
    });

    it('should apply rate limiting', async () => {
      const mockResponse = {
        choices: [{ message: { content: 'Test' }, finish_reason: 'stop' }],
        usage: { prompt_tokens: 10, completion_tokens: 10, total_tokens: 20 },
        model: 'gpt-3.5-turbo'
      };

      mockOpenAI.chat.completions.create.mockResolvedValue(mockResponse);

      const startTime = Date.now();

      // Make two rapid requests
      await provider.generateCompletion('Test 1');
      await provider.generateCompletion('Test 2');

      const endTime = Date.now();
      const duration = endTime - startTime;

      // Should take at least 1 second due to rate limiting (60 requests per minute)
      expect(duration).toBeGreaterThan(900);
    });

    it('should open circuit breaker after multiple failures', async () => {
      const error = new Error('Server Error');
      error.status = 500;
      mockOpenAI.chat.completions.create.mockRejectedValue(error);

      // Trigger multiple failures
      for (let i = 0; i < 5; i++) {
        try {
          await provider.generateCompletion('Test');
        } catch (e) {
          // Expected to fail
        }
      }

      // Circuit breaker should now be open
      expect(provider.isCircuitBreakerOpen()).toBe(true);

      // Next request should fail immediately
      await expect(provider.generateCompletion('Test'))
        .rejects.toThrow('circuit breaker open');
    });
  });

  describe('calculateCost', () => {
    it('should calculate cost correctly for different models', () => {
      expect(provider.calculateCost(1000, 'gpt-3.5-turbo')).toBe(0.0015);
      expect(provider.calculateCost(1000, 'gpt-4')).toBe(0.03);
      expect(provider.calculateCost(1000, 'unknown-model')).toBe(0.0015); // fallback
    });
  });

  describe('handleError', () => {
    it('should handle different error types', () => {
      const error401 = new Error('Unauthorized');
      error401.status = 401;

      expect(() => provider.handleError(error401))
        .toThrow('OpenAI API authentication failed');

      const error403 = new Error('Forbidden');
      error403.status = 403;

      expect(() => provider.handleError(error403))
        .toThrow('OpenAI API access forbidden');

      const error500 = new Error('Server Error');
      error500.status = 500;

      expect(() => provider.handleError(error500))
        .toThrow('OpenAI API server error');
    });
  });

  describe('testConnection', () => {
    it('should return true for successful connection', async () => {
      const mockResponse = {
        choices: [{ message: { content: 'OK' }, finish_reason: 'stop' }],
        usage: { prompt_tokens: 5, completion_tokens: 5, total_tokens: 10 },
        model: 'gpt-3.5-turbo'
      };

      mockOpenAI.chat.completions.create.mockResolvedValue(mockResponse);

      const result = await provider.testConnection();
      expect(result).toBe(true);
    });

    it('should return false for failed connection', async () => {
      mockOpenAI.chat.completions.create.mockRejectedValue(new Error('Connection failed'));

      const result = await provider.testConnection();
      expect(result).toBe(false);
    });
  });

  describe('getStatus', () => {
    it('should return provider status', () => {
      const status = provider.getStatus();

      expect(status).toEqual({
        provider: 'openai',
        model: 'gpt-3.5-turbo',
        circuitBreakerOpen: false,
        failureCount: 0,
        lastRequestTime: 0,
        requestsPerMinute: 60
      });
    });
  });
});