const LLMService = require('../../../lib/llm/index');

// Mock the providers
jest.mock('../../../lib/llm/providers/openai');
jest.mock('../../../lib/llm/providers/fallback');

const OpenAIProvider = require('../../../lib/llm/providers/openai');
const FallbackProvider = require('../../../lib/llm/providers/fallback');

describe('LLMService', () => {
  let service;
  let mockOpenAIProvider;
  let mockFallbackProvider;

  beforeEach(() => {
    // Reset environment variables
    process.env.OPENAI_API_KEY = 'test-api-key';
    process.env.OPENAI_MODEL = 'gpt-3.5-turbo';
    process.env.OPENAI_MAX_TOKENS = '1000';
    process.env.OPENAI_DAILY_BUDGET = '50';

    // Create mock providers
    mockOpenAIProvider = {
      testConnection: jest.fn().mockResolvedValue(true),
      generateCompletion: jest.fn().mockResolvedValue({
        content: 'Test OpenAI response',
        usage: { promptTokens: 50, completionTokens: 25, totalTokens: 75, cost: 0.01 },
        model: 'gpt-3.5-turbo',
        finishReason: 'stop'
      }),
      getStatus: jest.fn().mockReturnValue({
        provider: 'openai',
        model: 'gpt-3.5-turbo',
        circuitBreakerOpen: false
      })
    };

    mockFallbackProvider = {
      testConnection: jest.fn().mockResolvedValue(true),
      generateCompletion: jest.fn().mockResolvedValue({
        content: 'Test fallback response',
        usage: { promptTokens: 0, completionTokens: 20, totalTokens: 20, cost: 0 },
        model: 'fallback',
        finishReason: 'stop'
      }),
      getStatus: jest.fn().mockReturnValue({
        provider: 'fallback',
        model: 'template-based',
        available: true
      })
    };

    OpenAIProvider.mockImplementation(() => mockOpenAIProvider);
    FallbackProvider.mockImplementation(() => mockFallbackProvider);

    service = new LLMService();
  });

  afterEach(() => {
    jest.clearAllMocks();
    delete process.env.OPENAI_API_KEY;
    delete process.env.OPENAI_MODEL;
    delete process.env.OPENAI_MAX_TOKENS;
    delete process.env.OPENAI_DAILY_BUDGET;
  });

  describe('constructor', () => {
    it('should initialize with default options', () => {
      const service = new LLMService();

      expect(service.options.provider).toBe('openai');
      expect(service.options.model).toBe('gpt-3.5-turbo');
      expect(service.options.maxTokens).toBe(1000);
      expect(service.options.dailyBudget).toBe(50);
    });

    it('should use environment variables', () => {
      process.env.OPENAI_MODEL = 'gpt-4';
      process.env.OPENAI_MAX_TOKENS = '2000';
      process.env.OPENAI_DAILY_BUDGET = '100';

      const service = new LLMService();

      expect(service.options.model).toBe('gpt-4');
      expect(service.options.maxTokens).toBe(2000);
      expect(service.options.dailyBudget).toBe(100);
    });
  });

  describe('initialize', () => {
    it('should initialize successfully with OpenAI', async () => {
      await service.initialize();

      expect(service.isInitialized).toBe(true);
      expect(service.primaryProvider).toEqual(mockOpenAIProvider);
      expect(service.fallbackProvider).toEqual(mockFallbackProvider);
      expect(mockOpenAIProvider.testConnection).toHaveBeenCalled();
    });

    it('should fallback when OpenAI connection fails', async () => {
      mockOpenAIProvider.testConnection.mockResolvedValue(false);

      await service.initialize();

      expect(service.isInitialized).toBe(true);
      expect(service.primaryProvider).toBeNull();
      expect(service.fallbackProvider).toEqual(mockFallbackProvider);
    });

    it('should handle initialization errors gracefully', async () => {
      OpenAIProvider.mockImplementation(() => {
        throw new Error('API key invalid');
      });

      await service.initialize();

      expect(service.isInitialized).toBe(true);
      expect(service.primaryProvider).toBeNull();
      expect(service.fallbackProvider).toEqual(mockFallbackProvider);
    });

    it('should throw error if fallback is disabled and OpenAI fails', async () => {
      const service = new LLMService({ fallbackEnabled: false });

      // Mock the constructor to throw an error
      const originalImplementation = OpenAIProvider.getMockImplementation();
      OpenAIProvider.mockImplementation(() => {
        throw new Error('API key invalid');
      });

      await expect(service.initialize()).rejects.toThrow('API key invalid');

      // Restore the original implementation
      OpenAIProvider.mockImplementation(originalImplementation);
    });
  });

  describe('generatePredictionReport', () => {
    beforeEach(async () => {
      await service.initialize();
    });

    it('should generate prediction report using OpenAI', async () => {
      const predictions = [
        { number: '12', confidence: 75, method: 'lstm' },
        { number: '34', confidence: 68, method: 'statistical' }
      ];
      const historicalData = [
        { date: '2024-01-01', numbers: { lo: ['12', '34'], de: ['56'] } }
      ];

      const result = await service.generatePredictionReport(predictions, historicalData, 'lo');

      expect(result).toContain('Báo Cáo Dự Đoán');
      expect(mockOpenAIProvider.generateCompletion).toHaveBeenCalled();
      expect(service.dailyUsage).toBe(0.01);
    });

    it('should use fallback when budget exceeded', async () => {
      service.dailyUsage = service.options.dailyBudget + 1;

      const predictions = [{ number: '12', confidence: 75 }];
      const result = await service.generatePredictionReport(predictions, [], 'lo');

      expect(result).toContain('Báo Cáo Dự Đoán');
      expect(mockOpenAIProvider.generateCompletion).not.toHaveBeenCalled();
    });

    it('should use fallback when OpenAI fails', async () => {
      mockOpenAIProvider.generateCompletion.mockRejectedValue(new Error('API Error'));

      const predictions = [{ number: '12', confidence: 75 }];
      const result = await service.generatePredictionReport(predictions, [], 'lo');

      expect(result).toContain('Báo Cáo Dự Đoán');
    });
  });

  describe('generateTrendAnalysis', () => {
    beforeEach(async () => {
      await service.initialize();
    });

    it('should generate trend analysis using OpenAI', async () => {
      const trends = {
        hot: [{ number: '12', frequency: 5 }],
        cold: [{ number: '34', frequency: 1 }]
      };

      const result = await service.generateTrendAnalysis(trends, 'lo', '30 ngày');

      expect(result).toContain('Phân Tích Xu Hướng');
      expect(mockOpenAIProvider.generateCompletion).toHaveBeenCalled();
    });

    it('should handle empty trends data', async () => {
      const trends = { hot: [], cold: [] };

      const result = await service.generateTrendAnalysis(trends, 'lo');

      expect(result).toContain('Phân Tích Xu Hướng');
    });
  });

  describe('generateNumberAnalysis', () => {
    beforeEach(async () => {
      await service.initialize();
    });

    it('should generate number analysis using OpenAI', async () => {
      const frequency = { frequency: 5, period: 30, lastSeen: '2024-01-01' };
      const patterns = [{ type: 'sequence', confidence: 80 }];
      const correlations = { '34': { strength: 75, frequency: 3 } };

      const result = await service.generateNumberAnalysis('12', frequency, patterns, correlations);

      expect(result).toContain('Phân Tích Số 12');
      expect(mockOpenAIProvider.generateCompletion).toHaveBeenCalled();
    });
  });

  describe('generateWeeklyReport', () => {
    beforeEach(async () => {
      await service.initialize();
    });

    it('should generate weekly report using OpenAI', async () => {
      const weeklyData = [
        {
          startDate: '2024-01-01',
          endDate: '2024-01-07',
          topNumbers: [{ number: '12', frequency: 3 }],
          totalDraws: 7,
          trend: 'increasing'
        }
      ];

      const result = await service.generateWeeklyReport(weeklyData, 'lo');

      expect(result).toContain('Phân Tích Xu Hướng');
      expect(mockOpenAIProvider.generateCompletion).toHaveBeenCalled();
    });
  });

  describe('generatePredictionExplanation', () => {
    beforeEach(async () => {
      await service.initialize();
    });

    it('should generate prediction explanation using OpenAI', async () => {
      const prediction = { number: '12', confidence: 75, method: 'lstm' };
      const supportingData = { frequency: 5, lastSeen: '2024-01-01', trend: 'increasing' };

      const result = await service.generatePredictionExplanation(prediction, supportingData);

      expect(result).toContain('Báo Cáo Dự Đoán');
      expect(mockOpenAIProvider.generateCompletion).toHaveBeenCalledWith(
        expect.any(String),
        { maxTokens: 200 }
      );
    });

    it('should provide simple explanation when budget exceeded', async () => {
      service.dailyUsage = service.options.dailyBudget + 1;

      const prediction = { number: '12', confidence: 75, method: 'lstm' };
      const result = await service.generatePredictionExplanation(prediction, {});

      expect(result).toContain('Số 12 được dự đoán với độ tin cậy 75%');
      expect(mockOpenAIProvider.generateCompletion).not.toHaveBeenCalled();
    });
  });

  describe('budget management', () => {
    it('should check budget correctly', () => {
      expect(service.checkBudget()).toBe(true);

      service.dailyUsage = service.options.dailyBudget + 1;
      expect(service.checkBudget()).toBe(false);
    });

    it('should reset budget daily', () => {
      service.dailyUsage = 10;
      service.lastResetDate = '2024-01-01';

      // Simulate next day
      const today = new Date().toDateString();
      expect(service.checkBudget()).toBe(true);
      expect(service.dailyUsage).toBe(0);
      expect(service.lastResetDate).toBe(today);
    });

    it('should track usage correctly', () => {
      const usage = { cost: 0.05 };
      service.trackUsage(usage);

      expect(service.dailyUsage).toBe(0.05);
    });
  });

  describe('getUsageStats', () => {
    beforeEach(async () => {
      await service.initialize();
    });

    it('should return comprehensive usage stats', () => {
      service.dailyUsage = 10;

      const stats = service.getUsageStats();

      expect(stats).toEqual({
        dailyUsage: 10,
        dailyBudget: 50,
        lastReset: expect.any(String),
        budgetRemaining: 40,
        primaryProvider: expect.any(Object),
        fallbackProvider: expect.any(Object),
        isInitialized: true
      });
    });
  });

  describe('testService', () => {
    it('should test service functionality', async () => {
      await service.initialize();

      const results = await service.testService();

      expect(results.initialized).toBe(true);
      expect(results.canGenerateReports).toBe(true);
      expect(results.fallbackProvider).toBe(true);

      expect(mockOpenAIProvider.testConnection).toHaveBeenCalled();
      expect(mockFallbackProvider.testConnection).toHaveBeenCalled();
    });

    it('should handle test errors', async () => {
      mockOpenAIProvider.generateCompletion.mockRejectedValue(new Error('Test error'));
      await service.initialize();

      const results = await service.testService();

      // The service should still work with fallback, so no error expected
      expect(results.canGenerateReports).toBe(true);
    });
  });

  describe('utility methods', () => {
    it('should reset daily usage', () => {
      service.dailyUsage = 10;
      service.resetDailyUsage();

      expect(service.dailyUsage).toBe(0);
      expect(service.lastResetDate).toBe(new Date().toDateString());
    });

    it('should set daily budget', () => {
      service.setDailyBudget(100);
      expect(service.options.dailyBudget).toBe(100);
    });
  });
});