const FallbackProvider = require('../../../lib/llm/providers/fallback');

describe('FallbackProvider', () => {
  let provider;

  beforeEach(() => {
    provider = new FallbackProvider();
  });

  describe('generateCompletion', () => {
    it('should generate fallback completion', async () => {
      const prompt = 'Dự đoán số lô: 12, 34, 56';
      const result = await provider.generateCompletion(prompt);

      expect(result).toHaveProperty('content');
      expect(result).toHaveProperty('usage');
      expect(result).toHaveProperty('model', 'fallback');
      expect(result).toHaveProperty('finishReason', 'stop');

      expect(result.content).toContain('Báo Cáo Dự Đoán');
      expect(result.usage.cost).toBe(0);
    });

    it('should simulate API delay', async () => {
      const startTime = Date.now();
      await provider.generateCompletion('Test prompt');
      const endTime = Date.now();

      expect(endTime - startTime).toBeGreaterThanOrEqual(100);
    });
  });

  describe('detectPromptType', () => {
    it('should detect prediction prompts', () => {
      expect(provider.detectPromptType('Dự đoán số lô')).toBe('prediction');
      expect(provider.detectPromptType('prediction analysis')).toBe('prediction');
    });

    it('should detect trend prompts', () => {
      expect(provider.detectPromptType('Xu hướng số nóng')).toBe('trend');
      expect(provider.detectPromptType('trend analysis hot cold')).toBe('trend');
      expect(provider.detectPromptType('số nóng và lạnh')).toBe('trend');
    });

    it('should detect number analysis prompts', () => {
      expect(provider.detectPromptType('Phân tích số 12')).toBe('number');
      expect(provider.detectPromptType('number analysis')).toBe('number');
    });

    it('should default to prediction for unknown types', () => {
      expect(provider.detectPromptType('unknown prompt')).toBe('prediction');
    });
  });

  describe('extractDataFromPrompt', () => {
    it('should extract numbers from prompt', () => {
      const prompt = 'Dự đoán: 12, 34, 56, 78, 90';
      const data = provider.extractDataFromPrompt(prompt);

      expect(data.numbers).toBe('12, 34, 56, 78, 90');
      expect(data.hotNumbers).toBe('12, 34');
      expect(data.coldNumbers).toBe('78, 90');
    });

    it('should detect đề type', () => {
      const prompt = 'Dự đoán số đề: 12, 34';
      const data = provider.extractDataFromPrompt(prompt);

      expect(data.type).toBe('đề');
    });

    it('should default to lô type', () => {
      const prompt = 'Dự đoán số: 12, 34';
      const data = provider.extractDataFromPrompt(prompt);

      expect(data.type).toBe('lô');
    });
  });

  describe('template selection', () => {
    it('should have prediction templates', () => {
      const templates = provider.templates.prediction;
      expect(templates).toBeInstanceOf(Array);
      expect(templates.length).toBeGreaterThan(0);
      expect(templates[0]).toContain('Báo Cáo Dự Đoán');
    });

    it('should have trend templates', () => {
      const templates = provider.templates.trend;
      expect(templates).toBeInstanceOf(Array);
      expect(templates.length).toBeGreaterThan(0);
      expect(templates[0]).toContain('Xu Hướng');
    });

    it('should have number analysis templates', () => {
      const templates = provider.templates.number;
      expect(templates).toBeInstanceOf(Array);
      expect(templates.length).toBeGreaterThan(0);
      expect(templates[0]).toContain('Phân Tích Chi Tiết');
    });
  });

  describe('fillTemplate', () => {
    it('should replace placeholders in template', () => {
      const template = 'Số {numbers} có độ tin cậy {confidence}%';
      const prompt = 'Dự đoán: 12, 34';

      const result = provider.fillTemplate(template, prompt);

      expect(result).toContain('12, 34');
      expect(result).toContain('75');
    });
  });

  describe('getStatus', () => {
    it('should return fallback provider status', () => {
      const status = provider.getStatus();

      expect(status).toEqual({
        provider: 'fallback',
        model: 'template-based',
        circuitBreakerOpen: false,
        failureCount: 0,
        available: true
      });
    });
  });

  describe('testConnection', () => {
    it('should always return true', async () => {
      const result = await provider.testConnection();
      expect(result).toBe(true);
    });
  });
});