const models = require('../../../lib/models');

describe('Models Index', () => {
  test('should export all models', () => {
    expect(models.User).toBeDefined();
    expect(models.LotteryResult).toBeDefined();
    expect(models.Prediction).toBeDefined();
    expect(models.Group).toBeDefined();
    expect(models.Analytics).toBeDefined();
    expect(models.SystemLog).toBeDefined();
  });

  test('should export constructor functions', () => {
    expect(typeof models.User).toBe('function');
    expect(typeof models.LotteryResult).toBe('function');
    expect(typeof models.Prediction).toBe('function');
    expect(typeof models.Group).toBe('function');
    expect(typeof models.Analytics).toBe('function');
    expect(typeof models.SystemLog).toBe('function');
  });
});