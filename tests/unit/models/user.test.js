const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');

// Mock the mongo connections module to use test database
jest.mock('../../../lib/connections/mongo', () => {
  return () => mongoose; // Return the main mongoose instance for tests
});

const User = require('../../../lib/models/user');

describe('User Model', () => {
  let mongoServer;

  beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    const mongoUri = mongoServer.getUri();
    await mongoose.connect(mongoUri);
  });

  afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
  });

  beforeEach(async () => {
    await User.deleteMany({});
  });

  describe('Schema Validation', () => {
    test('should create a valid user', async () => {
      const validUser = {
        telegramId: 123456789,
        firstName: '<PERSON>',
        lastName: 'Doe',
        username: 'johndoe'
      };

      const user = new User(validUser);
      const savedUser = await user.save();

      expect(savedUser._id).toBeDefined();
      expect(savedUser.telegramId).toBe(123456789);
      expect(savedUser.firstName).toBe('John');
      expect(savedUser.preferences.notifications).toBe(true); // default
      expect(savedUser.statistics.totalQueries).toBe(0); // default
    });

    test('should require telegramId and firstName', async () => {
      const invalidUser = {
        lastName: 'Doe'
      };

      const user = new User(invalidUser);
      await expect(user.save()).rejects.toThrow();
    });

    test('should enforce unique telegramId', async () => {
      const user1 = new User({
        telegramId: 123456789,
        firstName: 'John'
      });

      const user2 = new User({
        telegramId: 123456789,
        firstName: 'Jane'
      });

      await user1.save();
      await expect(user2.save()).rejects.toThrow();
    });

    test('should validate favorite numbers format', async () => {
      const invalidUser = {
        telegramId: 123456789,
        firstName: 'John',
        preferences: {
          favoriteNumbers: ['1234'] // Invalid: should be 2-3 digits
        }
      };

      const user = new User(invalidUser);
      await expect(user.save()).rejects.toThrow('Favorite numbers must be 2-3 digits');
    });

    test('should validate language enum', async () => {
      const invalidUser = {
        telegramId: 123456789,
        firstName: 'John',
        preferences: {
          language: 'fr' // Invalid: not in enum
        }
      };

      const user = new User(invalidUser);
      await expect(user.save()).rejects.toThrow();
    });

    test('should set default values correctly', async () => {
      const user = new User({
        telegramId: 123456789,
        firstName: 'John'
      });

      await user.save();

      expect(user.preferences.notifications).toBe(true);
      expect(user.preferences.timezone).toBe('Asia/Ho_Chi_Minh');
      expect(user.preferences.language).toBe('vi');
      expect(user.statistics.totalQueries).toBe(0);
      expect(user.isActive).toBe(true);
      expect(user.isBlocked).toBe(false);
    });
  });

  describe('Instance Methods', () => {
    let user;

    beforeEach(async () => {
      user = new User({
        telegramId: 123456789,
        firstName: 'John',
        lastName: 'Doe'
      });
      await user.save();
    });

    test('should increment command usage', async () => {
      user.incrementCommand('dukienlo');

      expect(user.statistics.commandUsage.dukienlo).toBe(1);
      expect(user.statistics.totalQueries).toBe(1);
      expect(user.statistics.lastActive).toBeInstanceOf(Date);
    });

    test('should not increment invalid command', async () => {
      const originalQueries = user.statistics.totalQueries;
      user.incrementCommand('invalidcommand');

      expect(user.statistics.totalQueries).toBe(originalQueries);
    });

    test('should add favorite number', async () => {
      user.addFavoriteNumber('12');
      user.addFavoriteNumber('34');

      expect(user.preferences.favoriteNumbers).toContain('12');
      expect(user.preferences.favoriteNumbers).toContain('34');
      expect(user.preferences.favoriteNumbers).toHaveLength(2);
    });

    test('should not add duplicate favorite number', async () => {
      user.addFavoriteNumber('12');
      user.addFavoriteNumber('12');

      expect(user.preferences.favoriteNumbers).toHaveLength(1);
    });

    test('should track queried numbers', async () => {
      user.trackQueriedNumber('12');
      user.trackQueriedNumber('34');
      user.trackQueriedNumber('12'); // Increment existing

      expect(user.statistics.mostQueriedNumbers).toHaveLength(2);

      const number12 = user.statistics.mostQueriedNumbers.find(n => n.number === '12');
      const number34 = user.statistics.mostQueriedNumbers.find(n => n.number === '34');

      expect(number12.count).toBe(2);
      expect(number34.count).toBe(1);
    });

    test('should limit most queried numbers to 10', async () => {
      // Add 15 different numbers
      for (let i = 0; i < 15; i++) {
        user.trackQueriedNumber(i.toString().padStart(2, '0'));
      }

      expect(user.statistics.mostQueriedNumbers).toHaveLength(10);
    });

    test('should sort most queried numbers by count', async () => {
      user.trackQueriedNumber('12'); // count: 1
      user.trackQueriedNumber('34'); // count: 1
      user.trackQueriedNumber('34'); // count: 2
      user.trackQueriedNumber('34'); // count: 3

      expect(user.statistics.mostQueriedNumbers[0].number).toBe('34');
      expect(user.statistics.mostQueriedNumbers[0].count).toBe(3);
    });
  });

  describe('Static Methods', () => {
    beforeEach(async () => {
      const users = [
        {
          telegramId: 111,
          firstName: 'Active1',
          statistics: { lastActive: new Date() },
          isActive: true,
          isBlocked: false
        },
        {
          telegramId: 222,
          firstName: 'Active2',
          statistics: { lastActive: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000) }, // 2 days ago
          isActive: true,
          isBlocked: false
        },
        {
          telegramId: 333,
          firstName: 'Inactive',
          statistics: { lastActive: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000) }, // 10 days ago
          isActive: true,
          isBlocked: false
        },
        {
          telegramId: 444,
          firstName: 'Blocked',
          statistics: { lastActive: new Date() },
          isActive: true,
          isBlocked: true
        }
      ];

      await User.insertMany(users);
    });

    test('should get active users count for default 7 days', async () => {
      const count = await User.getActiveUsersCount();
      expect(count).toBe(2); // Only users 111 and 222
    });

    test('should get active users count for custom period', async () => {
      const count = await User.getActiveUsersCount(1); // 1 day
      expect(count).toBe(1); // Only user 111
    });
  });

  describe('Middleware', () => {
    test('should update updatedAt on save', async () => {
      const user = new User({
        telegramId: 123456789,
        firstName: 'John'
      });

      const originalUpdatedAt = user.updatedAt;

      // Wait a bit to ensure different timestamp
      await new Promise(resolve => setTimeout(resolve, 10));

      await user.save();

      expect(user.updatedAt.getTime()).toBeGreaterThan(originalUpdatedAt.getTime());
    });
  });

  describe('Indexes', () => {
    test('should have proper indexes', async () => {
      const indexes = await User.collection.getIndexes();

      expect(Object.keys(indexes)).toContain('telegramId_1');
      expect(Object.keys(indexes)).toContain('statistics.lastActive_-1');
      expect(Object.keys(indexes)).toContain('isActive_1_isBlocked_1');
    });
  });
});