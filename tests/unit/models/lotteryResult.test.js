const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');

// Mock the mongo connections module to use test database
jest.mock('../../../lib/connections/mongo', () => {
  return () => mongoose; // Return the main mongoose instance for tests
});

const LotteryResult = require('../../../lib/models/lotteryResult');

describe('LotteryResult Model', () => {
  let mongoServer;

  beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    const mongoUri = mongoServer.getUri();
    await mongoose.connect(mongoUri);
  });

  afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
  });

  beforeEach(async () => {
    await LotteryResult.deleteMany({});
  });

  describe('Schema Validation', () => {
    test('should create a valid lottery result', async () => {
      const validResult = {
        date: new Date('2024-01-15'),
        region: 'north',
        prizes: {
          special: '12345',
          first: '67890',
          second: ['11111', '22222'],
          third: ['33333', '44444', '55555'],
          fourth: ['1234', '5678'],
          fifth: ['9012', '3456'],
          sixth: ['123', '456'],
          seventh: ['78', '90']
        },
        numbers: {
          lo: ['45', '90', '11', '22'],
          de: '45' // Only last 2 digits of special prize
        }
      };

      const result = new LotteryResult(validResult);
      const savedResult = await result.save();

      expect(savedResult._id).toBeDefined();
      expect(savedResult.date).toEqual(validResult.date);
      expect(savedResult.region).toBe('north');
      expect(savedResult.prizes.special).toBe('12345');
    });

    test('should require date field', async () => {
      const invalidResult = {
        region: 'north',
        prizes: {
          special: '12345',
          first: '67890'
        }
      };

      const result = new LotteryResult(invalidResult);
      await expect(result.save()).rejects.toThrow('Path `date` is required');
    });

    test('should validate special prize format', async () => {
      const invalidResult = {
        date: new Date(),
        region: 'north',
        prizes: {
          special: '123', // Invalid: should be 5 digits
          first: '67890'
        }
      };

      const result = new LotteryResult(invalidResult);
      await expect(result.save()).rejects.toThrow('Special prize must be 5 digits');
    });

    test('should validate region enum', async () => {
      const invalidResult = {
        date: new Date(),
        region: 'invalid',
        prizes: {
          special: '12345',
          first: '67890'
        }
      };

      const result = new LotteryResult(invalidResult);
      await expect(result.save()).rejects.toThrow();
    });

    test('should validate lo numbers format', async () => {
      const invalidResult = {
        date: new Date(),
        region: 'north',
        prizes: {
          special: '12345',
          first: '67890'
        },
        numbers: {
          lo: ['123'], // Invalid: should be 2 digits
          de: ['123']
        }
      };

      const result = new LotteryResult(invalidResult);
      await expect(result.save()).rejects.toThrow('Lo number must be 2 digits');
    });

    test('should validate de numbers format', async () => {
      const invalidResult = {
        date: new Date(),
        region: 'north',
        prizes: {
          special: '12345',
          first: '67890'
        },
        numbers: {
          lo: ['12'],
          de: '123' // Invalid: should be 2 digits
        }
      };

      const result = new LotteryResult(invalidResult);
      await expect(result.save()).rejects.toThrow('De number must be 2 digits');
    });

    test('should enforce unique date-region combination', async () => {
      const result1 = new LotteryResult({
        date: new Date('2024-01-15'),
        region: 'north',
        prizes: {
          special: '12345',
          first: '67890'
        }
      });

      const result2 = new LotteryResult({
        date: new Date('2024-01-15'),
        region: 'north',
        prizes: {
          special: '54321',
          first: '09876'
        }
      });

      await result1.save();
      await expect(result2.save()).rejects.toThrow();
    });
  });

  describe('Instance Methods', () => {
    test('should extract lo numbers correctly', async () => {
      const result = new LotteryResult({
        date: new Date(),
        region: 'north',
        prizes: {
          special: '12345',
          first: '67890',
          second: ['11122'],
          third: ['33344'],
          fourth: ['5566'],
          fifth: ['7788'],
          sixth: ['999'],
          seventh: ['00', '11']
        }
      });

      const loNumbers = result.extractLoNumbers();

      expect(loNumbers).toContain('45'); // from special
      expect(loNumbers).toContain('90'); // from first
      expect(loNumbers).toContain('22'); // from second
      expect(loNumbers).toContain('44'); // from third
      expect(loNumbers).toContain('66'); // from fourth
      expect(loNumbers).toContain('88'); // from fifth
      expect(loNumbers).toContain('99'); // from sixth (last 2 digits)
      expect(loNumbers).toContain('00'); // from seventh
      expect(loNumbers).toContain('11'); // from seventh
    });

    test('should extract de numbers correctly', async () => {
      const result = new LotteryResult({
        date: new Date(),
        region: 'north',
        prizes: {
          special: '12345',
          first: '67890'
        }
      });

      const deNumber = result.extractDeNumber();

      expect(deNumber).toBe('45'); // Last 2 digits of special prize
    });

    test('should handle empty special prize for de extraction', async () => {
      const result = new LotteryResult({
        date: new Date(),
        region: 'north',
        prizes: {
          first: '67890'
        }
      });

      const deNumber = result.extractDeNumber();

      expect(deNumber).toBeNull();
    });
  });

  describe('Middleware', () => {
    test('should update updatedAt on save', async () => {
      const result = new LotteryResult({
        date: new Date(),
        region: 'north',
        prizes: {
          special: '12345',
          first: '67890'
        }
      });

      const originalUpdatedAt = result.updatedAt;

      // Wait a bit to ensure different timestamp
      await new Promise(resolve => setTimeout(resolve, 10));

      await result.save();

      expect(result.updatedAt.getTime()).toBeGreaterThan(originalUpdatedAt.getTime());
    });
  });

  describe('Indexes', () => {
    test('should have proper indexes', async () => {
      const indexes = await LotteryResult.collection.getIndexes();

      expect(Object.keys(indexes)).toContain('date_-1_region_1');
      expect(Object.keys(indexes)).toContain('numbers.lo_1');
      expect(Object.keys(indexes)).toContain('numbers.de_1');
      expect(Object.keys(indexes)).toContain('date_1_region_1');
    });
  });
});