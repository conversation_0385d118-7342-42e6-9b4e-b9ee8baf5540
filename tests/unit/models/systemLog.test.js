const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');

// Mock the mongo connections module to use test database
jest.mock('../../../lib/connections/mongo', () => {
  return () => mongoose; // Return the main mongoose instance for tests
});

const SystemLog = require('../../../lib/models/systemLog');

describe('SystemLog Model', () => {
  let mongoServer;

  beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    const mongoUri = mongoServer.getUri();
    await mongoose.connect(mongoUri);
  });

  afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
  });

  beforeEach(async () => {
    await SystemLog.deleteMany({});
  });

  describe('Schema Validation', () => {
    test('should create a valid system log', async () => {
      const validLog = {
        action: 'user_created',
        description: 'New user registered',
        level: 'info'
      };

      const log = new SystemLog(validLog);
      const savedLog = await log.save();

      expect(savedLog._id).toBeDefined();
      expect(savedLog.action).toBe('user_created');
      expect(savedLog.description).toBe('New user registered');
      expect(savedLog.level).toBe('info');
      expect(savedLog.createdAt).toBeInstanceOf(Date);
    });

    test('should require action and description', async () => {
      const invalidLog = {
        level: 'info'
      };

      const log = new SystemLog(invalidLog);
      await expect(log.save()).rejects.toThrow();
    });

    test('should validate action enum', async () => {
      const invalidLog = {
        action: 'invalid_action',
        description: 'Test description'
      };

      const log = new SystemLog(invalidLog);
      await expect(log.save()).rejects.toThrow();
    });

    test('should validate level enum', async () => {
      const invalidLog = {
        action: 'user_created',
        description: 'Test description',
        level: 'invalid_level'
      };

      const log = new SystemLog(invalidLog);
      await expect(log.save()).rejects.toThrow();
    });

    test('should validate description length', async () => {
      const invalidLog = {
        action: 'user_created',
        description: 'a'.repeat(1001) // Exceeds maxlength
      };

      const log = new SystemLog(invalidLog);
      await expect(log.save()).rejects.toThrow();
    });

    test('should set default values correctly', async () => {
      const log = new SystemLog({
        action: 'user_created',
        description: 'Test description'
      });

      await log.save();

      expect(log.level).toBe('info');
      expect(log.metadata.source).toBe('system');
      expect(log.createdAt).toBeInstanceOf(Date);
    });

    test('should store metadata correctly', async () => {
      const log = new SystemLog({
        action: 'user_created',
        description: 'Test description',
        metadata: {
          ip: '***********',
          userAgent: 'Test Agent',
          telegramId: 123456789,
          chatId: -987654321,
          source: 'bot'
        }
      });

      await log.save();

      expect(log.metadata.ip).toBe('***********');
      expect(log.metadata.userAgent).toBe('Test Agent');
      expect(log.metadata.telegramId).toBe(123456789);
      expect(log.metadata.chatId).toBe(-987654321);
      expect(log.metadata.source).toBe('bot');
    });
  });

  describe('Static Methods', () => {
    test('should log event with basic parameters', async () => {
      const log = await SystemLog.logEvent('user_created', 'New user registered');

      expect(log.action).toBe('user_created');
      expect(log.description).toBe('New user registered');
      expect(log.level).toBe('info');
      expect(log.metadata.source).toBe('system');
    });

    test('should log event with full options', async () => {
      const userId = new mongoose.Types.ObjectId();
      const options = {
        level: 'error',
        user: userId,
        data: { error: 'Test error' },
        updatedData: { status: 'failed' },
        ip: '***********',
        userAgent: 'Test Agent',
        telegramId: 123456789,
        chatId: -987654321,
        source: 'bot'
      };

      const log = await SystemLog.logEvent('error_occurred', 'Test error occurred', options);

      expect(log.action).toBe('error_occurred');
      expect(log.description).toBe('Test error occurred');
      expect(log.level).toBe('error');
      expect(log.user.toString()).toBe(userId.toString());
      expect(log.data.error).toBe('Test error');
      expect(log.updatedData.status).toBe('failed');
      expect(log.metadata.ip).toBe('***********');
      expect(log.metadata.source).toBe('bot');
    });

    test('should get logs with default sorting', async () => {
      // Create test logs
      await SystemLog.logEvent('user_created', 'User 1 created');
      await new Promise(resolve => setTimeout(resolve, 10)); // Ensure different timestamps
      await SystemLog.logEvent('user_updated', 'User 1 updated');
      await new Promise(resolve => setTimeout(resolve, 10));
      await SystemLog.logEvent('user_deleted', 'User 1 deleted');

      const logs = await SystemLog.getLogs();

      expect(logs).toHaveLength(3);
      expect(logs[0].action).toBe('user_deleted'); // Most recent first
      expect(logs[1].action).toBe('user_updated');
      expect(logs[2].action).toBe('user_created');
    });

    test('should get logs with criteria', async () => {
      await SystemLog.logEvent('user_created', 'User created', { level: 'info' });
      await SystemLog.logEvent('error_occurred', 'Error occurred', { level: 'error' });
      await SystemLog.logEvent('user_updated', 'User updated', { level: 'info' });

      const errorLogs = await SystemLog.getLogs({ level: 'error' });
      const userLogs = await SystemLog.getLogs({ action: { $in: ['user_created', 'user_updated'] } });

      expect(errorLogs).toHaveLength(1);
      expect(errorLogs[0].action).toBe('error_occurred');

      expect(userLogs).toHaveLength(2);
      expect(userLogs.every(log => ['user_created', 'user_updated'].includes(log.action))).toBe(true);
    });

    test('should get logs with limit', async () => {
      await SystemLog.logEvent('user_created', 'User 1 created');
      await SystemLog.logEvent('user_created', 'User 2 created');
      await SystemLog.logEvent('user_created', 'User 3 created');

      const logs = await SystemLog.getLogs({}, { limit: 2 });

      expect(logs).toHaveLength(2);
    });

    test('should clean old logs', async () => {
      const oldDate = new Date();
      oldDate.setDate(oldDate.getDate() - 35); // 35 days ago

      // Create old logs
      await SystemLog.create({
        action: 'user_created',
        description: 'Old user created',
        level: 'info',
        createdAt: oldDate
      });

      await SystemLog.create({
        action: 'error_occurred',
        description: 'Old error',
        level: 'error',
        createdAt: oldDate
      });

      // Create recent log
      await SystemLog.logEvent('user_created', 'Recent user created');

      const result = await SystemLog.cleanOldLogs(30);

      expect(result.deletedCount).toBe(1); // Only info log should be deleted, error log kept

      const remainingLogs = await SystemLog.find({});
      expect(remainingLogs).toHaveLength(2); // Error log + recent log
      expect(remainingLogs.some(log => log.level === 'error')).toBe(true);
    });
  });

  describe('Indexes', () => {
    test('should have proper indexes', async () => {
      const indexes = await SystemLog.collection.getIndexes();

      // Check that all expected indexes exist
      expect(Object.keys(indexes)).toContain('action_1_createdAt_-1');
      expect(Object.keys(indexes)).toContain('level_1_createdAt_-1');
      expect(Object.keys(indexes)).toContain('metadata.source_1_createdAt_-1');
      expect(Object.keys(indexes)).toContain('user_1_createdAt_-1');
    });
  });
});