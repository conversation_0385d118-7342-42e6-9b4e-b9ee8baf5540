const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');

// Mock the mongo connections module to use test database
jest.mock('../../../lib/connections/mongo', () => {
  return () => mongoose; // Return the main mongoose instance for tests
});

const Group = require('../../../lib/models/group');

describe('Group Model', () => {
  let mongoServer;

  beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    const mongoUri = mongoServer.getUri();
    await mongoose.connect(mongoUri);
  });

  afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
  });

  beforeEach(async () => {
    await Group.deleteMany({});
  });

  describe('Schema Validation', () => {
    test('should create a valid group', async () => {
      const validGroup = {
        telegramId: -123456789,
        title: 'Test Group',
        type: 'supergroup'
      };

      const group = new Group(validGroup);
      const savedGroup = await group.save();

      expect(savedGroup._id).toBeDefined();
      expect(savedGroup.telegramId).toBe(-123456789);
      expect(savedGroup.title).toBe('Test Group');
      expect(savedGroup.type).toBe('supergroup');
      expect(savedGroup.settings.dailyPredictions).toBe(false); // default
      expect(savedGroup.settings.language).toBe('vi'); // default
    });

    test('should require telegramId, title, and type', async () => {
      const invalidGroup = {
        title: 'Test Group'
      };

      const group = new Group(invalidGroup);
      await expect(group.save()).rejects.toThrow();
    });

    test('should enforce unique telegramId', async () => {
      const group1 = new Group({
        telegramId: -123456789,
        title: 'Group 1',
        type: 'group'
      });

      const group2 = new Group({
        telegramId: -123456789,
        title: 'Group 2',
        type: 'supergroup'
      });

      await group1.save();
      await expect(group2.save()).rejects.toThrow();
    });

    test('should validate type enum', async () => {
      const invalidGroup = {
        telegramId: -123456789,
        title: 'Test Group',
        type: 'invalid'
      };

      const group = new Group(invalidGroup);
      await expect(group.save()).rejects.toThrow();
    });

    test('should validate prediction time format', async () => {
      const invalidGroup = {
        telegramId: -123456789,
        title: 'Test Group',
        type: 'group',
        settings: {
          predictionTime: '25:00' // Invalid time
        }
      };

      const group = new Group(invalidGroup);
      await expect(group.save()).rejects.toThrow('Prediction time must be in HH:MM format');
    });

    test('should validate weekly report day range', async () => {
      const invalidGroup = {
        telegramId: -123456789,
        title: 'Test Group',
        type: 'group',
        settings: {
          weeklyReportDay: 7 // Invalid: should be 0-6
        }
      };

      const group = new Group(invalidGroup);
      await expect(group.save()).rejects.toThrow();
    });

    test('should validate language enum', async () => {
      const invalidGroup = {
        telegramId: -123456789,
        title: 'Test Group',
        type: 'group',
        settings: {
          language: 'fr' // Invalid: not in enum
        }
      };

      const group = new Group(invalidGroup);
      await expect(group.save()).rejects.toThrow();
    });

    test('should set default values correctly', async () => {
      const group = new Group({
        telegramId: -123456789,
        title: 'Test Group',
        type: 'group'
      });

      await group.save();

      expect(group.settings.dailyPredictions).toBe(false);
      expect(group.settings.predictionTime).toBe('08:00');
      expect(group.settings.weeklyReports).toBe(false);
      expect(group.settings.language).toBe('vi');
      expect(group.settings.maxPredictionsPerMessage).toBe(5);
      expect(group.statistics.memberCount).toBe(0);
      expect(group.isActive).toBe(true);
      expect(group.isBlocked).toBe(false);
    });
  });

  describe('Instance Methods', () => {
    let group;

    beforeEach(async () => {
      group = new Group({
        telegramId: -123456789,
        title: 'Test Group',
        type: 'supergroup'
      });
      await group.save();
    });

    test('should increment command usage', async () => {
      group.incrementCommand('dukienlo');

      expect(group.statistics.commandUsage.dukienlo).toBe(1);
      expect(group.statistics.totalMessages).toBe(1);
      expect(group.statistics.lastActivity).toBeInstanceOf(Date);
    });

    test('should not increment invalid command', async () => {
      const originalMessages = group.statistics.totalMessages;
      group.incrementCommand('invalidcommand');

      expect(group.statistics.totalMessages).toBe(originalMessages);
    });

    test('should add admin', async () => {
      group.addAdmin(111, 'admin1', 'Admin One');
      group.addAdmin(222, 'admin2', 'Admin Two');

      expect(group.admins).toHaveLength(2);
      expect(group.admins[0].telegramId).toBe(111);
      expect(group.admins[0].username).toBe('admin1');
      expect(group.admins[0].firstName).toBe('Admin One');
      expect(group.admins[0].addedAt).toBeInstanceOf(Date);
    });

    test('should not add duplicate admin', async () => {
      group.addAdmin(111, 'admin1', 'Admin One');
      group.addAdmin(111, 'admin1', 'Admin One');

      expect(group.admins).toHaveLength(1);
    });

    test('should remove admin', async () => {
      group.addAdmin(111, 'admin1', 'Admin One');
      group.addAdmin(222, 'admin2', 'Admin Two');

      group.removeAdmin(111);

      expect(group.admins).toHaveLength(1);
      expect(group.admins[0].telegramId).toBe(222);
    });

    test('should check if user is admin', async () => {
      group.addAdmin(111, 'admin1', 'Admin One');

      expect(group.isAdmin(111)).toBe(true);
      expect(group.isAdmin(222)).toBe(false);
    });

    test('should update member count', async () => {
      group.updateMemberCount(50);

      expect(group.statistics.memberCount).toBe(50);
      expect(group.statistics.lastActivity).toBeInstanceOf(Date);
    });
  });

  describe('Static Methods', () => {
    beforeEach(async () => {
      const groups = [
        {
          telegramId: -111,
          title: 'Daily Group',
          type: 'group',
          settings: { dailyPredictions: true },
          isActive: true,
          isBlocked: false
        },
        {
          telegramId: -222,
          title: 'Weekly Group',
          type: 'supergroup',
          settings: {
            weeklyReports: true,
            weeklyReportDay: 1 // Monday
          },
          isActive: true,
          isBlocked: false
        },
        {
          telegramId: -333,
          title: 'Inactive Group',
          type: 'group',
          settings: { dailyPredictions: true },
          isActive: false,
          isBlocked: false
        },
        {
          telegramId: -444,
          title: 'Blocked Group',
          type: 'group',
          settings: { dailyPredictions: true },
          isActive: true,
          isBlocked: true
        }
      ];

      await Group.insertMany(groups);
    });

    test('should get groups with daily predictions enabled', async () => {
      const groups = await Group.getGroupsWithDailyPredictions();

      expect(groups).toHaveLength(1);
      expect(groups[0].telegramId).toBe(-111);
    });

    test('should get groups for weekly reports', async () => {
      const groups = await Group.getGroupsForWeeklyReports(1); // Monday

      expect(groups).toHaveLength(1);
      expect(groups[0].telegramId).toBe(-222);
    });

    test('should not return inactive or blocked groups', async () => {
      const dailyGroups = await Group.getGroupsWithDailyPredictions();
      const weeklyGroups = await Group.getGroupsForWeeklyReports(1);

      expect(dailyGroups.every(g => g.isActive && !g.isBlocked)).toBe(true);
      expect(weeklyGroups.every(g => g.isActive && !g.isBlocked)).toBe(true);
    });
  });

  describe('Middleware', () => {
    test('should update updatedAt on save', async () => {
      const group = new Group({
        telegramId: -123456789,
        title: 'Test Group',
        type: 'group'
      });

      const originalUpdatedAt = group.updatedAt;

      // Wait a bit to ensure different timestamp
      await new Promise(resolve => setTimeout(resolve, 10));

      await group.save();

      expect(group.updatedAt.getTime()).toBeGreaterThan(originalUpdatedAt.getTime());
    });
  });

  describe('Indexes', () => {
    test('should have proper indexes', async () => {
      const indexes = await Group.collection.getIndexes();

      expect(Object.keys(indexes)).toContain('telegramId_1');
      expect(Object.keys(indexes)).toContain('type_1_isActive_1');
      expect(Object.keys(indexes)).toContain('statistics.lastActivity_-1');
      expect(Object.keys(indexes)).toContain('settings.dailyPredictions_1_isActive_1');
    });
  });
});