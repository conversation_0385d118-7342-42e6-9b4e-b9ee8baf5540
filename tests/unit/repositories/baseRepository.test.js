const BaseRepository = require('../../../lib/data/repositories/baseRepository');

// Mock collection methods
const mockCollection = {
  insertOne: jest.fn(),
  findOne: jest.fn(),
  find: jest.fn(() => ({
    project: jest.fn().mockReturnThis(),
    sort: jest.fn().mockReturnThis(),
    skip: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    toArray: jest.fn()
  })),
  updateOne: jest.fn(),
  updateMany: jest.fn(),
  deleteOne: jest.fn(),
  deleteMany: jest.fn(),
  countDocuments: jest.fn(),
  aggregate: jest.fn(() => ({
    toArray: jest.fn()
  })),
  createIndexes: jest.fn()
};

// Mock MongoDB database
const mockDb = {
  collection: jest.fn(() => mockCollection)
};

// Concrete implementation for testing
class TestRepository extends BaseRepository {
  constructor(database) {
    super(database, 'testCollection');
  }
}

describe('BaseRepository', () => {
  let repository;

  beforeEach(() => {
    jest.clearAllMocks();
    repository = new TestRepository(mockDb);
  });

  describe('constructor', () => {
    it('should throw error when instantiated directly', () => {
      expect(() => new BaseRepository(mockDb, 'test')).toThrow(
        'BaseRepository is an abstract class and cannot be instantiated directly'
      );
    });

    it('should set database and collection name', () => {
      expect(repository.db).toBe(mockDb);
      expect(repository.collection).toBe('testCollection');
    });
  });

  describe('create', () => {
    it('should create document with timestamps', async () => {
      const testData = { name: 'test', value: 123 };
      const mockResult = { insertedId: 'mock-id' };
      mockCollection.insertOne.mockResolvedValue(mockResult);

      const result = await repository.create(testData);

      expect(mockCollection.insertOne).toHaveBeenCalledWith({
        ...testData,
        createdAt: expect.any(Date),
        updatedAt: expect.any(Date)
      });
      expect(result).toBe('mock-id');
    });

    it('should handle creation errors', async () => {
      mockCollection.insertOne.mockRejectedValue(new Error('DB Error'));

      await expect(repository.create({})).rejects.toThrow(
        'Failed to create document in testCollection: DB Error'
      );
    });
  });

  describe('findById', () => {
    it('should find document by ObjectId', async () => {
      const mockDoc = { _id: 'mock-id', name: 'test' };
      mockCollection.findOne.mockResolvedValue(mockDoc);

      const result = await repository.findById('507f1f77bcf86cd799439011');

      expect(mockCollection.findOne).toHaveBeenCalledWith({
        _id: expect.any(Object)
      });
      expect(result).toBe(mockDoc);
    });

    it('should handle findById errors', async () => {
      mockCollection.findOne.mockRejectedValue(new Error('DB Error'));

      await expect(repository.findById('invalid-id')).rejects.toThrow(
        'Failed to find document by ID in testCollection'
      );
    });
  });

  describe('find', () => {
    it('should find documents with query and options', async () => {
      const mockDocs = [{ name: 'test1' }, { name: 'test2' }];
      const mockCursor = {
        project: jest.fn().mockReturnThis(),
        sort: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        toArray: jest.fn().mockResolvedValue(mockDocs)
      };
      mockCollection.find.mockReturnValue(mockCursor);

      const result = await repository.find(
        { active: true },
        { sort: { name: 1 }, limit: 10, skip: 5, projection: { name: 1 } }
      );

      expect(mockCollection.find).toHaveBeenCalledWith({ active: true });
      expect(mockCursor.project).toHaveBeenCalledWith({ name: 1 });
      expect(mockCursor.sort).toHaveBeenCalledWith({ name: 1 });
      expect(mockCursor.skip).toHaveBeenCalledWith(5);
      expect(mockCursor.limit).toHaveBeenCalledWith(10);
      expect(result).toBe(mockDocs);
    });

    it('should handle find errors', async () => {
      const mockCursor = {
        project: jest.fn().mockReturnThis(),
        sort: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        toArray: jest.fn().mockRejectedValue(new Error('DB Error'))
      };
      mockCollection.find.mockReturnValue(mockCursor);

      await expect(repository.find()).rejects.toThrow(
        'Failed to find documents in testCollection: DB Error'
      );
    });
  });

  describe('findOne', () => {
    it('should find one document', async () => {
      const mockDoc = { name: 'test' };
      mockCollection.findOne.mockResolvedValue(mockDoc);

      const result = await repository.findOne({ name: 'test' });

      expect(mockCollection.findOne).toHaveBeenCalledWith({ name: 'test' }, {});
      expect(result).toBe(mockDoc);
    });
  });

  describe('updateById', () => {
    it('should update document by ID with timestamp', async () => {
      const mockResult = { modifiedCount: 1 };
      mockCollection.updateOne.mockResolvedValue(mockResult);

      const result = await repository.updateById('507f1f77bcf86cd799439011', { name: 'updated' });

      expect(mockCollection.updateOne).toHaveBeenCalledWith(
        { _id: expect.any(Object) },
        {
          $set: {
            name: 'updated',
            updatedAt: expect.any(Date)
          }
        }
      );
      expect(result).toBe(mockResult);
    });
  });

  describe('update', () => {
    it('should update documents with upsert option', async () => {
      const mockResult = { modifiedCount: 1 };
      mockCollection.updateOne.mockResolvedValue(mockResult);

      const result = await repository.update(
        { name: 'test' },
        { value: 456 },
        { upsert: true }
      );

      expect(mockCollection.updateOne).toHaveBeenCalledWith(
        { name: 'test' },
        {
          $set: {
            value: 456,
            updatedAt: expect.any(Date)
          },
          $setOnInsert: { createdAt: expect.any(Date) }
        },
        { upsert: true }
      );
      expect(result).toBe(mockResult);
    });

    it('should use updateMany when multi option is true', async () => {
      const mockResult = { modifiedCount: 3 };
      mockCollection.updateMany.mockResolvedValue(mockResult);

      await repository.update({ active: true }, { status: 'updated' }, { multi: true });

      expect(mockCollection.updateMany).toHaveBeenCalled();
    });
  });

  describe('deleteById', () => {
    it('should delete document by ID', async () => {
      const mockResult = { deletedCount: 1 };
      mockCollection.deleteOne.mockResolvedValue(mockResult);

      const result = await repository.deleteById('507f1f77bcf86cd799439011');

      expect(mockCollection.deleteOne).toHaveBeenCalledWith({
        _id: expect.any(Object)
      });
      expect(result).toBe(mockResult);
    });
  });

  describe('delete', () => {
    it('should delete documents by query', async () => {
      const mockResult = { deletedCount: 3 };
      mockCollection.deleteMany.mockResolvedValue(mockResult);

      const result = await repository.delete({ active: false });

      expect(mockCollection.deleteMany).toHaveBeenCalledWith({ active: false });
      expect(result).toBe(mockResult);
    });
  });

  describe('count', () => {
    it('should count documents', async () => {
      mockCollection.countDocuments.mockResolvedValue(5);

      const result = await repository.count({ active: true });

      expect(mockCollection.countDocuments).toHaveBeenCalledWith({ active: true });
      expect(result).toBe(5);
    });
  });

  describe('exists', () => {
    it('should return true if document exists', async () => {
      mockCollection.countDocuments.mockResolvedValue(1);

      const result = await repository.exists({ name: 'test' });

      expect(mockCollection.countDocuments).toHaveBeenCalledWith(
        { name: 'test' },
        { limit: 1 }
      );
      expect(result).toBe(true);
    });

    it('should return false if document does not exist', async () => {
      mockCollection.countDocuments.mockResolvedValue(0);

      const result = await repository.exists({ name: 'nonexistent' });

      expect(result).toBe(false);
    });
  });

  describe('aggregate', () => {
    it('should perform aggregation', async () => {
      const mockResult = [{ count: 5 }];
      const mockCursor = {
        toArray: jest.fn().mockResolvedValue(mockResult)
      };
      mockCollection.aggregate.mockReturnValue(mockCursor);

      const pipeline = [{ $group: { _id: null, count: { $sum: 1 } } }];
      const result = await repository.aggregate(pipeline);

      expect(mockCollection.aggregate).toHaveBeenCalledWith(pipeline);
      expect(result).toBe(mockResult);
    });
  });

  describe('createIndexes', () => {
    it('should log message for base implementation', async () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      await repository.createIndexes();

      expect(consoleSpy).toHaveBeenCalledWith('No indexes defined for testCollection');
      consoleSpy.mockRestore();
    });
  });

  describe('validateData', () => {
    it('should return true by default', () => {
      const result = repository.validateData({ test: 'data' });
      expect(result).toBe(true);
    });
  });

  describe('transformData', () => {
    it('should return data unchanged by default', () => {
      const testData = { test: 'data' };
      const result = repository.transformData(testData);
      expect(result).toBe(testData);
    });
  });
});