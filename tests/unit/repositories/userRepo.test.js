const UserRepository = require('../../../lib/data/repositories/userRepo');

// Mock collection methods
const mockCollection = {
  insertOne: jest.fn(),
  findOne: jest.fn(),
  find: jest.fn(() => ({
    project: jest.fn().mockReturnThis(),
    sort: jest.fn().mockReturnThis(),
    skip: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    toArray: jest.fn(),
    count: jest.fn()
  })),
  updateOne: jest.fn(),
  updateMany: jest.fn(),
  deleteOne: jest.fn(),
  deleteMany: jest.fn(),
  countDocuments: jest.fn(),
  aggregate: jest.fn(() => ({
    toArray: jest.fn()
  })),
  createIndexes: jest.fn()
};

// Mock MongoDB database
const mockDb = {
  collection: jest.fn(() => mockCollection)
};

describe('UserRepository', () => {
  let repository;

  beforeEach(() => {
    jest.clearAllMocks();
    repository = new UserRepository(mockDb);
  });

  describe('constructor', () => {
    it('should extend BaseRepository with correct collection', () => {
      expect(repository.collection).toBe('users');
      expect(repository.db).toBe(mockDb);
    });
  });

  describe('create', () => {
    it('should create user with default preferences and statistics', async () => {
      const userData = {
        telegramId: 123456,
        username: 'testuser',
        firstName: 'Test'
      };
      const mockResult = { insertedId: 'mock-id' };
      mockCollection.insertOne.mockResolvedValue(mockResult);

      const result = await repository.create(userData);

      expect(mockCollection.insertOne).toHaveBeenCalledWith({
        ...userData,
        preferences: {
          notifications: true,
          favoriteNumbers: [],
          timezone: 'Asia/Ho_Chi_Minh'
        },
        statistics: {
          totalQueries: 0,
          lastActive: expect.any(Date),
          mostQueriedNumbers: []
        },
        createdAt: expect.any(Date),
        updatedAt: expect.any(Date)
      });
      expect(result).toBe('mock-id');
    });

    it('should merge provided preferences and statistics', async () => {
      const userData = {
        telegramId: 123456,
        firstName: 'Test',
        preferences: { notifications: false },
        statistics: { totalQueries: 5 }
      };
      mockCollection.insertOne.mockResolvedValue({ insertedId: 'mock-id' });

      await repository.create(userData);

      expect(mockCollection.insertOne).toHaveBeenCalledWith(
        expect.objectContaining({
          preferences: expect.objectContaining({
            notifications: false,
            favoriteNumbers: [],
            timezone: 'Asia/Ho_Chi_Minh'
          }),
          statistics: expect.objectContaining({
            totalQueries: 5,
            lastActive: expect.any(Date),
            mostQueriedNumbers: []
          })
        })
      );
    });

    it('should handle creation errors', async () => {
      mockCollection.insertOne.mockRejectedValue(new Error('DB Error'));

      await expect(repository.create({})).rejects.toThrow(
        'Failed to create user:'
      );
    });
  });

  describe('findByTelegramId', () => {
    it('should find user by telegram ID', async () => {
      const mockUser = { telegramId: 123456, firstName: 'Test' };
      mockCollection.findOne.mockResolvedValue(mockUser);

      const result = await repository.findByTelegramId(123456);

      expect(mockCollection.findOne).toHaveBeenCalledWith({ telegramId: 123456 }, {});
      expect(result).toBe(mockUser);
    });

    it('should handle find errors', async () => {
      mockCollection.findOne.mockRejectedValue(new Error('DB Error'));

      await expect(repository.findByTelegramId(123456)).rejects.toThrow(
        'Failed to find user:'
      );
    });
  });

  describe('updateStats', () => {
    it('should update user statistics with query increment', async () => {
      const mockResult = { modifiedCount: 1 };
      mockCollection.updateOne.mockResolvedValue(mockResult);

      const result = await repository.updateStats(123456, { queriedNumber: '12' });

      expect(mockCollection.updateOne).toHaveBeenCalledWith(
        { telegramId: 123456 },
        {
          $set: {
            'statistics.lastActive': expect.any(Date),
            updatedAt: expect.any(Date)
          },
          $inc: {
            'statistics.totalQueries': 1
          },
          $addToSet: {
            'statistics.mostQueriedNumbers': '12'
          }
        },
        { upsert: true }
      );
      expect(result).toBe(mockResult);
    });

    it('should update stats without queried number', async () => {
      mockCollection.updateOne.mockResolvedValue({ modifiedCount: 1 });

      await repository.updateStats(123456, {});

      expect(mockCollection.updateOne).toHaveBeenCalledWith(
        { telegramId: 123456 },
        {
          $set: {
            'statistics.lastActive': expect.any(Date),
            updatedAt: expect.any(Date)
          },
          $inc: {
            'statistics.totalQueries': 1
          }
        },
        { upsert: true }
      );
    });

    it('should handle update errors', async () => {
      mockCollection.updateOne.mockRejectedValue(new Error('DB Error'));

      await expect(repository.updateStats(123456, {})).rejects.toThrow(
        'Failed to update user stats: DB Error'
      );
    });
  });

  describe('updatePreferences', () => {
    it('should update user preferences', async () => {
      const preferences = { notifications: false, timezone: 'UTC' };
      const mockResult = { modifiedCount: 1 };
      mockCollection.updateOne.mockResolvedValue(mockResult);

      const result = await repository.updatePreferences(123456, preferences);

      expect(mockCollection.updateOne).toHaveBeenCalledWith(
        { telegramId: 123456 },
        {
          $set: {
            preferences,
            updatedAt: expect.any(Date)
          }
        }
      );
      expect(result).toBe(mockResult);
    });

    it('should handle preference update errors', async () => {
      mockCollection.updateOne.mockRejectedValue(new Error('DB Error'));

      await expect(repository.updatePreferences(123456, {})).rejects.toThrow(
        'Failed to update preferences: DB Error'
      );
    });
  });

  describe('getStats', () => {
    it('should get user statistics and preferences', async () => {
      const mockUser = {
        statistics: { totalQueries: 10 },
        preferences: { notifications: true }
      };
      mockCollection.findOne.mockResolvedValue(mockUser);

      const result = await repository.getStats(123456);

      expect(mockCollection.findOne).toHaveBeenCalledWith(
        { telegramId: 123456 },
        { projection: { statistics: 1, preferences: 1 } }
      );
      expect(result).toEqual({
        totalQueries: 10,
        preferences: { notifications: true }
      });
    });

    it('should return null if user not found', async () => {
      mockCollection.findOne.mockResolvedValue(null);

      const result = await repository.getStats(123456);

      expect(result).toBeNull();
    });

    it('should handle getStats errors', async () => {
      mockCollection.findOne.mockRejectedValue(new Error('DB Error'));

      await expect(repository.getStats(123456)).rejects.toThrow(
        'Failed to get user stats:'
      );
    });
  });

  describe('getActiveUsers', () => {
    it('should count active users within specified days', async () => {
      mockCollection.countDocuments.mockResolvedValue(25);

      const result = await repository.getActiveUsers(7);

      expect(mockCollection.countDocuments).toHaveBeenCalledWith({
        'statistics.lastActive': { $gte: expect.any(Date) }
      });
      expect(result).toBe(25);
    });

    it('should use default days parameter', async () => {
      mockCollection.countDocuments.mockResolvedValue(50);

      await repository.getActiveUsers();

      // Should use 30 days by default
      expect(mockCollection.countDocuments).toHaveBeenCalledWith({
        'statistics.lastActive': { $gte: expect.any(Date) }
      });
    });

    it('should handle active users count errors', async () => {
      mockCollection.countDocuments.mockRejectedValue(new Error('DB Error'));

      await expect(repository.getActiveUsers()).rejects.toThrow(
        'Failed to get active users:'
      );
    });
  });

  describe('getUserAnalytics', () => {
    it('should get user analytics', async () => {
      const mockAnalytics = {
        totalUsers: 100,
        avgQueries: 15.5,
        activeToday: 25
      };
      
      mockCollection.aggregate.mockReturnValue({
        toArray: jest.fn().mockResolvedValue([mockAnalytics])
      });

      const result = await repository.getUserAnalytics();

      expect(mockCollection.aggregate).toHaveBeenCalledWith([
        {
          $group: {
            _id: null,
            totalUsers: { $sum: 1 },
            avgQueries: { $avg: '$statistics.totalQueries' },
            activeToday: {
              $sum: {
                $cond: [
                  {
                    $gte: [
                      '$statistics.lastActive',
                      expect.any(Date)
                    ]
                  },
                  1,
                  0
                ]
              }
            }
          }
        }
      ]);
      expect(result).toBe(mockAnalytics);
    });

    it('should return default analytics if no results', async () => {
      mockCollection.aggregate.mockReturnValue({
        toArray: jest.fn().mockResolvedValue([])
      });

      const result = await repository.getUserAnalytics();

      expect(result).toEqual({
        totalUsers: 0,
        avgQueries: 0,
        activeToday: 0
      });
    });

    it('should handle analytics errors', async () => {
      mockCollection.aggregate.mockReturnValue({
        toArray: jest.fn().mockRejectedValue(new Error('DB Error'))
      });

      await expect(repository.getUserAnalytics()).rejects.toThrow(
        'Failed to get user analytics:'
      );
    });
  });

  describe('createIndexes', () => {
    it('should create database indexes', async () => {
      await repository.createIndexes();

      expect(mockCollection.createIndexes).toHaveBeenCalledWith([
        { key: { telegramId: 1 }, unique: true },
        { key: { 'statistics.lastActive': -1 } },
        { key: { createdAt: -1 } }
      ]);
    });

    it('should handle index creation errors', async () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      mockCollection.createIndexes.mockRejectedValue(new Error('Index Error'));

      await repository.createIndexes();

      expect(consoleSpy).toHaveBeenCalledWith(
        'Failed to create user indexes:',
        'Index Error'
      );
      consoleSpy.mockRestore();
    });
  });
});