const LotteryRepository = require('../../../lib/data/repositories/lotteryRepo');

// Mock collection methods
const mockCollection = {
  insertOne: jest.fn(),
  findOne: jest.fn(),
  find: jest.fn(() => ({
    sort: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    toArray: jest.fn()
  })),
  aggregate: jest.fn(() => ({
    toArray: jest.fn()
  })),
  createIndexes: jest.fn()
};

// Mock MongoDB database
const mockDb = {
  collection: jest.fn(() => mockCollection)
};

describe('LotteryRepository', () => {
  let repository;

  beforeEach(() => {
    jest.clearAllMocks();
    repository = new LotteryRepository(mockDb);
  });

  describe('constructor', () => {
    it('should extend BaseRepository with correct collection', () => {
      expect(repository.collection).toBe('lotteryResults');
      expect(repository.db).toBe(mockDb);
    });
  });

  describe('save', () => {
    it('should save lottery result with timestamps', async () => {
      const lotteryResult = {
        date: new Date('2024-01-15'),
        region: 'north',
        prizes: { special: '12345' },
        numbers: { lo: ['12', '34'], de: ['123'] }
      };
      const mockResult = { insertedId: 'mock-id' };
      mockCollection.insertOne.mockResolvedValue(mockResult);

      const result = await repository.save(lotteryResult);

      expect(mockCollection.insertOne).toHaveBeenCalledWith({
        ...lotteryResult,
        createdAt: expect.any(Date),
        updatedAt: expect.any(Date)
      });
      expect(result).toBe('mock-id');
    });

    it('should handle save errors', async () => {
      mockCollection.insertOne.mockRejectedValue(new Error('DB Error'));

      await expect(repository.save({})).rejects.toThrow(
        'Failed to save lottery result: DB Error'
      );
    });
  });

  describe('getHistory', () => {
    it('should get lottery history within date range', async () => {
      const mockResults = [
        { date: new Date('2024-01-15'), region: 'north' },
        { date: new Date('2024-01-14'), region: 'north' }
      ];
      const mockCursor = {
        sort: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        toArray: jest.fn().mockResolvedValue(mockResults)
      };
      mockCollection.find.mockReturnValue(mockCursor);

      const result = await repository.getHistory('2024-01-14', '2024-01-15', 'north');

      expect(mockCollection.find).toHaveBeenCalledWith({
        region: 'north',
        date: {
          $gte: new Date('2024-01-14'),
          $lte: new Date('2024-01-15')
        }
      });
      expect(mockCursor.sort).toHaveBeenCalledWith({ date: -1 });
      expect(result).toBe(mockResults);
    });

    it('should use default region if not provided', async () => {
      const mockCursor = {
        sort: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        toArray: jest.fn().mockResolvedValue([])
      };
      mockCollection.find.mockReturnValue(mockCursor);

      await repository.getHistory('2024-01-14', '2024-01-15');

      expect(mockCollection.find).toHaveBeenCalledWith({
        region: 'north',
        date: expect.any(Object)
      });
    });

    it('should handle getHistory errors', async () => {
      const mockCursor = {
        sort: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        toArray: jest.fn().mockRejectedValue(new Error('DB Error'))
      };
      mockCollection.find.mockReturnValue(mockCursor);

      await expect(repository.getHistory('2024-01-14', '2024-01-15')).rejects.toThrow(
        'Failed to get lottery history: DB Error'
      );
    });
  });

  describe('getLatest', () => {
    it('should get latest lottery results', async () => {
      const mockResults = [
        { date: new Date('2024-01-15'), region: 'north' }
      ];
      const mockCursor = mockCollection.find();
      mockCursor.toArray.mockResolvedValue(mockResults);

      const result = await repository.getLatest('north', 5);

      expect(mockCollection.find).toHaveBeenCalledWith({ region: 'north' });
      expect(mockCursor.sort).toHaveBeenCalledWith({ date: -1 });
      expect(mockCursor.limit).toHaveBeenCalledWith(5);
      expect(result).toBe(mockResults);
    });

    it('should use default parameters', async () => {
      const mockCursor = mockCollection.find();
      mockCursor.toArray.mockResolvedValue([]);

      await repository.getLatest();

      expect(mockCollection.find).toHaveBeenCalledWith({ region: 'north' });
      expect(mockCursor.limit).toHaveBeenCalledWith(10);
    });
  });

  describe('getByDate', () => {
    it('should get lottery result for specific date', async () => {
      const mockResult = { date: new Date('2024-01-15'), region: 'north' };
      mockCollection.findOne.mockResolvedValue(mockResult);

      const result = await repository.getByDate('2024-01-15', 'north');

      expect(mockCollection.findOne).toHaveBeenCalledWith({
        date: new Date('2024-01-15'),
        region: 'north'
      });
      expect(result).toBe(mockResult);
    });

    it('should use default region', async () => {
      mockCollection.findOne.mockResolvedValue(null);

      await repository.getByDate('2024-01-15');

      expect(mockCollection.findOne).toHaveBeenCalledWith({
        date: new Date('2024-01-15'),
        region: 'north'
      });
    });
  });

  describe('getNumberFrequency', () => {
    it('should get frequency of specific number', async () => {
      const mockResults = [
        { date: new Date('2024-01-15'), numbers: { lo: ['12'] } },
        { date: new Date('2024-01-14'), numbers: { lo: ['12'] } }
      ];
      const mockCursor = mockCollection.find();
      mockCursor.toArray.mockResolvedValue(mockResults);

      const result = await repository.getNumberFrequency('12', 'north', 30);

      expect(mockCollection.find).toHaveBeenCalledWith({
        region: 'north',
        date: { $gte: expect.any(Date) },
        $or: [
          { 'numbers.lo': '12' },
          { 'numbers.de': '12' }
        ]
      });

      expect(result).toEqual({
        number: '12',
        frequency: 2,
        period: 30,
        lastSeen: mockResults[0].date
      });
    });

    it('should handle no results', async () => {
      const mockCursor = mockCollection.find();
      mockCursor.toArray.mockResolvedValue([]);

      const result = await repository.getNumberFrequency('99');

      expect(result.frequency).toBe(0);
      expect(result.lastSeen).toBeNull();
    });
  });

  describe('getStatistics', () => {
    it('should get comprehensive statistics', async () => {
      const mockStats = [
        { _id: '12', frequency: 5, lastSeen: new Date('2024-01-15') },
        { _id: '34', frequency: 3, lastSeen: new Date('2024-01-14') }
      ];
      
      // Set up the mock to return the expected data
      mockCollection.aggregate.mockReturnValue({
        toArray: jest.fn().mockResolvedValue(mockStats)
      });

      const result = await repository.getStatistics('north', 100);

      expect(mockCollection.aggregate).toHaveBeenCalledWith([
        {
          $match: {
            region: 'north',
            date: { $gte: expect.any(Date) }
          }
        },
        { $unwind: '$numbers.lo' },
        {
          $group: {
            _id: '$numbers.lo',
            frequency: { $sum: 1 },
            lastSeen: { $max: '$date' }
          }
        },
        { $sort: { frequency: -1 } }
      ]);
      expect(result).toEqual(mockStats);
    });

    it('should use default parameters', async () => {
      mockCollection.aggregate.mockReturnValue({
        toArray: jest.fn().mockResolvedValue([])
      });

      await repository.getStatistics();

      expect(mockCollection.aggregate).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({
            $match: expect.objectContaining({
              region: 'north'
            })
          })
        ])
      );
    });
  });

  describe('createIndexes', () => {
    it('should create database indexes', async () => {
      await repository.createIndexes();

      expect(mockCollection.createIndexes).toHaveBeenCalledWith([
        { key: { date: -1, region: 1 } },
        { key: { region: 1, 'numbers.lo': 1 } },
        { key: { region: 1, 'numbers.de': 1 } },
        { key: { createdAt: -1 } }
      ]);
    });

    it('should handle index creation errors', async () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      mockCollection.createIndexes.mockRejectedValue(new Error('Index Error'));

      await repository.createIndexes();

      expect(consoleSpy).toHaveBeenCalledWith(
        'Failed to create indexes:',
        'Index Error'
      );
      consoleSpy.mockRestore();
    });
  });
});