const GroupRepository = require('../../../lib/data/repositories/groupRepo');

// Mock collection methods
const mockCollection = {
  insertOne: jest.fn(),
  findOne: jest.fn(),
  find: jest.fn(() => ({
    project: jest.fn().mockReturnThis(),
    sort: jest.fn().mockReturnThis(),
    skip: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    toArray: jest.fn()
  })),
  updateOne: jest.fn(),
  updateMany: jest.fn(),
  deleteOne: jest.fn(),
  deleteMany: jest.fn(),
  countDocuments: jest.fn(),
  aggregate: jest.fn(() => ({
    toArray: jest.fn()
  })),
  createIndexes: jest.fn()
};

// Mock MongoDB database
const mockDb = {
  collection: jest.fn(() => mockCollection)
};

describe('GroupRepository', () => {
  let repository;

  beforeEach(() => {
    jest.clearAllMocks();
    repository = new GroupRepository(mockDb);
  });

  describe('constructor', () => {
    it('should extend BaseRepository with correct collection', () => {
      expect(repository.collection).toBe('groups');
      expect(repository.db).toBe(mockDb);
    });
  });

  describe('createGroup', () => {
    it('should create group with default settings', async () => {
      const groupData = {
        telegramId: -123456,
        title: 'Test Group',
        type: 'group'
      };
      const mockResult = { insertedId: 'mock-id' };
      mockCollection.insertOne.mockResolvedValue(mockResult);

      const result = await repository.createGroup(groupData);

      expect(mockCollection.insertOne).toHaveBeenCalledWith({
        ...groupData,
        settings: {
          dailyPredictions: false,
          predictionTime: '08:00',
          weeklyReports: false,
          weeklyReportDay: 1,
          language: 'vi',
          timezone: 'Asia/Ho_Chi_Minh',
          enabledCommands: ['dukienlo', 'dukiende', 'lichsu', 'xuhuonglo', 'xuhuongde', 'number', 'help'],
          predictionTypes: ['lo', 'de'],
          maxPredictionsPerMessage: 5
        },
        statistics: {
          memberCount: 0,
          totalMessages: 0,
          lastActivity: expect.any(Date),
          commandUsage: {
            dukienlo: 0,
            dukiende: 0,
            lichsu: 0,
            xuhuonglo: 0,
            xuhuongde: 0,
            number: 0,
            help: 0
          },
          joinDate: expect.any(Date)
        },
        admins: [],
        isActive: true,
        isBlocked: false,
        createdAt: expect.any(Date),
        updatedAt: expect.any(Date)
      });
      expect(result).toBe('mock-id');
    });

    it('should merge provided settings and statistics', async () => {
      const groupData = {
        telegramId: -123456,
        title: 'Test Group',
        type: 'group',
        settings: { dailyPredictions: true },
        statistics: { memberCount: 10 },
        admins: [{ telegramId: 123, username: 'admin' }]
      };
      mockCollection.insertOne.mockResolvedValue({ insertedId: 'mock-id' });

      await repository.createGroup(groupData);

      expect(mockCollection.insertOne).toHaveBeenCalledWith(
        expect.objectContaining({
          settings: expect.objectContaining({
            dailyPredictions: true,
            predictionTime: '08:00'
          }),
          statistics: expect.objectContaining({
            memberCount: 10,
            totalMessages: 0
          }),
          admins: [{ telegramId: 123, username: 'admin' }]
        })
      );
    });

    it('should handle creation errors', async () => {
      mockCollection.insertOne.mockRejectedValue(new Error('DB Error'));

      await expect(repository.createGroup({})).rejects.toThrow(
        'Failed to create group:'
      );
    });
  });

  describe('findByTelegramId', () => {
    it('should find group by telegram ID', async () => {
      const mockGroup = { telegramId: -123456, title: 'Test Group' };
      mockCollection.findOne.mockResolvedValue(mockGroup);

      const result = await repository.findByTelegramId(-123456);

      expect(mockCollection.findOne).toHaveBeenCalledWith({ telegramId: -123456 }, {});
      expect(result).toBe(mockGroup);
    });

    it('should handle find errors', async () => {
      mockCollection.findOne.mockRejectedValue(new Error('DB Error'));

      await expect(repository.findByTelegramId(-123456)).rejects.toThrow(
        'Failed to find group:'
      );
    });
  });

  describe('updateSettings', () => {
    it('should update group settings', async () => {
      const settings = { dailyPredictions: true, predictionTime: '09:00' };
      const mockResult = { modifiedCount: 1 };
      mockCollection.updateOne.mockResolvedValue(mockResult);

      const result = await repository.updateSettings(-123456, settings);

      expect(mockCollection.updateOne).toHaveBeenCalledWith(
        { telegramId: -123456 },
        {
          $set: {
            'settings.dailyPredictions': true,
            'settings.predictionTime': '09:00',
            updatedAt: expect.any(Date)
          }
        },
        {}
      );
      expect(result).toBe(mockResult);
    });

    it('should handle settings update errors', async () => {
      mockCollection.updateOne.mockRejectedValue(new Error('DB Error'));

      await expect(repository.updateSettings(-123456, {})).rejects.toThrow(
        'Failed to update group settings:'
      );
    });
  });

  describe('getSettings', () => {
    it('should get group settings', async () => {
      const mockGroup = {
        settings: { dailyPredictions: true },
        isActive: true,
        isBlocked: false
      };
      mockCollection.findOne.mockResolvedValue(mockGroup);

      const result = await repository.getSettings(-123456);

      expect(mockCollection.findOne).toHaveBeenCalledWith(
        { telegramId: -123456 },
        { projection: { settings: 1, isActive: 1, isBlocked: 1 } }
      );
      expect(result).toBe(mockGroup.settings);
    });

    it('should return null if group not found', async () => {
      mockCollection.findOne.mockResolvedValue(null);

      const result = await repository.getSettings(-123456);

      expect(result).toBeNull();
    });

    it('should handle getSettings errors', async () => {
      mockCollection.findOne.mockRejectedValue(new Error('DB Error'));

      await expect(repository.getSettings(-123456)).rejects.toThrow(
        'Failed to get group settings:'
      );
    });
  });

  describe('addAdmin', () => {
    it('should add admin to group', async () => {
      const adminData = {
        telegramId: 123,
        username: 'admin',
        firstName: 'Admin'
      };
      const mockResult = { modifiedCount: 1 };
      mockCollection.updateOne.mockResolvedValue(mockResult);

      const result = await repository.addAdmin(-123456, adminData);

      expect(mockCollection.updateOne).toHaveBeenCalledWith(
        { telegramId: -123456 },
        {
          $addToSet: {
            admins: {
              telegramId: 123,
              username: 'admin',
              firstName: 'Admin',
              addedAt: expect.any(Date)
            }
          }
        }
      );
      expect(result).toBe(mockResult);
    });

    it('should handle add admin errors', async () => {
      mockCollection.updateOne.mockRejectedValue(new Error('DB Error'));

      await expect(repository.addAdmin(-123456, {})).rejects.toThrow(
        'Failed to add admin: DB Error'
      );
    });
  });

  describe('removeAdmin', () => {
    it('should remove admin from group', async () => {
      const mockResult = { modifiedCount: 1 };
      mockCollection.updateOne.mockResolvedValue(mockResult);

      const result = await repository.removeAdmin(-123456, 123);

      expect(mockCollection.updateOne).toHaveBeenCalledWith(
        { telegramId: -123456 },
        { $pull: { admins: { telegramId: 123 } } }
      );
      expect(result).toBe(mockResult);
    });

    it('should handle remove admin errors', async () => {
      mockCollection.updateOne.mockRejectedValue(new Error('DB Error'));

      await expect(repository.removeAdmin(-123456, 123)).rejects.toThrow(
        'Failed to remove admin: DB Error'
      );
    });
  });

  describe('isAdmin', () => {
    it('should return true if user is admin', async () => {
      const mockGroup = { _id: 'group-id' };
      mockCollection.findOne.mockResolvedValue(mockGroup);

      const result = await repository.isAdmin(-123456, 123);

      expect(mockCollection.findOne).toHaveBeenCalledWith(
        {
          telegramId: -123456,
          'admins.telegramId': 123
        },
        { projection: { _id: 1 } }
      );
      expect(result).toBe(true);
    });

    it('should return false if user is not admin', async () => {
      mockCollection.findOne.mockResolvedValue(null);

      const result = await repository.isAdmin(-123456, 123);

      expect(result).toBe(false);
    });

    it('should handle isAdmin errors', async () => {
      mockCollection.findOne.mockRejectedValue(new Error('DB Error'));

      await expect(repository.isAdmin(-123456, 123)).rejects.toThrow(
        'Failed to check admin status:'
      );
    });
  });

  describe('getGroupsWithDailyPredictions', () => {
    it('should get groups with daily predictions enabled', async () => {
      const mockGroups = [
        { telegramId: -123456, settings: { dailyPredictions: true } }
      ];
      
      mockCollection.find.mockReturnValue({
        project: jest.fn().mockReturnThis(),
        sort: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        toArray: jest.fn().mockResolvedValue(mockGroups)
      });

      const result = await repository.getGroupsWithDailyPredictions();

      expect(mockCollection.find).toHaveBeenCalledWith({
        'settings.dailyPredictions': true,
        isActive: true,
        isBlocked: false
      });
      expect(result).toBe(mockGroups);
    });

    it('should handle errors', async () => {
      mockCollection.find.mockReturnValue({
        project: jest.fn().mockReturnThis(),
        sort: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        toArray: jest.fn().mockRejectedValue(new Error('DB Error'))
      });

      await expect(repository.getGroupsWithDailyPredictions()).rejects.toThrow(
        'Failed to get groups with daily predictions:'
      );
    });
  });

  describe('getGroupAnalytics', () => {
    it('should get group analytics', async () => {
      const mockAnalytics = {
        totalGroups: 50,
        totalMembers: 1000,
        avgMembersPerGroup: 20,
        groupsWithDailyPredictions: 15,
        groupsWithWeeklyReports: 10,
        activeToday: 25
      };
      
      mockCollection.aggregate.mockReturnValue({
        toArray: jest.fn().mockResolvedValue([mockAnalytics])
      });

      const result = await repository.getGroupAnalytics();

      expect(mockCollection.aggregate).toHaveBeenCalledWith([
        {
          $match: {
            isActive: true,
            isBlocked: false
          }
        },
        {
          $group: {
            _id: null,
            totalGroups: { $sum: 1 },
            totalMembers: { $sum: '$statistics.memberCount' },
            avgMembersPerGroup: { $avg: '$statistics.memberCount' },
            groupsWithDailyPredictions: {
              $sum: {
                $cond: ['$settings.dailyPredictions', 1, 0]
              }
            },
            groupsWithWeeklyReports: {
              $sum: {
                $cond: ['$settings.weeklyReports', 1, 0]
              }
            },
            activeToday: {
              $sum: {
                $cond: [
                  {
                    $gte: [
                      '$statistics.lastActivity',
                      expect.any(Date)
                    ]
                  },
                  1,
                  0
                ]
              }
            }
          }
        }
      ]);
      expect(result).toBe(mockAnalytics);
    });

    it('should return default analytics if no results', async () => {
      mockCollection.aggregate.mockReturnValue({
        toArray: jest.fn().mockResolvedValue([])
      });

      const result = await repository.getGroupAnalytics();

      expect(result).toEqual({
        totalGroups: 0,
        totalMembers: 0,
        avgMembersPerGroup: 0,
        groupsWithDailyPredictions: 0,
        groupsWithWeeklyReports: 0,
        activeToday: 0
      });
    });
  });

  describe('validateData', () => {
    it('should validate correct group data', () => {
      const validData = {
        telegramId: -123456,
        title: 'Test Group',
        type: 'group'
      };

      expect(() => repository.validateData(validData)).not.toThrow();
    });

    it('should throw error for missing telegramId', () => {
      const invalidData = {
        title: 'Test Group',
        type: 'group'
      };

      expect(() => repository.validateData(invalidData)).toThrow(
        'telegramId is required and must be a number'
      );
    });

    it('should throw error for invalid type', () => {
      const invalidData = {
        telegramId: -123456,
        title: 'Test Group',
        type: 'invalid'
      };

      expect(() => repository.validateData(invalidData)).toThrow(
        'type must be one of: group, supergroup, channel'
      );
    });

    it('should throw error for missing title', () => {
      const invalidData = {
        telegramId: -123456,
        type: 'group'
      };

      expect(() => repository.validateData(invalidData)).toThrow(
        'title is required and must be a string'
      );
    });
  });

  describe('createIndexes', () => {
    it('should create database indexes', async () => {
      await repository.createIndexes();

      expect(mockCollection.createIndexes).toHaveBeenCalledWith([
        { key: { telegramId: 1 }, unique: true },
        { key: { type: 1, isActive: 1 } },
        { key: { 'statistics.lastActivity': -1 } },
        { key: { 'settings.dailyPredictions': 1, isActive: 1 } },
        { key: { 'settings.weeklyReports': 1, 'settings.weeklyReportDay': 1, isActive: 1 } },
        { key: { isActive: 1, isBlocked: 1 } },
        { key: { createdAt: -1 } }
      ]);
    });

    it('should handle index creation errors', async () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      mockCollection.createIndexes.mockRejectedValue(new Error('Index Error'));

      await repository.createIndexes();

      expect(consoleSpy).toHaveBeenCalledWith(
        'Failed to create group indexes:',
        'Index Error'
      );
      consoleSpy.mockRestore();
    });
  });
});