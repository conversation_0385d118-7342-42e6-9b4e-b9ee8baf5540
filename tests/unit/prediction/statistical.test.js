// Unit tests for Statistical Predictor
const StatisticalPredictor = require('../../../lib/prediction/models/statistical');

describe('StatisticalPredictor', () => {
  let predictor;
  let mockHistoricalData;

  beforeEach(() => {
    predictor = new StatisticalPredictor({
      lookbackPeriod: 50,
      minFrequency: 2
    });

    mockHistoricalData = [
      testUtils.createMockLotteryResult({
        numbers: { lo: ['12', '34', '56'], de: ['12', '34'] }
      }),
      testUtils.createMockLotteryResult({
        numbers: { lo: ['12', '78', '90'], de: ['56', '78'] }
      }),
      testUtils.createMockLotteryResult({
        numbers: { lo: ['34', '56', '78'], de: ['12', '90'] }
      })
    ];
  });

  describe('calculateFrequencies', () => {
    it('should calculate number frequencies correctly', () => {
      const frequencies = predictor.calculateFrequencies(mockHistoricalData, 'lo');

      expect(frequencies.get('12')).toBe(2);
      expect(frequencies.get('34')).toBe(2);
      expect(frequencies.get('56')).toBe(2);
      expect(frequencies.get('78')).toBe(2);
      expect(frequencies.get('90')).toBe(1);
    });

    it('should handle empty data', () => {
      const frequencies = predictor.calculateFrequencies([], 'lo');
      expect(frequencies.size).toBe(0);
    });
  });

  describe('calculateProbabilities', () => {
    it('should convert frequencies to probabilities', () => {
      const frequencies = new Map([
        ['12', 4],
        ['34', 2],
        ['56', 2]
      ]);

      const probabilities = predictor.calculateProbabilities(frequencies);

      expect(probabilities.get('12')).toBe(0.5); // 4/8
      expect(probabilities.get('34')).toBe(0.25); // 2/8
      expect(probabilities.get('56')).toBe(0.25); // 2/8
    });
  });

  describe('predict', () => {
    it('should generate predictions with confidence scores', async () => {
      const result = await predictor.predict(mockHistoricalData, 'lo');

      expect(result).toHaveProperty('predictions');
      expect(result).toHaveProperty('confidence');
      expect(result).toHaveProperty('method', 'statistical');
      expect(Array.isArray(result.predictions)).toBe(true);
      expect(result.predictions.length).toBeGreaterThan(0);

      // Check prediction structure - updated for new format
      const prediction = result.predictions[0];
      expect(prediction).toHaveProperty('number');
      expect(prediction).toHaveProperty('confidence');
      expect(prediction).toHaveProperty('reasoning');
      expect(prediction).toHaveProperty('probability');
      expect(prediction).toHaveProperty('metadata');
    });

    it('should handle different lottery types', async () => {
      const loResult = await predictor.predict(mockHistoricalData, 'lo');
      const deResult = await predictor.predict(mockHistoricalData, 'de');

      expect(loResult.predictions).not.toEqual(deResult.predictions);
    });

    it('should respect lookback period', async () => {
      const shortPredictor = new StatisticalPredictor({ lookbackPeriod: 2 });
      const result = await shortPredictor.predict(mockHistoricalData, 'lo');

      expect(result).toHaveProperty('dataPoints');
      expect(result.dataPoints).toBeLessThanOrEqual(2);
    });
  });

  describe('analyzeHotColdNumbers', () => {
    it('should identify hot and cold numbers', () => {
      const analysis = predictor.analyzeHotColdNumbers(mockHistoricalData, 'lo', 30);

      expect(analysis).toHaveProperty('hot');
      expect(analysis).toHaveProperty('cold');
      expect(Array.isArray(analysis.hot)).toBe(true);
      expect(Array.isArray(analysis.cold)).toBe(true);
      expect(analysis.hot.length).toBeLessThanOrEqual(5);
      expect(analysis.cold.length).toBeLessThanOrEqual(5);

      // Check backward compatibility format
      if (analysis.hot.length > 0) {
        expect(analysis.hot[0]).toHaveProperty('number');
        expect(analysis.hot[0]).toHaveProperty('frequency');
      }
    });
  });

  describe('calculateConfidence', () => {
    it('should calculate confidence based on probability distribution', () => {
      const probabilities = new Map([
        ['12', 0.5],
        ['34', 0.3],
        ['56', 0.2]
      ]);

      const confidence = predictor.calculateConfidence(probabilities);

      expect(typeof confidence).toBe('number');
      expect(confidence).toBeGreaterThanOrEqual(0);
      expect(confidence).toBeLessThanOrEqual(100);
    });

    it('should handle empty probabilities', () => {
      const confidence = predictor.calculateConfidence(new Map());
      expect(confidence).toBe(0);
    });
  });

  describe('enhanced methods', () => {
    it('should analyze individual numbers', () => {
      const analysis = predictor.analyzeNumber('12', mockHistoricalData, 'lo');

      expect(analysis).toHaveProperty('number', '12');
      expect(analysis).toHaveProperty('totalOccurrences');
      expect(analysis).toHaveProperty('probability');
      expect(analysis).toHaveProperty('trend');
      expect(analysis).toHaveProperty('hotColdStatus');
    });

    it('should detect patterns', () => {
      const patterns = predictor.detectPatterns(mockHistoricalData, 'lo');

      expect(patterns).toHaveProperty('consecutivePatterns');
      expect(patterns).toHaveProperty('gapPatterns');
      expect(patterns).toHaveProperty('cyclicPatterns');
      expect(patterns).toHaveProperty('sumPatterns');
    });

    it('should provide detailed hot/cold analysis', () => {
      const analysis = predictor.getDetailedHotColdAnalysis(mockHistoricalData, 'lo');

      expect(analysis).toHaveProperty('periods');
      expect(analysis).toHaveProperty('hot');
      expect(analysis).toHaveProperty('cold');
      expect(analysis).toHaveProperty('trending');
    });
  });

  describe('backward compatibility', () => {
    it('should maintain legacy generatePredictions format', () => {
      const probabilities = new Map([
        ['12', 0.4],
        ['34', 0.3],
        ['56', 0.3]
      ]);

      const predictions = predictor.generatePredictions(probabilities);

      expect(Array.isArray(predictions)).toBe(true);
      expect(predictions.length).toBeLessThanOrEqual(10);

      if (predictions.length > 0) {
        const prediction = predictions[0];
        expect(prediction).toHaveProperty('number');
        expect(prediction).toHaveProperty('confidence');
        expect(prediction).toHaveProperty('reasoning');
        expect(typeof prediction.confidence).toBe('number');
      }
    });
  });
});