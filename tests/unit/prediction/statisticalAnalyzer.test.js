// Unit tests for StatisticalAnalyzer
const StatisticalAnalyzer = require('../../../lib/prediction/analyzers/statisticalAnalyzer');

describe('StatisticalAnalyzer', () => {
  let analyzer;
  let mockHistoricalData;

  beforeEach(() => {
    analyzer = new StatisticalAnalyzer({
      defaultLookbackPeriod: 50,
      minFrequency: 2,
      hotColdThreshold: 0.15,
      patternMinLength: 3
    });

    // Create comprehensive mock data
    mockHistoricalData = [
      testUtils.createMockLotteryResult({
        date: new Date('2024-01-01'),
        numbers: { lo: ['12', '34', '56'], de: ['12', '34'] }
      }),
      testUtils.createMockLotteryResult({
        date: new Date('2024-01-02'),
        numbers: { lo: ['12', '78', '90'], de: ['56', '78'] }
      }),
      testUtils.createMockLotteryResult({
        date: new Date('2024-01-03'),
        numbers: { lo: ['34', '56', '78'], de: ['12', '90'] }
      }),
      testUtils.createMockLotteryResult({
        date: new Date('2024-01-04'),
        numbers: { lo: ['12', '34', '90'], de: ['34', '56'] }
      }),
      testUtils.createMockLotteryResult({
        date: new Date('2024-01-05'),
        numbers: { lo: ['56', '78', '01'], de: ['78', '01'] }
      })
    ];
  });

  describe('calculateFrequencies', () => {
    it('should calculate number frequencies correctly', () => {
      const frequencies = analyzer.calculateFrequencies(mockHistoricalData, 'lo');

      expect(frequencies.get('12')).toBe(3); // appears in first 3 results
      expect(frequencies.get('34')).toBe(3);
      expect(frequencies.get('56')).toBe(3);
      expect(frequencies.get('78')).toBe(3);
      expect(frequencies.get('90')).toBe(2);
      expect(frequencies.get('01')).toBe(1);
    });

    it('should handle period limitation', () => {
      const frequencies = analyzer.calculateFrequencies(mockHistoricalData, 'lo', 2);

      // Only last 2 results should be considered (indices 3 and 4)
      // Index 3: ['12', '34', '90']
      // Index 4: ['56', '78', '01']
      expect(frequencies.get('12')).toBe(1);
      expect(frequencies.get('34')).toBe(1);
      expect(frequencies.get('56')).toBe(1);
      expect(frequencies.get('78')).toBe(1);
      expect(frequencies.get('90')).toBe(1);
      expect(frequencies.get('01')).toBe(1);
    });

    it('should handle empty data', () => {
      const frequencies = analyzer.calculateFrequencies([], 'lo');
      expect(frequencies.size).toBe(0);
    });

    it('should handle missing numbers property', () => {
      const badData = [{ date: new Date(), region: 'north' }];
      const frequencies = analyzer.calculateFrequencies(badData, 'lo');
      expect(frequencies.size).toBe(0);
    });

    it('should normalize numbers correctly', () => {
      const dataWithSingleDigits = [
        testUtils.createMockLotteryResult({
          numbers: { lo: ['1', '2', '12'], de: ['3', '4'] }
        })
      ];

      const frequencies = analyzer.calculateFrequencies(dataWithSingleDigits, 'lo');

      expect(frequencies.get('01')).toBe(1);
      expect(frequencies.get('02')).toBe(1);
      expect(frequencies.get('12')).toBe(1);
    });
  });

  describe('calculateProbabilities', () => {
    it('should convert frequencies to probabilities', () => {
      const frequencies = new Map([
        ['12', 4],
        ['34', 2],
        ['56', 2]
      ]);

      const probabilities = analyzer.calculateProbabilities(frequencies);

      expect(probabilities.get('12')).toBe(0.5); // 4/8
      expect(probabilities.get('34')).toBe(0.25); // 2/8
      expect(probabilities.get('56')).toBe(0.25); // 2/8
    });

    it('should handle empty frequencies', () => {
      const probabilities = analyzer.calculateProbabilities(new Map());
      expect(probabilities.size).toBe(0);
    });

    it('should handle zero total frequency', () => {
      const frequencies = new Map([['12', 0], ['34', 0]]);
      const probabilities = analyzer.calculateProbabilities(frequencies);
      expect(probabilities.size).toBe(0);
    });
  });

  describe('analyzeHotColdNumbers', () => {
    it('should identify hot and cold numbers', () => {
      const analysis = analyzer.analyzeHotColdNumbers(mockHistoricalData, 'lo');

      expect(analysis).toHaveProperty('periods');
      expect(analysis).toHaveProperty('hot');
      expect(analysis).toHaveProperty('cold');
      expect(analysis).toHaveProperty('trending');

      expect(Array.isArray(analysis.hot)).toBe(true);
      expect(Array.isArray(analysis.cold)).toBe(true);
      expect(Array.isArray(analysis.trending)).toBe(true);

      // Check structure of hot numbers
      if (analysis.hot.length > 0) {
        const hotNumber = analysis.hot[0];
        expect(hotNumber).toHaveProperty('number');
        expect(hotNumber).toHaveProperty('probability');
        expect(hotNumber).toHaveProperty('intensity');
        expect(hotNumber).toHaveProperty('periods');
      }
    });

    it('should use configurable time periods', () => {
      const options = {
        shortPeriod: 2,
        mediumPeriod: 3,
        longPeriod: 4
      };

      const analysis = analyzer.analyzeHotColdNumbers(mockHistoricalData, 'lo', options);

      expect(analysis.periods.short.days).toBe(2);
      expect(analysis.periods.medium.days).toBe(3);
      expect(analysis.periods.long.days).toBe(4);
    });

    it('should handle different lottery types', () => {
      // Create data with more distinct differences between lo and de
      const distinctData = [
        testUtils.createMockLotteryResult({
          numbers: { lo: ['10', '20', '30'], de: ['40', '50'] }
        }),
        testUtils.createMockLotteryResult({
          numbers: { lo: ['10', '60', '70'], de: ['40', '80'] }
        }),
        testUtils.createMockLotteryResult({
          numbers: { lo: ['20', '30', '80'], de: ['50', '90'] }
        })
      ];

      const loAnalysis = analyzer.analyzeHotColdNumbers(distinctData, 'lo');
      const deAnalysis = analyzer.analyzeHotColdNumbers(distinctData, 'de');

      // Should have different results due to different number sets
      const loNumbers = loAnalysis.hot.map(h => h.number).concat(loAnalysis.cold.map(c => c.number));
      const deNumbers = deAnalysis.hot.map(h => h.number).concat(deAnalysis.cold.map(c => c.number));

      expect(loNumbers).not.toEqual(deNumbers);
    });
  });

  describe('detectPatterns', () => {
    it('should detect consecutive patterns', () => {
      const patterns = analyzer.detectPatterns(mockHistoricalData, 'lo');

      expect(patterns).toHaveProperty('consecutivePatterns');
      expect(patterns).toHaveProperty('gapPatterns');
      expect(patterns).toHaveProperty('cyclicPatterns');
      expect(patterns).toHaveProperty('sumPatterns');

      expect(Array.isArray(patterns.consecutivePatterns)).toBe(true);
      expect(Array.isArray(patterns.sumPatterns)).toBe(true);
    });

    it('should find sum patterns', () => {
      const patterns = analyzer.detectPatterns(mockHistoricalData, 'lo');

      expect(patterns.sumPatterns.length).toBeGreaterThanOrEqual(0);

      if (patterns.sumPatterns.length > 0) {
        const sumPattern = patterns.sumPatterns[0];
        expect(sumPattern).toHaveProperty('sum');
        expect(sumPattern).toHaveProperty('occurrences');
        expect(sumPattern).toHaveProperty('probability');
      }
    });

    it('should use configurable options', () => {
      const options = {
        minLength: 2,
        maxGap: 3,
        minOccurrences: 2
      };

      const patterns = analyzer.detectPatterns(mockHistoricalData, 'lo', options);

      // Should not throw and return valid structure
      expect(patterns).toHaveProperty('consecutivePatterns');
      expect(patterns).toHaveProperty('sumPatterns');
    });
  });

  describe('analyzeNumber', () => {
    it('should provide comprehensive number analysis', () => {
      const analysis = analyzer.analyzeNumber('12', mockHistoricalData, 'lo');

      expect(analysis).toHaveProperty('number', '12');
      expect(analysis).toHaveProperty('totalOccurrences');
      expect(analysis).toHaveProperty('probability');
      expect(analysis).toHaveProperty('lastSeen');
      expect(analysis).toHaveProperty('averageGap');
      expect(analysis).toHaveProperty('minGap');
      expect(analysis).toHaveProperty('maxGap');
      expect(analysis).toHaveProperty('currentGap');
      expect(analysis).toHaveProperty('trend');
      expect(analysis).toHaveProperty('hotColdStatus');

      expect(typeof analysis.totalOccurrences).toBe('number');
      expect(typeof analysis.probability).toBe('number');
      expect(['increasing', 'decreasing', 'stable']).toContain(analysis.trend);
      expect(['hot', 'cold', 'neutral']).toContain(analysis.hotColdStatus);
    });

    it('should handle numbers that never appeared', () => {
      const analysis = analyzer.analyzeNumber('99', mockHistoricalData, 'lo');

      expect(analysis.totalOccurrences).toBe(0);
      expect(analysis.probability).toBe(0);
      expect(analysis.lastSeen).toBeNull();
      expect(analysis.averageGap).toBeNull();
      expect(analysis.currentGap).toBe(mockHistoricalData.length);
    });

    it('should normalize single digit numbers', () => {
      const analysis = analyzer.analyzeNumber('1', mockHistoricalData, 'lo');
      expect(analysis.number).toBe('01');
    });
  });

  describe('generatePredictions', () => {
    it('should generate statistical predictions', () => {
      const result = analyzer.generatePredictions(mockHistoricalData, 'lo');

      expect(result).toHaveProperty('predictions');
      expect(result).toHaveProperty('confidence');
      expect(result).toHaveProperty('method', 'statistical');
      expect(result).toHaveProperty('analysisDate');
      expect(result).toHaveProperty('dataPoints', mockHistoricalData.length);

      expect(Array.isArray(result.predictions)).toBe(true);
      expect(result.predictions.length).toBeGreaterThan(0);
      expect(result.predictions.length).toBeLessThanOrEqual(10);

      // Check prediction structure
      const prediction = result.predictions[0];
      expect(prediction).toHaveProperty('number');
      expect(prediction).toHaveProperty('confidence');
      expect(prediction).toHaveProperty('probability');
      expect(prediction).toHaveProperty('reasoning');
      expect(prediction).toHaveProperty('metadata');

      expect(typeof prediction.confidence).toBe('number');
      expect(prediction.confidence).toBeGreaterThanOrEqual(0);
      expect(prediction.confidence).toBeLessThanOrEqual(100);
    });

    it('should sort predictions by confidence', () => {
      const result = analyzer.generatePredictions(mockHistoricalData, 'lo');

      for (let i = 1; i < result.predictions.length; i++) {
        expect(result.predictions[i - 1].confidence).toBeGreaterThanOrEqual(
          result.predictions[i].confidence
        );
      }
    });

    it('should respect count option', () => {
      const result = analyzer.generatePredictions(mockHistoricalData, 'lo', { count: 5 });
      expect(result.predictions.length).toBeLessThanOrEqual(5);
    });

    it('should include reasoning when requested', () => {
      const withReasons = analyzer.generatePredictions(mockHistoricalData, 'lo', {
        includeReasons: true
      });
      const withoutReasons = analyzer.generatePredictions(mockHistoricalData, 'lo', {
        includeReasons: false
      });

      expect(withReasons.predictions[0].reasoning).toBeTruthy();
      expect(withoutReasons.predictions[0].reasoning).toBeNull();
    });

    it('should handle empty historical data', () => {
      const result = analyzer.generatePredictions([], 'lo');

      expect(result.predictions).toEqual([]);
      expect(result.confidence).toBe(0);
      expect(result.dataPoints).toBe(0);
    });
  });

  describe('private helper methods', () => {
    it('should normalize numbers correctly', () => {
      expect(analyzer._normalizeNumber('1')).toBe('01');
      expect(analyzer._normalizeNumber('12')).toBe('12');
      expect(analyzer._normalizeNumber(5)).toBe('05');
    });

    it('should calculate average probability', () => {
      const probabilities = new Map([
        ['12', 0.4],
        ['34', 0.3],
        ['56', 0.3]
      ]);

      const avg = analyzer._calculateAverageProbability(probabilities);
      expect(avg).toBeCloseTo(0.333, 2);
    });

    it('should handle empty probabilities for average', () => {
      const avg = analyzer._calculateAverageProbability(new Map());
      expect(avg).toBe(0);
    });

    it('should find number occurrences', () => {
      const occurrences = analyzer._findNumberOccurrences('12', mockHistoricalData, 'lo');

      expect(Array.isArray(occurrences)).toBe(true);
      expect(occurrences.length).toBe(3); // '12' appears 3 times

      if (occurrences.length > 0) {
        expect(occurrences[0]).toHaveProperty('index');
        expect(occurrences[0]).toHaveProperty('date');
        expect(occurrences[0]).toHaveProperty('position');
      }
    });

    it('should calculate gaps between occurrences', () => {
      const occurrences = [
        { index: 0 }, { index: 2 }, { index: 5 }, { index: 7 }
      ];

      const gaps = analyzer._calculateGapsBetweenOccurrences(occurrences);
      expect(gaps).toEqual([2, 3, 2]);
    });

    it('should calculate current gap', () => {
      const gap = analyzer._calculateCurrentGap('12', mockHistoricalData, 'lo');
      expect(typeof gap).toBe('number');
      expect(gap).toBeGreaterThanOrEqual(0);
    });

    it('should calculate number trend', () => {
      const trend = analyzer._calculateNumberTrend('12', mockHistoricalData, 'lo');
      expect(['increasing', 'decreasing', 'stable']).toContain(trend);
    });

    it('should get hot/cold status', () => {
      const status = analyzer._getHotColdStatus('12', mockHistoricalData, 'lo');
      expect(['hot', 'cold', 'neutral']).toContain(status);
    });
  });

  describe('edge cases and error handling', () => {
    it('should handle malformed lottery results', () => {
      const badData = [
        { date: new Date() }, // missing numbers
        { numbers: {} }, // empty numbers
        { numbers: { lo: null } }, // null lo
        { numbers: { lo: [] } } // empty lo array
      ];

      expect(() => {
        analyzer.calculateFrequencies(badData, 'lo');
      }).not.toThrow();

      const frequencies = analyzer.calculateFrequencies(badData, 'lo');
      expect(frequencies.size).toBe(0);
    });

    it('should handle very small datasets', () => {
      const smallData = [mockHistoricalData[0]];

      const result = analyzer.generatePredictions(smallData, 'lo');
      expect(result.predictions.length).toBeGreaterThanOrEqual(0);
      expect(result.confidence).toBeGreaterThanOrEqual(0);
    });

    it('should handle datasets with no matching numbers', () => {
      const analysis = analyzer.analyzeHotColdNumbers(mockHistoricalData, 'nonexistent');

      expect(analysis.hot).toEqual([]);
      expect(analysis.cold).toEqual([]);
      expect(analysis.trending).toEqual([]);
    });

    it('should handle extreme configuration values', () => {
      const extremeAnalyzer = new StatisticalAnalyzer({
        hotColdThreshold: 0,
        patternMinLength: 1,
        minFrequency: 0
      });

      expect(() => {
        extremeAnalyzer.generatePredictions(mockHistoricalData, 'lo');
      }).not.toThrow();
    });
  });

  describe('performance considerations', () => {
    it('should handle large datasets efficiently', () => {
      // Create a larger dataset
      const largeData = [];
      for (let i = 0; i < 1000; i++) {
        largeData.push(testUtils.createMockLotteryResult({
          date: new Date(2024, 0, i + 1),
          numbers: {
            lo: [
              String(Math.floor(Math.random() * 100)).padStart(2, '0'),
              String(Math.floor(Math.random() * 100)).padStart(2, '0'),
              String(Math.floor(Math.random() * 100)).padStart(2, '0')
            ],
            de: [
              String(Math.floor(Math.random() * 100)).padStart(2, '0'),
              String(Math.floor(Math.random() * 100)).padStart(2, '0')
            ]
          }
        }));
      }

      const startTime = Date.now();
      const result = analyzer.generatePredictions(largeData, 'lo');
      const endTime = Date.now();

      expect(result.predictions.length).toBeGreaterThan(0);
      expect(endTime - startTime).toBeLessThan(5000); // Should complete within 5 seconds
    });
  });
});