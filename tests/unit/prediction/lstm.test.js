const LSTMPredictor = require('../../../lib/prediction/models/lstm');

describe('LSTMPredictor', () => {
  let predictor;
  let mockLotteryData;

  beforeEach(() => {
    predictor = new LSTMPredictor({
      sequenceLength: 10,
      hiddenUnits: 32,
      epochs: 5,
      batchSize: 16
    });

    // Mock lottery data
    mockLotteryData = [];
    for (let i = 0; i < 50; i++) {
      mockLotteryData.push({
        date: new Date(2024, 0, i + 1),
        numbers: {
          lo: ['12', '34', '56'],
          de: ['78', '90']
        }
      });
    }
  });

  afterEach(() => {
    if (predictor) {
      predictor.dispose();
    }
  });

  describe('Constructor and Initialization', () => {
    test('should initialize with default options', () => {
      const defaultPredictor = new LSTMPredictor();
      expect(defaultPredictor.options.sequenceLength).toBe(30);
      expect(defaultPredictor.options.hiddenUnits).toBe(50);
      expect(defaultPredictor.options.epochs).toBe(100);
      expect(defaultPredictor.options.batchSize).toBe(32);
    });

    test('should initialize with custom options', () => {
      expect(predictor.options.sequenceLength).toBe(10);
      expect(predictor.options.hiddenUnits).toBe(32);
      expect(predictor.options.epochs).toBe(5);
      expect(predictor.options.batchSize).toBe(16);
    });

    test('should have correct initial state', () => {
      expect(predictor.model).toBeNull();
      expect(predictor.isTraining).toBe(false);
      expect(predictor.trainingHistory).toEqual([]);
      expect(predictor.modelVersion).toBe('1.0.0');
    });
  });

  describe('Data Preprocessing', () => {
    test('should preprocess lottery data correctly', () => {
      const processed = predictor.preprocessData(mockLotteryData);

      expect(processed).toHaveProperty('sequences');
      expect(processed).toHaveProperty('targets');
      expect(processed).toHaveProperty('originalNumbers');
      expect(processed).toHaveProperty('sequenceCount');

      expect(Array.isArray(processed.sequences)).toBe(true);
      expect(Array.isArray(processed.targets)).toBe(true);
      expect(processed.sequences.length).toBeGreaterThan(0);
      expect(processed.targets.length).toBe(processed.sequences.length);
    });

    test('should normalize sequences to 0-1 range', () => {
      const processed = predictor.preprocessData(mockLotteryData);

      processed.sequences.forEach(sequence => {
        sequence.forEach(value => {
          expect(value).toBeGreaterThanOrEqual(0);
          expect(value).toBeLessThanOrEqual(1);
        });
      });
    });

    test('should create one-hot encoded targets', () => {
      const processed = predictor.preprocessData(mockLotteryData);

      processed.targets.forEach(target => {
        expect(target.length).toBe(100);
        const sum = target.reduce((a, b) => a + b, 0);
        expect(sum).toBe(1); // One-hot encoding should sum to 1
      });
    });

    test('should throw error for invalid data', () => {
      expect(() => predictor.preprocessData([])).toThrow('Invalid lottery data provided');
      expect(() => predictor.preprocessData(null)).toThrow('Invalid lottery data provided');
      expect(() => predictor.preprocessData('invalid')).toThrow('Invalid lottery data provided');
    });

    test('should throw error for insufficient data', () => {
      const insufficientData = [
        {
          numbers: {
            lo: ['12'],
            de: ['34']
          }
        }
      ];

      expect(() => predictor.preprocessData(insufficientData))
        .toThrow('Insufficient data: need at least 11 numbers');
    });

    test('should handle edge cases in number extraction', () => {
      const edgeCaseData = [
        {
          numbers: {
            lo: ['00', '99', 'invalid'],
            de: ['50']
          }
        },
        {
          numbers: {
            lo: ['25'],
            de: ['75', '100'] // 100 should be filtered out
          }
        }
      ];

      // Add more valid data to meet minimum requirements
      for (let i = 0; i < 20; i++) {
        edgeCaseData.push({
          numbers: {
            lo: ['12', '34'],
            de: ['56']
          }
        });
      }

      const processed = predictor.preprocessData(edgeCaseData);
      expect(processed.originalNumbers.length).toBeGreaterThan(0);

      // Should only contain valid numbers (0-99)
      processed.originalNumbers.forEach(num => {
        expect(num).toBeGreaterThanOrEqual(0);
        expect(num).toBeLessThanOrEqual(99);
      });
    });
  });

  describe('Model Information', () => {
    test('should return basic model info when untrained', () => {
      const info = predictor.getModelInfo();

      expect(info.type).toBe('LSTM');
      expect(info.version).toBe('1.0.0');
      expect(info.trained).toBe(false);
      expect(info.training).toBe(false);
      expect(info.options).toEqual(predictor.options);
      expect(info.trainingHistory).toEqual([]);
      expect(info.architecture).toBeNull();
    });
  });

  describe('Input Validation', () => {
    test('should validate prediction input format', async () => {
      // Test without trained model
      await expect(predictor.predict([1, 2, 3]))
        .rejects.toThrow('Model not trained yet');
    });

    test('should validate sequence length requirements', () => {
      const shortData = [
        { numbers: { lo: ['12'], de: ['34'] } }
      ];

      expect(() => predictor.preprocessData(shortData))
        .toThrow('Insufficient data');
    });
  });

  describe('Memory Management', () => {
    test('should dispose model and free memory', () => {
      // Set a mock model to test disposal
      predictor.model = { dispose: jest.fn() };

      predictor.dispose();
      expect(predictor.model).toBeNull();
    });

    test('should handle disposal when no model exists', () => {
      expect(predictor.model).toBeNull();
      expect(() => predictor.dispose()).not.toThrow();
      expect(predictor.model).toBeNull();
    });
  });

  describe('Configuration Validation', () => {
    test('should accept valid configuration options', () => {
      const customPredictor = new LSTMPredictor({
        sequenceLength: 20,
        hiddenUnits: 64,
        epochs: 50,
        batchSize: 64,
        learningRate: 0.01,
        validationSplit: 0.3,
        patience: 15
      });

      expect(customPredictor.options.sequenceLength).toBe(20);
      expect(customPredictor.options.hiddenUnits).toBe(64);
      expect(customPredictor.options.epochs).toBe(50);
      expect(customPredictor.options.batchSize).toBe(64);
      expect(customPredictor.options.learningRate).toBe(0.01);
      expect(customPredictor.options.validationSplit).toBe(0.3);
      expect(customPredictor.options.patience).toBe(15);
    });
  });

  describe('Error Handling', () => {
    test('should handle invalid model save path', async () => {
      await expect(predictor.saveModel('/invalid/path'))
        .rejects.toThrow('No model to save');
    });

    test('should handle invalid model load path', async () => {
      // The actual TensorFlow.js will throw an error for invalid paths
      // For now, we'll test that the method exists and can be called
      const result = await predictor.loadModel('/nonexistent/path');
      expect(typeof result).toBe('boolean');
    });

    test('should handle validation without model', async () => {
      await expect(predictor.validateModel(mockLotteryData))
        .rejects.toThrow('Model not available for validation');
    });
  });
});