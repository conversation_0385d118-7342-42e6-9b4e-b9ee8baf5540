// Tests for enhanced prediction engine with unified interface and confidence scoring
const PredictionEngine = require('../../../lib/prediction/index');

// Mock the testUtils if not available
const testUtils = {
  createMockLotteryResult: (overrides = {}) => ({
    date: new Date(),
    numbers: { lo: ['12', '34'], de: ['56'] },
    ...overrides
  })
};

describe('PredictionEngine', () => {
  let engine;
  let mockDataManager;

  beforeEach(() => {
    mockDataManager = {
      getLotteryHistory: jest.fn()
    };
    engine = new PredictionEngine(mockDataManager, {
      enableLSTM: false, // Disable LSTM for testing
      enableEnsemble: false,
      cacheEnabled: true,
      cacheTTL: 0.01, // 10ms TTL for testing
      maxPredictions: 5
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('initialization', () => {
    test('should initialize with default options', () => {
      expect(engine.dataManager).toBe(mockDataManager);
      expect(engine.models).toBeInstanceOf(Map);
      expect(engine.analyzers).toBeInstanceOf(Map);
      expect(engine.cache).toBeInstanceOf(Map);
      expect(engine.accuracyMetrics).toBeInstanceOf(Map);
      expect(engine.initialized).toBe(false);
    });

    test('should initialize models and analyzers', async () => {
      await engine.initialize();

      expect(engine.initialized).toBe(true);
      expect(engine.models.has('statistical')).toBe(true);
      expect(engine.analyzers.has('statistical')).toBe(true);
      expect(engine.analyzers.has('trend')).toBe(true);
      expect(engine.accuracyMetrics.has('statistical')).toBe(true);
    });

    test('should initialize with ensemble when multiple models available', async () => {
      const engineWithEnsemble = new PredictionEngine(mockDataManager, {
        enableLSTM: false,
        enableEnsemble: true
      });

      await engineWithEnsemble.initialize();

      // With only statistical model, ensemble should not be created
      expect(engineWithEnsemble.models.has('ensemble')).toBe(false);
    });

    test('should handle initialization errors gracefully', async () => {
      // Create engine with invalid options to trigger error
      const failingEngine = new PredictionEngine(mockDataManager);

      // Mock the statistical model to throw an error
      const originalRequire = require;
      jest.doMock('../../../lib/prediction/models/statistical', () => {
        throw new Error('Model initialization failed');
      });

      try {
        await failingEngine.initialize();
      } catch (error) {
        expect(error.message).toContain('Prediction Engine initialization failed');
      }
    });
  });

  describe('predictLo', () => {
    beforeEach(async () => {
      await engine.initialize();
    });

    test('should return predictions for lo numbers', async () => {
      const mockHistory = [
        {
          date: new Date('2024-01-01'),
          numbers: { lo: ['12', '34', '56'] }
        },
        {
          date: new Date('2024-01-02'),
          numbers: { lo: ['78', '90', '12'] }
        }
      ];
      mockDataManager.getLotteryHistory.mockResolvedValue(mockHistory);

      const result = await engine.predictLo();

      expect(result).toHaveProperty('predictions');
      expect(result).toHaveProperty('confidence');
      expect(result).toHaveProperty('method');
      expect(result).toHaveProperty('type');
      expect(result).toHaveProperty('timestamp');
      expect(result.type).toBe('lo');
      expect(Array.isArray(result.predictions)).toBe(true);
    });

    test('should use cache for repeated requests', async () => {
      const mockHistory = [
        { numbers: { lo: ['12', '34', '56'] } }
      ];
      mockDataManager.getLotteryHistory.mockResolvedValue(mockHistory);

      // First request
      const result1 = await engine.predictLo();
      expect(mockDataManager.getLotteryHistory).toHaveBeenCalledTimes(1);

      // Second request should use cache
      const result2 = await engine.predictLo();
      expect(mockDataManager.getLotteryHistory).toHaveBeenCalledTimes(1);
      expect(result2.cached).toBe(true);
    });

    test('should handle data manager errors gracefully', async () => {
      mockDataManager.getLotteryHistory.mockRejectedValue(new Error('Database error'));

      const result = await engine.predictLo();

      expect(result.predictions).toEqual([]);
      expect(result.confidence).toBe(0);
      expect(result.method).toBe('error');
      expect(result.error).toBe('Database error');
    });

    test('should respect maxPredictions option', async () => {
      const mockHistory = Array.from({ length: 100 }, (_, i) => ({
        numbers: { lo: [String(i % 100).padStart(2, '0')] }
      }));
      mockDataManager.getLotteryHistory.mockResolvedValue(mockHistory);

      const result = await engine.predictLo();

      expect(result.predictions.length).toBeLessThanOrEqual(engine.options.maxPredictions);
    });
  });

  describe('predictDe', () => {
    beforeEach(async () => {
      await engine.initialize();
    });

    test('should return predictions for de numbers', async () => {
      const mockHistory = [
        { numbers: { de: ['12', '34'] } },
        { numbers: { de: ['56', '78'] } }
      ];
      mockDataManager.getLotteryHistory.mockResolvedValue(mockHistory);

      const result = await engine.predictDe();

      expect(result).toHaveProperty('predictions');
      expect(result).toHaveProperty('confidence');
      expect(result).toHaveProperty('method');
      expect(result.type).toBe('de');
    });

    test('should handle specific model requests', async () => {
      const mockHistory = [
        { numbers: { de: ['12', '34'] } }
      ];
      mockDataManager.getLotteryHistory.mockResolvedValue(mockHistory);

      const result = await engine.predictDe({ model: 'statistical' });

      expect(result.method).toBe('statistical');
    });
  });

  describe('confidence scoring', () => {
    beforeEach(async () => {
      await engine.initialize();
    });

    test('should calculate confidence based on multiple factors', async () => {
      const mockHistory = Array.from({ length: 50 }, (_, i) => ({
        numbers: { lo: ['12', '34', '56'] } // Consistent numbers for high confidence
      }));
      mockDataManager.getLotteryHistory.mockResolvedValue(mockHistory);

      const result = await engine.predictLo();

      expect(result.confidence).toBeGreaterThan(0);
      expect(result).toHaveProperty('confidenceFactors');
      expect(result.confidenceFactors).toHaveProperty('baseConfidence');
      expect(result.confidenceFactors).toHaveProperty('consistencyBonus');
    });

    test('should provide lower confidence for inconsistent predictions', async () => {
      const mockHistory = Array.from({ length: 50 }, (_, i) => ({
        numbers: { lo: [String(i % 100).padStart(2, '0')] } // Random numbers
      }));
      mockDataManager.getLotteryHistory.mockResolvedValue(mockHistory);

      const result = await engine.predictLo();

      expect(result.confidence).toBeGreaterThanOrEqual(0);
      expect(result.confidence).toBeLessThan(100);
    });
  });

  describe('updateAccuracy', () => {
    beforeEach(async () => {
      await engine.initialize();
    });

    test('should update accuracy metrics for a model', async () => {
      const actualResults = ['12', '34'];
      const predictions = [
        { number: '12', confidence: 80 },
        { number: '56', confidence: 60 },
        { number: '34', confidence: 70 }
      ];

      const result = await engine.updateAccuracy('statistical', actualResults, predictions);

      expect(result.modelName).toBe('statistical');
      expect(result.correctInBatch).toBe(2);
      expect(result.totalInBatch).toBe(3);
      expect(result.accuracy).toBeGreaterThan(0);

      const metrics = engine.accuracyMetrics.get('statistical');
      expect(metrics.totalPredictions).toBe(3);
      expect(metrics.correctPredictions).toBe(2);
    });

    test('should create new metrics for unknown model', async () => {
      const actualResults = ['12'];
      const predictions = [{ number: '12', confidence: 80 }];

      await engine.updateAccuracy('newModel', actualResults, predictions);

      expect(engine.accuracyMetrics.has('newModel')).toBe(true);
      const metrics = engine.accuracyMetrics.get('newModel');
      expect(metrics.accuracy).toBe(100); // 1/1 correct
    });
  });

  describe('cache management', () => {
    beforeEach(async () => {
      await engine.initialize();
    });

    test('should clear cache', () => {
      engine.cache.set('test', 'value');
      expect(engine.cache.size).toBe(1);

      engine.clearCache();
      expect(engine.cache.size).toBe(0);
    });

    test('should provide cache statistics', () => {
      const stats = engine.getCacheStats();

      expect(stats).toHaveProperty('size');
      expect(stats).toHaveProperty('enabled');
      expect(stats).toHaveProperty('ttl');
      expect(stats.enabled).toBe(true);
    });

    test('should expire cache entries after TTL', async () => {
      const mockHistory = [{ numbers: { lo: ['12'] } }];
      mockDataManager.getLotteryHistory.mockResolvedValue(mockHistory);

      // First request
      const result1 = await engine.predictLo();
      expect(engine.cache.size).toBe(1);
      expect(result1.cached).toBeUndefined();

      // Immediate second request should use cache
      const result2 = await engine.predictLo();
      expect(result2.cached).toBe(true);
      expect(mockDataManager.getLotteryHistory).toHaveBeenCalledTimes(1);

      // Wait for cache to expire (TTL is 0.01s = 10ms in test setup)
      await new Promise(resolve => setTimeout(resolve, 15));

      // Third request should not use cache due to expiration
      const result3 = await engine.predictLo();
      expect(result3.cached).toBeUndefined(); // Should not be cached due to expiration
      expect(mockDataManager.getLotteryHistory).toHaveBeenCalledTimes(2);
    });
  });

  describe('getModelInfo', () => {
    beforeEach(async () => {
      await engine.initialize();
    });

    test('should return comprehensive model information', () => {
      const info = engine.getModelInfo();

      expect(info).toHaveProperty('initialized');
      expect(info).toHaveProperty('models');
      expect(info).toHaveProperty('analyzers');
      expect(info).toHaveProperty('options');
      expect(info).toHaveProperty('cache');
      expect(info).toHaveProperty('accuracy');

      expect(info.initialized).toBe(true);
      expect(info.models).toHaveProperty('statistical');
      expect(info.analyzers).toContain('statistical');
      expect(info.analyzers).toContain('trend');
    });
  });

  describe('updateModel', () => {
    beforeEach(async () => {
      await engine.initialize();
    });

    test('should update models with new data', async () => {
      const newData = [
        { numbers: { lo: ['99', '88'] } }
      ];

      const result = await engine.updateModel(newData);

      expect(result.success).toBe(true);
      expect(result.modelsUpdated).toContain('statistical');
      expect(result).toHaveProperty('timestamp');
    });

    test('should clear cache after model update', async () => {
      engine.cache.set('test', 'value');
      expect(engine.cache.size).toBe(1);

      await engine.updateModel([]);
      expect(engine.cache.size).toBe(0);
    });
  });

  describe('getAccuracy', () => {
    beforeEach(async () => {
      await engine.initialize();
    });

    test('should return accuracy metrics', async () => {
      // Add some accuracy data
      await engine.updateAccuracy('statistical', ['12'], [
        { number: '12', confidence: 80 },
        { number: '34', confidence: 60 }
      ]);

      const result = await engine.getAccuracy();

      expect(result).toHaveProperty('overall');
      expect(result).toHaveProperty('byModel');
      expect(result).toHaveProperty('totalPredictions');
      expect(result).toHaveProperty('totalCorrect');
      expect(result).toHaveProperty('lastUpdated');

      expect(result.byModel).toHaveProperty('statistical');
      expect(result.overall).toBeGreaterThan(0);
    });

    test('should return zero accuracy when no predictions made', async () => {
      const result = await engine.getAccuracy();

      expect(result.overall).toBe(0);
      expect(result.totalPredictions).toBe(0);
      expect(result.totalCorrect).toBe(0);
    });
  });

  describe('analyzeNumber', () => {
    beforeEach(async () => {
      await engine.initialize();
    });

    test('should analyze specific number with comprehensive insights', async () => {
      const mockHistory = [
        { date: new Date(), numbers: { lo: ['12', '34', '56'] } },
        { date: new Date(), numbers: { lo: ['12', '78', '90'] } }
      ];
      mockDataManager.getLotteryHistory.mockResolvedValue(mockHistory);

      const result = await engine.analyzeNumber('12');

      expect(result).toHaveProperty('number', '12');
      expect(result).toHaveProperty('type', 'lo');
      expect(result).toHaveProperty('totalOccurrences');
      expect(result).toHaveProperty('frequency');
      expect(result).toHaveProperty('trends');
      expect(result).toHaveProperty('correlations');
      expect(result).toHaveProperty('patterns');
      expect(result).toHaveProperty('recommendations');
    });

    test('should handle different number types', async () => {
      const mockHistory = [
        { date: new Date(), numbers: { de: ['78', '90'] } }
      ];
      mockDataManager.getLotteryHistory.mockResolvedValue(mockHistory);

      const result = await engine.analyzeNumber('78', 'de');

      expect(result.number).toBe('78');
      expect(result.type).toBe('de');
    });

    test('should handle analysis errors gracefully', async () => {
      mockDataManager.getLotteryHistory.mockRejectedValue(new Error('Database error'));

      const result = await engine.analyzeNumber('12');

      expect(result).toHaveProperty('error');
      expect(result.analysis).toBe('Analysis failed');
    });
  });

  describe('trackNumberPerformance', () => {
    beforeEach(async () => {
      await engine.initialize();
    });

    test('should track historical performance', async () => {
      const mockHistory = Array.from({ length: 50 }, (_, i) => ({
        date: new Date(Date.now() - i * 24 * 60 * 60 * 1000),
        numbers: { lo: i % 5 === 0 ? ['12'] : ['34', '56'] }
      }));
      mockDataManager.getLotteryHistory.mockResolvedValue(mockHistory);

      const result = await engine.trackNumberPerformance('12');

      expect(result).toHaveProperty('number', '12');
      expect(result).toHaveProperty('type', 'lo');
      expect(result).toHaveProperty('overallPerformance');
      expect(result).toHaveProperty('periodPerformance');
      expect(result).toHaveProperty('streaks');
      expect(result).toHaveProperty('gaps');
      expect(result).toHaveProperty('volatility');
    });

    test('should handle missing data manager', async () => {
      const engineWithoutData = new PredictionEngine(null);

      await expect(engineWithoutData.trackNumberPerformance('12')).rejects.toThrow('DataManager not available');
    });
  });

  describe('getTrends', () => {
    beforeEach(async () => {
      await engine.initialize();
    });

    test('should return trend analysis', async () => {
      const mockHistory = [
        { numbers: { lo: ['12', '34', '56'] } }
      ];
      mockDataManager.getLotteryHistory.mockResolvedValue(mockHistory);

      const result = await engine.getTrends('lo', 'weekly');

      expect(result).toHaveProperty('hot');
      expect(result).toHaveProperty('cold');
      expect(result.type).toBe('lo');
      expect(result.period).toBe('weekly');
    });
  });

  describe('error handling', () => {
    test('should handle missing data manager', async () => {
      const engineWithoutData = new PredictionEngine(null);

      const result = await engineWithoutData.predictLo();

      expect(result.method).toBe('error');
      expect(result.error).toContain('DataManager not available');
      expect(result.predictions).toEqual([]);
      expect(result.confidence).toBe(0);
    });

    test('should handle model update errors', async () => {
      await engine.initialize();

      // Mock a model that throws during update
      const mockModel = {
        train: jest.fn().mockRejectedValue(new Error('Training failed'))
      };
      engine.models.set('failing', mockModel);

      // Should not throw if some models succeed
      const result = await engine.updateModel([]);
      expect(result.success).toBe(true);
      expect(result.errors).toBeDefined();
      expect(result.errors[0]).toContain('Training failed');
    });
  });
});