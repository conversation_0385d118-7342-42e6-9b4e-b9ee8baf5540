// Tests for NumberAnalyzer - Detailed number analysis system
const NumberAnalyzer = require('../../../lib/prediction/analyzers/numberAnalyzer');

describe('NumberAnalyzer', () => {
  let analyzer;
  let mockHistoricalData;

  beforeEach(() => {
    analyzer = new NumberAnalyzer({
      minDataPoints: 10,
      correlationThreshold: 0.2,
      shortPeriod: 7,
      mediumPeriod: 30,
      longPeriod: 90
    });

    // Create comprehensive mock data
    mockHistoricalData = [];
    const baseDate = new Date('2024-01-01');

    for (let i = 0; i < 100; i++) {
      const date = new Date(baseDate.getTime() + i * 24 * 60 * 60 * 1000);
      mockHistoricalData.push({
        date,
        numbers: {
          lo: generateMockNumbers(i),
          de: generateMockDeNumbers(i)
        }
      });
    }
  });

  afterEach(() => {
    analyzer.clearCache();
  });

  function generateMockNumbers(index) {
    // Create patterns for testing
    const numbers = [];

    // Number '12' appears frequently in early draws
    if (index < 30 && index % 3 === 0) numbers.push('12');

    // Number '34' appears in cycles
    if (index % 7 === 0) numbers.push('34');

    // Number '56' appears randomly
    if (Math.random() > 0.7) numbers.push('56');

    // Add some random numbers
    for (let i = 0; i < 3; i++) {
      numbers.push(String(Math.floor(Math.random() * 100)).padStart(2, '0'));
    }

    return numbers;
  }

  function generateMockDeNumbers(index) {
    const numbers = [];

    // Number '78' appears in patterns
    if (index % 5 === 0) numbers.push('78');

    // Add random number
    numbers.push(String(Math.floor(Math.random() * 100)).padStart(2, '0'));

    return numbers;
  }

  describe('analyzeNumber', () => {
    test('should analyze a number with comprehensive insights', () => {
      const result = analyzer.analyzeNumber('12', mockHistoricalData, 'lo');

      expect(result).toHaveProperty('number', '12');
      expect(result).toHaveProperty('type', 'lo');
      expect(result).toHaveProperty('dataPoints', 100);
      expect(result).toHaveProperty('totalOccurrences');
      expect(result).toHaveProperty('frequency');
      expect(result).toHaveProperty('expectedFrequency');
      expect(result).toHaveProperty('trends');
      expect(result).toHaveProperty('correlations');
      expect(result).toHaveProperty('patterns');
      expect(result).toHaveProperty('recommendations');
      expect(result).toHaveProperty('lastUpdated');

      expect(Array.isArray(result.correlations)).toBe(true);
      expect(Array.isArray(result.recommendations)).toBe(true);
    });

    test('should calculate basic statistics correctly', () => {
      const result = analyzer.analyzeNumber('34', mockHistoricalData, 'lo');

      expect(result.totalOccurrences).toBeGreaterThan(0);
      expect(result.frequency).toBeGreaterThan(0);
      expect(result.relativeFrequency).toBeGreaterThan(0);
      expect(result.lastSeen).toBeDefined();
      expect(result.daysSinceLastSeen).toBeGreaterThanOrEqual(0);
    });

    test('should analyze trends for different periods', () => {
      const result = analyzer.analyzeNumber('34', mockHistoricalData, 'lo');

      expect(result.trends).toBeDefined();
      expect(typeof result.trends).toBe('object');

      // Should have trend analysis for configured periods
      if (result.trends.short) {
        expect(result.trends.short).toHaveProperty('period');
        expect(result.trends.short).toHaveProperty('occurrences');
        expect(result.trends.short).toHaveProperty('frequency');
        expect(result.trends.short).toHaveProperty('trend');
        expect(['hot', 'cold', 'neutral']).toContain(result.trends.short.trend);
      }
    });

    test('should calculate performance metrics', () => {
      const result = analyzer.analyzeNumber('12', mockHistoricalData, 'lo');

      expect(result).toHaveProperty('consistency');
      expect(result).toHaveProperty('predictability');
      expect(result).toHaveProperty('volatility');
      expect(result).toHaveProperty('momentum');

      expect(typeof result.consistency).toBe('number');
      expect(typeof result.predictability).toBe('number');
      expect(typeof result.volatility).toBe('number');
      expect(typeof result.momentum).toBe('number');
    });

    test('should analyze patterns', () => {
      const result = analyzer.analyzeNumber('34', mockHistoricalData, 'lo');

      expect(result.patterns).toBeDefined();
      expect(result.patterns).toHaveProperty('weekdayPreference');
      expect(result.patterns).toHaveProperty('monthlyDistribution');
      expect(result.patterns).toHaveProperty('consecutiveAppearances');
      expect(result.patterns).toHaveProperty('cyclicPatterns');

      expect(Array.isArray(result.patterns.monthlyDistribution)).toBe(true);
      expect(Array.isArray(result.patterns.consecutiveAppearances)).toBe(true);
      expect(Array.isArray(result.patterns.cyclicPatterns)).toBe(true);
    });

    test('should generate recommendations', () => {
      const result = analyzer.analyzeNumber('12', mockHistoricalData, 'lo');

      expect(Array.isArray(result.recommendations)).toBe(true);

      result.recommendations.forEach(rec => {
        expect(rec).toHaveProperty('type');
        expect(rec).toHaveProperty('priority');
        expect(rec).toHaveProperty('message');
        expect(rec).toHaveProperty('confidence');
        expect(['high', 'medium', 'low']).toContain(rec.priority);
        expect(typeof rec.confidence).toBe('number');
        expect(rec.confidence).toBeGreaterThanOrEqual(0);
        expect(rec.confidence).toBeLessThanOrEqual(100);
      });
    });

    test('should handle numbers with no occurrences', () => {
      // Create specific data without number 'XX'
      const specificData = [
        { date: new Date(), numbers: { lo: ['12', '34'], de: ['56'] } },
        { date: new Date(), numbers: { lo: ['78', '90'], de: ['11'] } }
      ];

      const result = analyzer.analyzeNumber('XX', specificData, 'lo');

      expect(result.number).toBe('XX');
      expect(result.totalOccurrences).toBe(0);
      expect(result.frequency).toBe(0);
      expect(result.lastSeen).toBeNull();
      expect(result.daysSinceLastSeen).toBeNull();
    });

    test('should cache analysis results', () => {
      const result1 = analyzer.analyzeNumber('12', mockHistoricalData, 'lo');
      const result2 = analyzer.analyzeNumber('12', mockHistoricalData, 'lo');

      expect(result1).toEqual(result2);
      expect(analyzer.getCacheStats().performanceCache).toBe(1);
    });

    test('should handle invalid input gracefully', () => {
      expect(() => {
        analyzer.analyzeNumber('12', null, 'lo');
      }).toThrow('Historical data is required');

      expect(() => {
        analyzer.analyzeNumber('12', [], 'lo');
      }).toThrow('Historical data is required');
    });
  });

  describe('trackHistoricalPerformance', () => {
    test('should track performance over time periods', () => {
      const result = analyzer.trackHistoricalPerformance('34', mockHistoricalData, 'lo');

      expect(result).toHaveProperty('number', '34');
      expect(result).toHaveProperty('type', 'lo');
      expect(result).toHaveProperty('overallPerformance');
      expect(result).toHaveProperty('periodPerformance');
      expect(result).toHaveProperty('streaks');
      expect(result).toHaveProperty('gaps');
      expect(result).toHaveProperty('volatility');

      expect(Array.isArray(result.periodPerformance)).toBe(true);
      expect(typeof result.overallPerformance).toBe('number');
      expect(typeof result.volatility).toBe('number');
    });

    test('should calculate streaks correctly', () => {
      const result = analyzer.trackHistoricalPerformance('34', mockHistoricalData, 'lo');

      expect(result.streaks).toHaveProperty('longest');
      expect(result.streaks).toHaveProperty('current');
      expect(result.streaks).toHaveProperty('average');
      expect(result.streaks).toHaveProperty('total');

      expect(typeof result.streaks.longest).toBe('number');
      expect(typeof result.streaks.current).toBe('number');
      expect(typeof result.streaks.average).toBe('number');
      expect(typeof result.streaks.total).toBe('number');
    });

    test('should calculate gaps correctly', () => {
      const result = analyzer.trackHistoricalPerformance('34', mockHistoricalData, 'lo');

      expect(result.gaps).toHaveProperty('longest');
      expect(result.gaps).toHaveProperty('current');
      expect(result.gaps).toHaveProperty('average');
      expect(result.gaps).toHaveProperty('total');

      expect(typeof result.gaps.longest).toBe('number');
      expect(typeof result.gaps.current).toBe('number');
      expect(typeof result.gaps.average).toBe('number');
      expect(typeof result.gaps.total).toBe('number');
    });
  });

  describe('correlation analysis', () => {
    test('should calculate correlations between numbers', () => {
      const result = analyzer.analyzeNumber('12', mockHistoricalData, 'lo');

      expect(Array.isArray(result.correlations)).toBe(true);

      result.correlations.forEach(corr => {
        expect(corr).toHaveProperty('number');
        expect(corr).toHaveProperty('correlation');
        expect(corr).toHaveProperty('strength');
        expect(corr).toHaveProperty('coOccurrences');
        expect(corr).toHaveProperty('significance');

        expect(typeof corr.correlation).toBe('number');
        expect(corr.correlation).toBeGreaterThanOrEqual(-1);
        expect(corr.correlation).toBeLessThanOrEqual(1);
        expect(typeof corr.coOccurrences).toBe('number');
      });
    });

    test('should cache correlation results', () => {
      analyzer.analyzeNumber('12', mockHistoricalData, 'lo');
      analyzer.analyzeNumber('34', mockHistoricalData, 'lo');

      const stats = analyzer.getCacheStats();
      expect(stats.correlationCache).toBeGreaterThan(0);
    });

    test('should limit correlation results', () => {
      const result = analyzer.analyzeNumber('12', mockHistoricalData, 'lo');

      expect(result.correlations.length).toBeLessThanOrEqual(10);
    });
  });

  describe('pattern detection', () => {
    test('should detect cyclic patterns', () => {
      const result = analyzer.analyzeNumber('34', mockHistoricalData, 'lo');

      expect(Array.isArray(result.patterns.cyclicPatterns)).toBe(true);

      result.patterns.cyclicPatterns.forEach(pattern => {
        expect(pattern).toHaveProperty('cycle');
        expect(pattern).toHaveProperty('confidence');
        expect(pattern).toHaveProperty('matches');
        expect(pattern).toHaveProperty('total');
        expect(pattern).toHaveProperty('description');

        expect(typeof pattern.cycle).toBe('number');
        expect(typeof pattern.confidence).toBe('number');
        expect(pattern.confidence).toBeGreaterThan(60); // Only patterns with >60% confidence
      });
    });

    test('should analyze weekday preferences', () => {
      const result = analyzer.analyzeNumber('12', mockHistoricalData, 'lo');

      expect(result.patterns.weekdayPreference).toHaveProperty('preferredDay');
      expect(result.patterns.weekdayPreference).toHaveProperty('distribution');

      expect(Array.isArray(result.patterns.weekdayPreference.distribution)).toBe(true);
      expect(result.patterns.weekdayPreference.distribution).toHaveLength(7);
    });

    test('should analyze monthly distribution', () => {
      const result = analyzer.analyzeNumber('12', mockHistoricalData, 'lo');

      expect(Array.isArray(result.patterns.monthlyDistribution)).toBe(true);
      expect(result.patterns.monthlyDistribution).toHaveLength(12);

      result.patterns.monthlyDistribution.forEach(month => {
        expect(month).toHaveProperty('month');
        expect(month).toHaveProperty('count');
        expect(month).toHaveProperty('percentage');
      });
    });

    test('should find consecutive appearances', () => {
      const result = analyzer.analyzeNumber('12', mockHistoricalData, 'lo');

      expect(Array.isArray(result.patterns.consecutiveAppearances)).toBe(true);

      result.patterns.consecutiveAppearances.forEach(seq => {
        expect(seq).toHaveProperty('length');
        expect(seq).toHaveProperty('startDate');
        expect(seq).toHaveProperty('endDate');
        expect(seq).toHaveProperty('indices');
        expect(seq.length).toBeGreaterThan(1);
      });
    });
  });

  describe('cache management', () => {
    test('should clear caches', () => {
      analyzer.analyzeNumber('12', mockHistoricalData, 'lo');
      analyzer.analyzeNumber('34', mockHistoricalData, 'lo');

      let stats = analyzer.getCacheStats();
      expect(stats.performanceCache).toBeGreaterThan(0);

      analyzer.clearCache();

      stats = analyzer.getCacheStats();
      expect(stats.performanceCache).toBe(0);
      expect(stats.correlationCache).toBe(0);
    });

    test('should provide cache statistics', () => {
      const stats = analyzer.getCacheStats();

      expect(stats).toHaveProperty('performanceCache');
      expect(stats).toHaveProperty('correlationCache');
      expect(typeof stats.performanceCache).toBe('number');
      expect(typeof stats.correlationCache).toBe('number');
    });
  });

  describe('helper methods', () => {
    test('should calculate expected frequency correctly', () => {
      // Test the private method through public interface
      const loResult = analyzer.analyzeNumber('12', mockHistoricalData, 'lo');
      const deResult = analyzer.analyzeNumber('78', mockHistoricalData, 'de');

      expect(loResult.expectedFrequency).toBe(0.27); // 27% for 'lo'
      expect(deResult.expectedFrequency).toBe(0.01); // 1% for 'de' (only 1 number per draw)
    });

    test('should handle edge cases in correlation calculation', () => {
      // Create minimal data set
      const minimalData = [
        { date: new Date(), numbers: { lo: ['12'], de: ['78'] } },
        { date: new Date(), numbers: { lo: ['34'], de: ['90'] } }
      ];

      const result = analyzer.analyzeNumber('12', minimalData, 'lo');

      expect(result).toBeDefined();
      expect(result.correlations).toBeDefined();
      expect(Array.isArray(result.correlations)).toBe(true);
    });

    test('should handle numbers with insufficient data', () => {
      const smallData = mockHistoricalData.slice(0, 5);
      const result = analyzer.analyzeNumber('99', smallData, 'lo');

      expect(result).toBeDefined();
      expect(result.number).toBe('99');
      expect(result.dataPoints).toBe(5);
    });
  });

  describe('recommendation system', () => {
    test('should prioritize recommendations correctly', () => {
      const result = analyzer.analyzeNumber('12', mockHistoricalData, 'lo');

      if (result.recommendations.length > 1) {
        const priorities = result.recommendations.map(r => r.priority);
        const priorityOrder = { high: 3, medium: 2, low: 1 };

        for (let i = 1; i < priorities.length; i++) {
          expect(priorityOrder[priorities[i - 1]]).toBeGreaterThanOrEqual(priorityOrder[priorities[i]]);
        }
      }
    });

    test('should generate appropriate recommendation types', () => {
      const result = analyzer.analyzeNumber('12', mockHistoricalData, 'lo');

      const validTypes = ['trend', 'correlation', 'pattern'];
      result.recommendations.forEach(rec => {
        expect(validTypes).toContain(rec.type);
      });
    });

    test('should include confidence scores in recommendations', () => {
      const result = analyzer.analyzeNumber('12', mockHistoricalData, 'lo');

      result.recommendations.forEach(rec => {
        expect(rec.confidence).toBeGreaterThanOrEqual(0);
        expect(rec.confidence).toBeLessThanOrEqual(100);
      });
    });
  });
});