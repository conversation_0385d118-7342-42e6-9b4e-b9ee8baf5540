const KeyboardBuilder = require('../../../../lib/bot/utils/keyboard');

describe('KeyboardBuilder', () => {
  describe('Constructor and basic methods', () => {
    it('should create empty keyboard builder', () => {
      const builder = new KeyboardBuilder();

      expect(builder.keyboard).toEqual([]);
      expect(builder.currentRow).toEqual([]);
    });

    it('should add buttons to current row', () => {
      const builder = new KeyboardBuilder();

      builder.addButton('Test', 'test_data');

      expect(builder.currentRow).toHaveLength(1);
      expect(builder.currentRow[0]).toEqual({
        text: 'Test',
        callback_data: 'test_data'
      });
    });

    it('should add URL buttons', () => {
      const builder = new KeyboardBuilder();

      builder.addUrlButton('Visit', 'https://example.com');

      expect(builder.currentRow[0]).toEqual({
        text: 'Visit',
        url: 'https://example.com'
      });
    });

    it('should add switch inline buttons', () => {
      const builder = new KeyboardBuilder();

      builder.addSwitchInlineButton('Share', 'query');

      expect(builder.currentRow[0]).toEqual({
        text: 'Share',
        switch_inline_query: 'query'
      });
    });

    it('should throw error for invalid button parameters', () => {
      const builder = new KeyboardBuilder();

      expect(() => builder.addButton('', 'data')).toThrow('Button text and callback data are required');
      expect(() => builder.addButton('text', '')).toThrow('Button text and callback data are required');
      expect(() => builder.addUrlButton('', 'url')).toThrow('Button text and URL are required');
      expect(() => builder.addUrlButton('text', '')).toThrow('Button text and URL are required');
      expect(() => builder.addSwitchInlineButton('')).toThrow('Button text is required');
    });

    it('should create new rows', () => {
      const builder = new KeyboardBuilder();

      builder.addButton('Button 1', 'data1');
      builder.newRow();
      builder.addButton('Button 2', 'data2');

      expect(builder.keyboard).toHaveLength(1);
      expect(builder.keyboard[0]).toHaveLength(1);
      expect(builder.currentRow).toHaveLength(1);
    });

    it('should build keyboard with reply markup', () => {
      const builder = new KeyboardBuilder();

      builder
        .addButton('Button 1', 'data1')
        .addButton('Button 2', 'data2')
        .newRow()
        .addButton('Button 3', 'data3');

      const result = builder.build();

      expect(result).toEqual({
        reply_markup: {
          inline_keyboard: [
            [
              { text: 'Button 1', callback_data: 'data1' },
              { text: 'Button 2', callback_data: 'data2' }
            ],
            [
              { text: 'Button 3', callback_data: 'data3' }
            ]
          ]
        }
      });
    });
  });

  describe('Static helper methods', () => {
    it('should create pagination keyboard', () => {
      const result = KeyboardBuilder.createPaginationKeyboard(2, 5, 'test');

      expect(result.reply_markup.inline_keyboard).toEqual([
        [
          { text: '⬅️ Trước', callback_data: 'test_1' },
          { text: '2/5', callback_data: 'noop' },
          { text: 'Sau ➡️', callback_data: 'test_3' }
        ]
      ]);
    });

    it('should create pagination keyboard for first page', () => {
      const result = KeyboardBuilder.createPaginationKeyboard(1, 3);

      expect(result.reply_markup.inline_keyboard[0]).toEqual([
        { text: '1/3', callback_data: 'noop' },
        { text: 'Sau ➡️', callback_data: 'page_2' }
      ]);
    });

    it('should create pagination keyboard for last page', () => {
      const result = KeyboardBuilder.createPaginationKeyboard(3, 3);

      expect(result.reply_markup.inline_keyboard[0]).toEqual([
        { text: '⬅️ Trước', callback_data: 'page_2' },
        { text: '3/3', callback_data: 'noop' }
      ]);
    });

    it('should throw error for invalid pagination parameters', () => {
      expect(() => KeyboardBuilder.createPaginationKeyboard(0, 5)).toThrow('Invalid pagination parameters');
      expect(() => KeyboardBuilder.createPaginationKeyboard(6, 5)).toThrow('Invalid pagination parameters');
      expect(() => KeyboardBuilder.createPaginationKeyboard(1, 0)).toThrow('Invalid pagination parameters');
    });

    it('should create number keyboard', () => {
      const result = KeyboardBuilder.createNumberKeyboard(0, 9, 5);

      expect(result.reply_markup.inline_keyboard).toHaveLength(2);
      expect(result.reply_markup.inline_keyboard[0]).toHaveLength(5);
      expect(result.reply_markup.inline_keyboard[1]).toHaveLength(5);

      expect(result.reply_markup.inline_keyboard[0][0]).toEqual({
        text: '00',
        callback_data: 'number_00'
      });
      expect(result.reply_markup.inline_keyboard[1][4]).toEqual({
        text: '09',
        callback_data: 'number_09'
      });
    });

    it('should create quick actions keyboard', () => {
      const result = KeyboardBuilder.createQuickActionsKeyboard();

      expect(result.reply_markup.inline_keyboard).toEqual([
        [
          { text: '🎯 Dự đoán Lô', callback_data: 'cmd_dukienlo' },
          { text: '🎲 Dự đoán Đề', callback_data: 'cmd_dukiende' }
        ],
        [
          { text: '📊 Lịch sử', callback_data: 'cmd_lichsu' },
          { text: '📈 Xu hướng', callback_data: 'cmd_trends' }
        ],
        [
          { text: '❓ Trợ giúp', callback_data: 'cmd_help' }
        ]
      ]);
    });

    it('should create trend type keyboard', () => {
      const result = KeyboardBuilder.createTrendTypeKeyboard();

      expect(result.reply_markup.inline_keyboard).toEqual([
        [
          { text: '📈 Xu hướng Lô', callback_data: 'trend_lo' },
          { text: '📉 Xu hướng Đề', callback_data: 'trend_de' }
        ],
        [
          { text: '🔙 Quay lại', callback_data: 'back_main' }
        ]
      ]);
    });

    it('should create history period keyboard', () => {
      const result = KeyboardBuilder.createHistoryPeriodKeyboard();

      expect(result.reply_markup.inline_keyboard).toEqual([
        [
          { text: '📅 Hôm nay', callback_data: 'history_today' },
          { text: '📅 Tuần này', callback_data: 'history_week' }
        ],
        [
          { text: '📅 Tháng này', callback_data: 'history_month' },
          { text: '📅 Tùy chỉnh', callback_data: 'history_custom' }
        ],
        [
          { text: '🔙 Quay lại', callback_data: 'back_main' }
        ]
      ]);
    });

    it('should create confirmation keyboard', () => {
      const result = KeyboardBuilder.createConfirmationKeyboard('delete', 'item123');

      expect(result.reply_markup.inline_keyboard).toEqual([
        [
          { text: '✅ Xác nhận', callback_data: 'confirm_delete_item123' },
          { text: '❌ Hủy bỏ', callback_data: 'cancel_delete' }
        ],
        [
          { text: '🔙 Quay lại', callback_data: 'back_main' }
        ]
      ]);
    });

    it('should create settings keyboard', () => {
      const result = KeyboardBuilder.createSettingsKeyboard();

      expect(result.reply_markup.inline_keyboard).toEqual([
        [
          { text: '🔔 Thông báo', callback_data: 'settings_notifications' },
          { text: '🌐 Ngôn ngữ', callback_data: 'settings_language' }
        ],
        [
          { text: '⏰ Lịch trình', callback_data: 'settings_schedule' },
          { text: '📊 Thống kê', callback_data: 'settings_stats' }
        ],
        [
          { text: '🔙 Quay lại', callback_data: 'back_main' }
        ]
      ]);
    });

    it('should create back button', () => {
      const result = KeyboardBuilder.createBackButton('settings');

      expect(result.reply_markup.inline_keyboard).toEqual([
        [
          { text: '🔙 Quay lại', callback_data: 'back_settings' }
        ]
      ]);
    });

    it('should create empty keyboard', () => {
      const result = KeyboardBuilder.createEmptyKeyboard();

      expect(result).toEqual({
        reply_markup: {
          inline_keyboard: []
        }
      });
    });

    it('should create yes/no keyboard', () => {
      const result = KeyboardBuilder.createYesNoKeyboard('yes_action', 'no_action');

      expect(result.reply_markup.inline_keyboard).toEqual([
        [
          { text: '✅ Có', callback_data: 'yes_action' },
          { text: '❌ Không', callback_data: 'no_action' }
        ]
      ]);
    });

    it('should create options keyboard', () => {
      const options = ['Option 1', 'Option 2', 'Option 3', 'Option 4'];
      const result = KeyboardBuilder.createOptionsKeyboard(options, 'opt', 2);

      expect(result.reply_markup.inline_keyboard).toEqual([
        [
          { text: 'Option 1', callback_data: 'opt_0' },
          { text: 'Option 2', callback_data: 'opt_1' }
        ],
        [
          { text: 'Option 3', callback_data: 'opt_2' },
          { text: 'Option 4', callback_data: 'opt_3' }
        ]
      ]);
    });

    it('should create options keyboard with objects', () => {
      const options = [
        { text: 'First Option' },
        { text: 'Second Option' }
      ];
      const result = KeyboardBuilder.createOptionsKeyboard(options);

      expect(result.reply_markup.inline_keyboard).toEqual([
        [
          { text: 'First Option', callback_data: 'option_0' },
          { text: 'Second Option', callback_data: 'option_1' }
        ]
      ]);
    });
  });

  describe('Legacy static methods', () => {
    it('should create main menu', () => {
      const result = KeyboardBuilder.createMainMenu();

      expect(result.reply_markup.inline_keyboard).toEqual([
        [
          { text: '🎯 Dự đoán Lô', callback_data: 'predict_lo' },
          { text: '🎯 Dự đoán Đề', callback_data: 'predict_de' }
        ],
        [
          { text: '📊 Xu hướng Lô', callback_data: 'trends_lo' },
          { text: '📊 Xu hướng Đề', callback_data: 'trends_de' }
        ],
        [
          { text: '📋 Lịch sử', callback_data: 'history' },
          { text: '❓ Trợ giúp', callback_data: 'help' }
        ]
      ]);
    });

    it('should create prediction options', () => {
      const result = KeyboardBuilder.createPredictionOptions();

      expect(result.reply_markup.inline_keyboard).toEqual([
        [
          { text: '🔥 Số nóng', callback_data: 'hot_numbers' },
          { text: '❄️ Số lạnh', callback_data: 'cold_numbers' }
        ],
        [
          { text: '📈 Phân tích AI', callback_data: 'ai_analysis' },
          { text: '📊 Thống kê', callback_data: 'statistics' }
        ],
        [
          { text: '🔙 Quay lại', callback_data: 'main_menu' }
        ]
      ]);
    });

    it('should create number analysis keyboard', () => {
      const result = KeyboardBuilder.createNumberAnalysisKeyboard('25');

      expect(result.reply_markup.inline_keyboard).toEqual([
        [
          { text: '📊 Tần suất', callback_data: 'freq_25' },
          { text: '📈 Xu hướng', callback_data: 'trend_25' }
        ],
        [
          { text: '🔍 Chi tiết', callback_data: 'detail_25' },
          { text: '🔙 Quay lại', callback_data: 'main_menu' }
        ]
      ]);
    });
  });
});