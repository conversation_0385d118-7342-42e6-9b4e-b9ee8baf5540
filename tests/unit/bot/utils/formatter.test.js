const MessageFormatter = require('../../../../lib/bot/utils/formatter');

// Mock logger
const mockLogger = {
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn()
};

jest.mock('../../../../lib/logger', () => mockLogger);

describe('MessageFormatter', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('HTML escaping', () => {
    it('should escape HTML characters', () => {
      expect(MessageFormatter.escapeHtml('<script>alert("xss")</script>'))
        .toBe('&lt;script&gt;alert("xss")&lt;/script&gt;');

      expect(MessageFormatter.escapeHtml('A & B')).toBe('A &amp; B');
      expect(MessageFormatter.escapeHtml('')).toBe('');
      expect(MessageFormatter.escapeHtml(null)).toBe('');
    });

    it('should format HTML tags', () => {
      expect(MessageFormatter.bold('test')).toBe('<b>test</b>');
      expect(MessageFormatter.italic('test')).toBe('<i>test</i>');
      expect(MessageFormatter.code('test')).toBe('<code>test</code>');
      expect(MessageFormatter.pre('test')).toBe('<pre>test</pre>');
      expect(MessageFormatter.link('test', 'http://example.com'))
        .toBe('<a href="http://example.com">test</a>');
    });

    it('should escape content in HTML tags', () => {
      expect(MessageFormatter.bold('<script>')).toBe('<b>&lt;script&gt;</b>');
      expect(MessageFormatter.code('A & B')).toBe('<code>A &amp; B</code>');
    });
  });

  describe('formatNumber', () => {
    it('should format numbers with leading zeros', () => {
      expect(MessageFormatter.formatNumber(5)).toBe('<code>05</code>');
      expect(MessageFormatter.formatNumber(25)).toBe('<code>25</code>');
      expect(MessageFormatter.formatNumber('7')).toBe('<code>07</code>');
    });
  });

  describe('formatConfidence', () => {
    it('should format confidence with appropriate emoji', () => {
      expect(MessageFormatter.formatConfidence(85)).toBe('🟢 85%');
      expect(MessageFormatter.formatConfidence(65)).toBe('🟡 65%');
      expect(MessageFormatter.formatConfidence(35)).toBe('🔴 35%');
      expect(MessageFormatter.formatConfidence(70)).toBe('🟢 70%');
      expect(MessageFormatter.formatConfidence(50)).toBe('🟡 50%');
    });
  });

  describe('formatPrediction', () => {
    it('should format prediction data', () => {
      const prediction = {
        type: 'lo',
        date: new Date('2024-01-15'),
        predictions: [
          { number: '25', confidence: 85 },
          { number: '07', confidence: 70 }
        ],
        modelVersion: 'v1.0'
      };

      const result = MessageFormatter.formatPrediction(prediction);

      expect(result).toContain('🎯 <b>Dự Đoán Số Lô</b>');
      expect(result).toContain('📅 Ngày: <code>15/1/2024</code>');
      expect(result).toContain('1. <code>25</code> - 🟢 85%');
      expect(result).toContain('2. <code>07</code> - 🟢 70%');
      expect(result).toContain('🤖 Model: <code>v1.0</code>');
      expect(result).toContain('💡 Lưu ý');
    });

    it('should handle empty predictions', () => {
      const prediction = {
        type: 'de',
        date: new Date(),
        predictions: []
      };

      const result = MessageFormatter.formatPrediction(prediction);

      expect(result).toContain('Không có dự đoán cho hôm nay');
    });

    it('should handle null prediction', () => {
      const result = MessageFormatter.formatPrediction(null);
      expect(result).toBe('Không có dự đoán khả dụng.');
    });

    it('should handle formatting errors', () => {
      const prediction = { invalid: 'data' };

      const result = MessageFormatter.formatPrediction(prediction);

      expect(result).toBe('Không có dự đoán khả dụng.');
    });
  });

  describe('formatHistory', () => {
    it('should format history data', () => {
      const history = [
        {
          date: new Date('2024-01-15'),
          prizes: { special: '12345' },
          numbers: {
            lo: ['25', '07', '89'],
            de: ['25', '07']
          }
        },
        {
          date: new Date('2024-01-14'),
          prizes: { special: '67890' },
          numbers: {
            lo: ['15', '33'],
            de: ['15']
          }
        }
      ];

      const result = MessageFormatter.formatHistory(history);

      expect(result).toContain('📊 <b>Lịch Sử Kết Quả</b>');
      expect(result).toContain('📅 <b>15/1/2024</b>');
      expect(result).toContain('🏆 Đặc biệt: <code>12345</code>');
      expect(result).toContain('🎲 Lô: <code>25</code> <code>07</code> <code>89</code>');
      expect(result).toContain('🎯 Đề: <code>25</code> <code>07</code>');
    });

    it('should limit results', () => {
      const history = Array.from({ length: 10 }, (_, i) => ({
        date: new Date(`2024-01-${i + 1}`),
        prizes: { special: '12345' },
        numbers: { lo: ['25'], de: ['25'] }
      }));

      const result = MessageFormatter.formatHistory(history, 3);

      expect(result).toContain('... và 7 kết quả khác');
    });

    it('should handle empty history', () => {
      const result = MessageFormatter.formatHistory([]);
      expect(result).toBe('Không có dữ liệu lịch sử.');
    });
  });

  describe('formatTrends', () => {
    it('should format trend data', () => {
      const trends = {
        type: 'lo',
        period: 'Tuần qua',
        hot: [
          { number: '25', count: 5 },
          { number: '07', frequency: 3 }
        ],
        cold: [
          { number: '89', frequency: 0 },
          { number: '15', frequency: 1 }
        ]
      };

      const result = MessageFormatter.formatTrends(trends);

      expect(result).toContain('📈 <b>Xu Hướng Số Lô</b>');
      expect(result).toContain('⏰ Thời gian: Tuần qua');
      expect(result).toContain('🔥 <b>Số Nóng (xuất hiện nhiều):</b>');
      expect(result).toContain('1. <code>25</code> (5 lần)');
      expect(result).toContain('❄️ <b>Số Lạnh (ít xuất hiện):</b>');
      expect(result).toContain('1. <code>89</code> (0 lần)');
    });

    it('should handle null trends', () => {
      const result = MessageFormatter.formatTrends(null);
      expect(result).toBe('Không có dữ liệu xu hướng.');
    });
  });

  describe('formatNumberAnalysis', () => {
    it('should format number analysis', () => {
      const analysis = {
        number: '25',
        statistics: {
          totalAppearances: 15,
          lastAppearance: new Date('2024-01-15'),
          frequency: 12.5
        },
        trends: {
          isHot: true,
          streak: 3
        },
        predictions: {
          nextAppearance: 75
        }
      };

      const result = MessageFormatter.formatNumberAnalysis(analysis);

      expect(result).toContain('🔍 <b>Phân Tích Số <code>25</code></b>');
      expect(result).toContain('📊 <b>Thống Kê:</b>');
      expect(result).toContain('• Tổng lần xuất hiện: <code>15</code>');
      expect(result).toContain('• Lần cuối xuất hiện: <code>15/1/2024</code>');
      expect(result).toContain('• Tần suất: <code>12.50</code>%');
      expect(result).toContain('📈 <b>Xu Hướng:</b>');
      expect(result).toContain('• Trạng thái: 🔥 Nóng');
      expect(result).toContain('• Chuỗi: <code>3</code> ngày');
      expect(result).toContain('🎯 <b>Dự Đoán:</b>');
      expect(result).toContain('• Khả năng xuất hiện: 🟢 75%');
    });

    it('should handle cold numbers', () => {
      const analysis = {
        number: '89',
        trends: { isHot: false }
      };

      const result = MessageFormatter.formatNumberAnalysis(analysis);
      expect(result).toContain('• Trạng thái: ❄️ Lạnh');
    });

    it('should handle null analysis', () => {
      const result = MessageFormatter.formatNumberAnalysis(null);
      expect(result).toBe('Không có dữ liệu phân tích.');
    });
  });

  describe('formatHelp', () => {
    it('should format help message', () => {
      const result = MessageFormatter.formatHelp();

      expect(result).toContain('🤖 <b>Hướng Dẫn Sử Dụng Bot</b>');
      expect(result).toContain('<b>Lệnh Dự Đoán:</b>');
      expect(result).toContain('• /dukienlo - Dự đoán số lô');
      expect(result).toContain('• /dukiende - Dự đoán số đề');
      expect(result).toContain('<b>Lệnh Lịch Sử:</b>');
      expect(result).toContain('• /lichsu - Xem kết quả gần đây');
      expect(result).toContain('<b>Lệnh Xu Hướng:</b>');
      expect(result).toContain('• /xuhuonglo - Xu hướng số lô');
      expect(result).toContain('• /xuhuongde - Xu hướng số đề');
      expect(result).toContain('<b>Lệnh Phân Tích:</b>');
      expect(result).toContain('• /number [số] - Phân tích số cụ thể');
      expect(result).toContain('Ví dụ: /number 25');
      expect(result).toContain('<b>Lệnh Khác:</b>');
      expect(result).toContain('• /help - Hiển thị hướng dẫn này');
      expect(result).toContain('<i>Lưu ý: Tất cả dự đoán chỉ mang tính chất tham khảo.</i>');
    });
  });

  describe('formatError', () => {
    it('should format error messages', () => {
      const error = new Error('Test error');
      const result = MessageFormatter.formatError(error, 'prediction');

      expect(result).toBe('❌ Đã xảy ra lỗi (prediction). Vui lòng thử lại sau.');
    });

    it('should format error without context', () => {
      const error = new Error('Test error');
      const result = MessageFormatter.formatError(error);

      expect(result).toBe('❌ Đã xảy ra lỗi. Vui lòng thử lại sau.');
    });
  });

  describe('message utilities', () => {
    it('should truncate long messages', () => {
      const longMessage = 'a'.repeat(5000);
      const result = MessageFormatter.truncateMessage(longMessage, 100);

      expect(result).toHaveLength(100);
      expect(result.endsWith('...')).toBe(true);
    });

    it('should not truncate short messages', () => {
      const shortMessage = 'short message';
      const result = MessageFormatter.truncateMessage(shortMessage, 100);

      expect(result).toBe(shortMessage);
    });

    it('should split long messages', () => {
      const longMessage = 'line1\n' + 'a'.repeat(5000) + '\nline3';
      const parts = MessageFormatter.splitLongMessage(longMessage, 100);

      expect(parts.length).toBeGreaterThan(1); // Should split into multiple parts
      expect(parts[0]).toBe('line1');
      expect(parts[parts.length - 1]).toBe('line3');
    });

    it('should return single part for short messages', () => {
      const shortMessage = 'short message';
      const parts = MessageFormatter.splitLongMessage(shortMessage, 100);

      expect(parts).toEqual([shortMessage]);
    });
  });
});