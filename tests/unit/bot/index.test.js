const BotHandler = require('../../../lib/bot');

// Mock logger
const mockLogger = {
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn()
};

// Mock dependencies
jest.mock('node-telegram-bot-api');
jest.mock('../../../lib/logger', () => mockLogger);
jest.mock('../../../lib/bot/middleware/auth');
jest.mock('../../../lib/bot/middleware/rateLimit');
jest.mock('../../../lib/bot/middleware/logging');

const TelegramBot = require('node-telegram-bot-api');

describe('BotHandler', () => {
  let botHandler;
  let mockBot;
  const testToken = 'test-token';

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();

    // Mock TelegramBot instance
    mockBot = {
      on: jest.fn(),
      stopPolling: jest.fn(),
      sendMessage: jest.fn(),
      editMessageText: jest.fn(),
      deleteMessage: jest.fn(),
      answerCallbackQuery: jest.fn(),
      emit: jest.fn()
    };

    TelegramBot.mockImplementation(() => mockBot);

    botHandler = new BotHandler(testToken);
  });

  describe('Constructor', () => {
    it('should create bot handler with token', () => {
      expect(botHandler.token).toBe(testToken);
      expect(botHandler.isStarted).toBe(false);
      expect(botHandler.commands).toBeInstanceOf(Map);
      expect(botHandler.middleware).toBeInstanceOf(Array);
    });

    it('should throw error without token', () => {
      expect(() => new BotHandler()).toThrow('Bot token is required');
    });

    it('should accept options', () => {
      const options = { polling: false };
      const handler = new BotHandler(testToken, options);
      expect(handler.options.polling).toBe(false);
    });
  });

  describe('start()', () => {
    it('should start bot successfully', async () => {
      await botHandler.start();

      expect(TelegramBot).toHaveBeenCalledWith(testToken, { polling: true });
      expect(mockBot.on).toHaveBeenCalledWith('error', expect.any(Function));
      expect(mockBot.on).toHaveBeenCalledWith('polling_error', expect.any(Function));
      expect(mockBot.on).toHaveBeenCalledWith('message', expect.any(Function));
      expect(mockBot.on).toHaveBeenCalledWith('callback_query', expect.any(Function));
      expect(botHandler.isStarted).toBe(true);
      expect(mockLogger.info).toHaveBeenCalledWith('Telegram bot started successfully');
    });

    it('should not start if already started', async () => {
      botHandler.isStarted = true;

      await botHandler.start();

      expect(TelegramBot).not.toHaveBeenCalled();
      expect(mockLogger.warn).toHaveBeenCalledWith('Bot is already started');
    });

    it('should handle start errors', async () => {
      const error = new Error('Start failed');
      TelegramBot.mockImplementation(() => {
        throw error;
      });

      await expect(botHandler.start()).rejects.toThrow('Start failed');
      expect(mockLogger.error).toHaveBeenCalledWith('Failed to start bot:', error);
    });
  });

  describe('stop()', () => {
    beforeEach(async () => {
      await botHandler.start();
    });

    it('should stop bot successfully', async () => {
      await botHandler.stop();

      expect(mockBot.stopPolling).toHaveBeenCalled();
      expect(botHandler.isStarted).toBe(false);
      expect(botHandler.bot).toBe(null);
      expect(mockLogger.info).toHaveBeenCalledWith('Telegram bot stopped successfully');
    });

    it('should not stop if not started', async () => {
      botHandler.isStarted = false;
      botHandler.bot = null;

      await botHandler.stop();

      expect(mockBot.stopPolling).not.toHaveBeenCalled();
      expect(mockLogger.warn).toHaveBeenCalledWith('Bot is not started');
    });

    it('should handle stop errors', async () => {
      const error = new Error('Stop failed');
      mockBot.stopPolling.mockRejectedValue(error);

      await expect(botHandler.stop()).rejects.toThrow('Stop failed');
      expect(mockLogger.error).toHaveBeenCalledWith('Failed to stop bot:', error);
    });
  });

  describe('registerCommand()', () => {
    it('should register command successfully', () => {
      const handler = jest.fn();

      botHandler.registerCommand('test', handler);

      expect(botHandler.commands.get('test')).toBe(handler);
      expect(mockLogger.info).toHaveBeenCalledWith('Registered command: /test');
    });

    it('should register command without leading slash', () => {
      const handler = jest.fn();

      botHandler.registerCommand('/test', handler);

      expect(botHandler.commands.get('test')).toBe(handler);
    });

    it('should throw error for invalid command', () => {
      expect(() => botHandler.registerCommand('', jest.fn())).toThrow('Command must be a non-empty string');
      expect(() => botHandler.registerCommand(null, jest.fn())).toThrow('Command must be a non-empty string');
    });

    it('should throw error for invalid handler', () => {
      expect(() => botHandler.registerCommand('test', null)).toThrow('Handler must be a function');
      expect(() => botHandler.registerCommand('test', 'not-function')).toThrow('Handler must be a function');
    });
  });

  describe('handleMessage()', () => {
    let mockMessage;
    let mockHandler;

    beforeEach(async () => {
      await botHandler.start();
      mockHandler = jest.fn();
      botHandler.registerCommand('test', mockHandler);

      mockMessage = {
        chat: { id: 123 },
        from: { id: 456, username: 'testuser' },
        text: '/test arg1 arg2'
      };
    });

    it('should handle command message', async () => {
      await botHandler.handleMessage(mockMessage);

      expect(mockHandler).toHaveBeenCalledWith(expect.objectContaining({
        message: mockMessage,
        chatId: 123,
        userId: 456,
        text: '/test arg1 arg2',
        isCommand: true,
        command: 'test',
        args: ['arg1', 'arg2']
      }));
    });

    it('should handle command with bot username', async () => {
      mockMessage.text = '/test@botname arg1';

      await botHandler.handleMessage(mockMessage);

      expect(mockHandler).toHaveBeenCalledWith(expect.objectContaining({
        command: 'test',
        args: ['arg1']
      }));
    });

    it('should handle unknown command', async () => {
      mockMessage.text = '/unknown';
      mockBot.sendMessage = jest.fn();

      await botHandler.handleMessage(mockMessage);

      expect(mockBot.sendMessage).toHaveBeenCalledWith(
        123,
        'Lệnh không hợp lệ: /unknown\nGửi /help để xem danh sách lệnh có sẵn.'
      );
    });

    it('should handle non-command message', async () => {
      mockMessage.text = 'hello';

      await botHandler.handleMessage(mockMessage);

      expect(mockHandler).not.toHaveBeenCalled();
    });

    it('should handle message errors', async () => {
      const error = new Error('Handler error');
      mockHandler.mockRejectedValue(error);
      mockBot.sendMessage = jest.fn();

      await botHandler.handleMessage(mockMessage);

      expect(mockLogger.error).toHaveBeenCalledWith('Error handling message:', error);
      expect(mockBot.sendMessage).toHaveBeenCalledWith(
        123,
        'Đã xảy ra lỗi khi xử lý tin nhắn của bạn. Vui lòng thử lại sau.'
      );
    });
  });

  describe('handleCallbackQuery()', () => {
    let mockQuery;

    beforeEach(async () => {
      await botHandler.start();

      mockQuery = {
        id: 'query123',
        from: { id: 456 },
        message: { chat: { id: 123 } },
        data: 'test_data'
      };
    });

    it('should handle callback query', async () => {
      await botHandler.handleCallbackQuery(mockQuery);

      expect(mockBot.answerCallbackQuery).toHaveBeenCalledWith('query123');
      expect(mockBot.emit).toHaveBeenCalledWith('callback_query_handled', expect.objectContaining({
        callbackQuery: mockQuery,
        chatId: 123,
        userId: 456,
        data: 'test_data'
      }));
    });

    it('should handle callback query errors', async () => {
      const error = new Error('Callback error');
      mockBot.answerCallbackQuery.mockRejectedValue(error);

      await botHandler.handleCallbackQuery(mockQuery);

      expect(mockLogger.error).toHaveBeenCalledWith('Error handling callback query:', error);
    });
  });

  describe('sendMessage()', () => {
    beforeEach(async () => {
      await botHandler.start();
    });

    it('should send message successfully', async () => {
      const result = { message_id: 123 };
      mockBot.sendMessage.mockResolvedValue(result);

      const response = await botHandler.sendMessage(123, 'test message');

      expect(mockBot.sendMessage).toHaveBeenCalledWith(123, 'test message', {
        parse_mode: 'HTML',
        disable_web_page_preview: true
      });
      expect(response).toBe(result);
    });

    it('should send message with custom options', async () => {
      const options = { parse_mode: 'Markdown' };

      await botHandler.sendMessage(123, 'test', options);

      expect(mockBot.sendMessage).toHaveBeenCalledWith(123, 'test', {
        parse_mode: 'Markdown',
        disable_web_page_preview: true
      });
    });

    it('should throw error if bot not initialized', async () => {
      botHandler.bot = null;

      await expect(botHandler.sendMessage(123, 'test')).rejects.toThrow('Bot is not initialized');
    });

    it('should handle send message errors', async () => {
      const error = new Error('Send failed');
      mockBot.sendMessage.mockRejectedValue(error);

      await expect(botHandler.sendMessage(123, 'test')).rejects.toThrow('Send failed');
      expect(mockLogger.error).toHaveBeenCalledWith('Failed to send message:', error);
    });
  });

  describe('sendScheduledMessage()', () => {
    beforeEach(async () => {
      await botHandler.start();
    });

    it('should send scheduled messages to multiple groups', async () => {
      const groupIds = [123, 456, 789];
      const message = 'scheduled message';
      mockBot.sendMessage.mockResolvedValue({ message_id: 1 });

      const result = await botHandler.sendScheduledMessage(groupIds, message);

      expect(mockBot.sendMessage).toHaveBeenCalledTimes(3);
      expect(result.results).toHaveLength(3);
      expect(result.errors).toHaveLength(0);
    });

    it('should handle partial failures', async () => {
      const groupIds = [123, 456];
      const message = 'test';
      mockBot.sendMessage
        .mockResolvedValueOnce({ message_id: 1 })
        .mockRejectedValueOnce(new Error('Send failed'));

      const result = await botHandler.sendScheduledMessage(groupIds, message);

      expect(result.results).toHaveLength(1);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].groupId).toBe(456);
    });
  });

  describe('utility methods', () => {
    beforeEach(async () => {
      await botHandler.start();
    });

    it('should edit message', async () => {
      await botHandler.editMessage(123, 456, 'new text');

      expect(mockBot.editMessageText).toHaveBeenCalledWith('new text', {
        chat_id: 123,
        message_id: 456,
        parse_mode: 'HTML',
        disable_web_page_preview: true
      });
    });

    it('should delete message', async () => {
      await botHandler.deleteMessage(123, 456);

      expect(mockBot.deleteMessage).toHaveBeenCalledWith(123, 456);
    });

    it('should return bot instance', () => {
      expect(botHandler.getBot()).toBe(mockBot);
    });

    it('should check if running', () => {
      expect(botHandler.isRunning()).toBe(true);

      botHandler.isStarted = false;
      expect(botHandler.isRunning()).toBe(false);
    });
  });

  describe('middleware', () => {
    it('should add middleware', () => {
      const middleware = jest.fn();

      botHandler.use(middleware);

      expect(botHandler.middleware).toContain(middleware);
    });

    it('should throw error for invalid middleware', () => {
      expect(() => botHandler.use('not-function')).toThrow('Middleware must be a function');
    });

    it('should run middleware chain', async () => {
      const middleware1 = jest.fn();
      const middleware2 = jest.fn();

      botHandler.use(middleware1);
      botHandler.use(middleware2);

      const ctx = { test: true };
      await botHandler.runMiddleware(ctx);

      expect(middleware1).toHaveBeenCalledWith(ctx);
      expect(middleware2).toHaveBeenCalledWith(ctx);
    });

    it('should handle middleware errors', async () => {
      const error = new Error('Middleware error');
      const middleware = jest.fn().mockRejectedValue(error);

      botHandler.use(middleware);

      const ctx = { test: true };
      await expect(botHandler.runMiddleware(ctx)).rejects.toThrow('Middleware error');
      expect(mockLogger.error).toHaveBeenCalledWith('Middleware error:', error);
    });
  });
});