const loggingMiddleware = require('../../../../lib/bot/middleware/logging');

// Mock logger
const mockLogger = {
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn()
};

jest.mock('../../../../lib/logger', () => mockLogger);

describe('Logging Middleware', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should log message interactions', async () => {
    const ctx = {
      message: {
        chat: { id: 123, type: 'private' },
        from: { id: 456, username: 'testuser' },
        text: '/test command'
      },
      userId: 456,
      chatId: 123,
      isCommand: true,
      command: 'test',
      text: '/test command'
    };

    await loggingMiddleware(ctx);

    expect(mockLogger.info).toHaveBeenCalledWith('Bot interaction:', {
      timestamp: expect.any(String),
      type: 'message',
      userId: 456,
      chatId: 123,
      chatType: 'private',
      username: 'testuser',
      isCommand: true,
      command: 'test',
      messageLength: 13
    });

    expect(mockLogger.info).toHaveBeenCalledWith(
      'Command usage: /test by user 456 in chat 123'
    );
  });

  it('should log non-command messages', async () => {
    const ctx = {
      message: {
        chat: { id: 123, type: 'group' },
        from: { id: 456 },
        text: 'hello world'
      },
      userId: 456,
      chatId: 123,
      isCommand: false,
      command: null,
      text: 'hello world'
    };

    await loggingMiddleware(ctx);

    expect(mockLogger.info).toHaveBeenCalledWith('Bot interaction:', {
      timestamp: expect.any(String),
      type: 'message',
      userId: 456,
      chatId: 123,
      chatType: 'group',
      username: undefined,
      isCommand: false,
      command: null,
      messageLength: 11
    });

    // Should not log command usage for non-commands
    expect(mockLogger.info).not.toHaveBeenCalledWith(
      expect.stringContaining('Command usage:')
    );
  });

  it('should log callback queries', async () => {
    const ctx = {
      callbackQuery: {
        from: { id: 456 },
        message: { chat: { id: 123 } },
        data: 'test_callback'
      },
      userId: 456,
      chatId: 123,
      data: 'test_callback'
    };

    await loggingMiddleware(ctx);

    expect(mockLogger.info).toHaveBeenCalledWith('Callback query:', {
      timestamp: expect.any(String),
      type: 'callback_query',
      userId: 456,
      chatId: 123,
      data: 'test_callback'
    });
  });

  it('should handle messages without text', async () => {
    const ctx = {
      message: {
        chat: { id: 123, type: 'private' },
        from: { id: 456, username: 'testuser' }
      },
      userId: 456,
      chatId: 123,
      isCommand: false,
      command: null,
      text: null
    };

    await loggingMiddleware(ctx);

    expect(mockLogger.info).toHaveBeenCalledWith('Bot interaction:', {
      timestamp: expect.any(String),
      type: 'message',
      userId: 456,
      chatId: 123,
      chatType: 'private',
      username: 'testuser',
      isCommand: false,
      command: null,
      messageLength: 0
    });
  });

  it('should handle missing user info gracefully', async () => {
    const ctx = {
      message: {
        chat: { id: 123 },
        from: null
      },
      userId: null,
      chatId: 123,
      isCommand: false,
      command: null,
      text: 'test'
    };

    await loggingMiddleware(ctx);

    expect(mockLogger.info).toHaveBeenCalledWith('Bot interaction:', {
      timestamp: expect.any(String),
      type: 'message',
      userId: null,
      chatId: 123,
      chatType: undefined,
      username: undefined,
      isCommand: false,
      command: null,
      messageLength: 4
    });
  });

  it('should not throw errors on logging failures', async () => {
    const ctx = {
      message: {
        chat: { id: 123 },
        from: { id: 456 }
      },
      userId: 456,
      chatId: 123,
      isCommand: false,
      command: null,
      text: 'test'
    };

    // Mock logger to throw error
    mockLogger.info.mockImplementation(() => {
      throw new Error('Logging failed');
    });

    // Should not throw
    await expect(loggingMiddleware(ctx)).resolves.toBeUndefined();

    expect(mockLogger.error).toHaveBeenCalledWith(
      'Logging middleware error:',
      expect.any(Error)
    );
  });

  it('should handle both message and callback query in same context', async () => {
    const ctx = {
      message: {
        chat: { id: 123 },
        from: { id: 456 }
      },
      callbackQuery: {
        from: { id: 456 },
        message: { chat: { id: 123 } },
        data: 'test'
      },
      userId: 456,
      chatId: 123,
      isCommand: false,
      command: null,
      text: 'test',
      data: 'test'
    };

    await loggingMiddleware(ctx);

    expect(mockLogger.info).toHaveBeenCalledTimes(2);
    expect(mockLogger.info).toHaveBeenCalledWith('Bot interaction:', expect.objectContaining({
      type: 'message'
    }));
    expect(mockLogger.info).toHaveBeenCalledWith('Callback query:', expect.objectContaining({
      type: 'callback_query'
    }));
  });
});