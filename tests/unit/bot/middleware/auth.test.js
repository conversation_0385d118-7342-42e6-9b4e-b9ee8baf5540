const authMiddleware = require('../../../../lib/bot/middleware/auth');

// Mock logger
const mockLogger = {
  logInfo: jest.fn(),
  logError: jest.fn()
};

jest.mock('../../../../lib/logger', () => () => mockLogger);

describe('Auth Middleware', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should skip auth for non-command messages', async () => {
    const ctx = {
      isCommand: false,
      userId: 123
    };

    await authMiddleware(ctx);

    expect(ctx.user).toBeUndefined();
    expect(ctx.chat).toBeUndefined();
  });

  it('should add user info to context for commands', async () => {
    const ctx = {
      isCommand: true,
      userId: 456,
      chatId: 789,
      command: 'test',
      message: {
        from: {
          id: 456,
          username: 'testuser',
          first_name: 'Test',
          last_name: 'User',
          is_bot: false
        },
        chat: {
          id: 789,
          type: 'private',
          title: 'Test Chat'
        }
      }
    };

    await authMiddleware(ctx);

    expect(ctx.user).toEqual({
      id: 456,
      username: 'testuser',
      firstName: 'Test',
      lastName: 'User',
      isBot: false
    });

    expect(ctx.chat).toEqual({
      id: 789,
      type: 'private',
      title: 'Test Chat'
    });

    expect(mockLogger.logInfo).toHaveBeenCalledWith(
      'User 456 (testuser) used command /test in chat 789'
    );
  });

  it('should handle user without username', async () => {
    const ctx = {
      isCommand: true,
      userId: 456,
      chatId: 789,
      command: 'test',
      message: {
        from: {
          id: 456,
          first_name: 'Test',
          is_bot: false
        },
        chat: {
          id: 789,
          type: 'private'
        }
      }
    };

    await authMiddleware(ctx);

    expect(ctx.user.username).toBeUndefined();
    expect(mockLogger.logInfo).toHaveBeenCalledWith(
      'User 456 (no username) used command /test in chat 789'
    );
  });

  it('should block bot users', async () => {
    const ctx = {
      isCommand: true,
      userId: 456,
      message: {
        from: {
          id: 456,
          is_bot: true
        }
      }
    };

    await expect(authMiddleware(ctx)).rejects.toThrow('Bot users are not allowed');
    expect(mockLogger.logError).toHaveBeenCalledWith('Bot user attempted to use command: 456');
  });

  it('should warn for messages without user ID', async () => {
    const ctx = {
      isCommand: true,
      userId: null
    };

    await authMiddleware(ctx);

    expect(mockLogger.logError).toHaveBeenCalledWith('Message received without user ID');
  });

  it('should handle errors', async () => {
    const ctx = {
      isCommand: true,
      userId: 456,
      message: null // This will cause an error
    };

    await expect(authMiddleware(ctx)).rejects.toThrow();
    expect(mockLogger.logError).toHaveBeenCalledWith('Auth middleware error:', expect.any(Error));
  });
});