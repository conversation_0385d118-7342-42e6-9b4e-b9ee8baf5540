const rateLimitMiddleware = require('../../../../lib/bot/middleware/rateLimit');

// Mock logger
const mockLogger = {
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn()
};

jest.mock('../../../../lib/logger', () => mockLogger);

describe('Rate Limit Middleware', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  it('should skip rate limiting for non-command messages', async () => {
    const ctx = {
      isCommand: false,
      userId: 123,
      bot: { sendMessage: jest.fn() }
    };

    await rateLimitMiddleware(ctx);

    expect(ctx.bot.sendMessage).not.toHaveBeenCalled();
  });

  it('should skip rate limiting for messages without user ID', async () => {
    const ctx = {
      isCommand: true,
      userId: null,
      bot: { sendMessage: jest.fn() }
    };

    await rateLimitMiddleware(ctx);

    expect(ctx.bot.sendMessage).not.toHaveBeenCalled();
  });

  it('should allow first request from user', async () => {
    const ctx = {
      isCommand: true,
      userId: 123,
      chatId: 456,
      bot: { sendMessage: jest.fn() }
    };

    await rateLimitMiddleware(ctx);

    expect(ctx.bot.sendMessage).not.toHaveBeenCalled();
  });

  it('should allow requests within rate limit', async () => {
    const ctx = {
      isCommand: true,
      userId: 123,
      chatId: 456,
      bot: { sendMessage: jest.fn() }
    };

    // Send 5 requests (well within limit)
    for (let i = 0; i < 5; i++) {
      await rateLimitMiddleware(ctx);
    }

    expect(ctx.bot.sendMessage).not.toHaveBeenCalled();
  });

  it('should block requests exceeding rate limit', async () => {
    const ctx = {
      isCommand: true,
      userId: 123,
      chatId: 456,
      bot: { sendMessage: jest.fn() }
    };

    // Send 21 requests (exceeding limit of 20)
    for (let i = 0; i < 21; i++) {
      if (i < 20) {
        await rateLimitMiddleware(ctx);
      } else {
        await expect(rateLimitMiddleware(ctx)).rejects.toThrow('Rate limit exceeded');
      }
    }

    expect(ctx.bot.sendMessage).toHaveBeenCalledWith(
      456,
      expect.stringContaining('Bạn đang gửi lệnh quá nhanh')
    );
    expect(mockLogger.warn).toHaveBeenCalledWith(
      expect.stringContaining('User 123 rate limited')
    );
  });

  it('should reset rate limit after time window', async () => {
    const ctx = {
      isCommand: true,
      userId: 123,
      chatId: 456,
      bot: { sendMessage: jest.fn() }
    };

    // Send 21 requests to trigger rate limit
    for (let i = 0; i < 21; i++) {
      if (i < 20) {
        await rateLimitMiddleware(ctx);
      } else {
        await expect(rateLimitMiddleware(ctx)).rejects.toThrow('Rate limit exceeded');
      }
    }

    // Advance time by more than window (1 minute)
    jest.advanceTimersByTime(61 * 1000);

    // Should allow requests again
    await rateLimitMiddleware(ctx);
    expect(ctx.bot.sendMessage).toHaveBeenCalledTimes(1); // Only the rate limit message
  });

  it('should handle different users independently', async () => {
    const ctx1 = {
      isCommand: true,
      userId: 123,
      chatId: 456,
      bot: { sendMessage: jest.fn() }
    };

    const ctx2 = {
      isCommand: true,
      userId: 789,
      chatId: 456,
      bot: { sendMessage: jest.fn() }
    };

    // User 1 hits rate limit
    for (let i = 0; i < 21; i++) {
      if (i < 20) {
        await rateLimitMiddleware(ctx1);
      } else {
        await expect(rateLimitMiddleware(ctx1)).rejects.toThrow('Rate limit exceeded');
      }
    }

    // User 2 should still be allowed
    await rateLimitMiddleware(ctx2);
    expect(ctx2.bot.sendMessage).not.toHaveBeenCalled();
  });

  it('should handle middleware errors', async () => {
    const ctx = {
      isCommand: true,
      userId: 123,
      chatId: 456,
      bot: { sendMessage: jest.fn().mockRejectedValue(new Error('Send failed')) }
    };

    // Trigger rate limit
    for (let i = 0; i < 21; i++) {
      if (i < 20) {
        await rateLimitMiddleware(ctx);
      } else {
        await expect(rateLimitMiddleware(ctx)).rejects.toThrow('Rate limit exceeded');
      }
    }

    expect(mockLogger.error).toHaveBeenCalledWith(
      'Rate limit middleware error:',
      expect.any(Error)
    );
  });

  it('should cleanup old entries', async () => {
    const ctx = {
      isCommand: true,
      userId: 123,
      chatId: 456,
      bot: { sendMessage: jest.fn() }
    };

    // Make some requests
    await rateLimitMiddleware(ctx);

    // Advance time by cleanup interval (5 minutes)
    jest.advanceTimersByTime(5 * 60 * 1000);

    // This should trigger cleanup
    await rateLimitMiddleware(ctx);

    // No specific assertion needed, just ensuring no errors occur
    expect(ctx.bot.sendMessage).not.toHaveBeenCalled();
  });
});