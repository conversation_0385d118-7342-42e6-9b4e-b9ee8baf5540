const TrendsHandler = require('../../../../lib/bot/handlers/trends');

// Mock dependencies
const MessageFormatter = {
  formatTrends: jest.fn(),
  formatError: jest.fn()
};

jest.mock('../../../../lib/bot/utils/formatter', () => MessageFormatter);
jest.mock('../../../../lib/logger', () => () => ({
  logInfo: jest.fn(),
  logError: jest.fn()
}));

describe('TrendsHandler', () => {
  let trendsHandler;
  let mockPredictionEngine;
  let mockDataManager;
  let mockCtx;

  beforeEach(() => {
    // Mock PredictionEngine
    mockPredictionEngine = {
      getTrends: jest.fn()
    };

    // Mock DataManager
    mockDataManager = {
      getNumberStatistics: jest.fn(),
      getLotteryHistory: jest.fn()
    };

    // Mock Telegram context
    mockCtx = {
      message: { text: '/xuhuonglo' },
      from: { id: 123456 },
      chat: { id: -789012 },
      reply: jest.fn(),
      editMessageText: jest.fn(),
      answerCbQuery: jest.fn(),
      callbackQuery: { data: 'trends:lo:weekly' }
    };

    trendsHandler = new TrendsHandler(mockPredictionEngine, mockDataManager);

    // Reset mocks
    jest.clearAllMocks();
  });

  describe('constructor', () => {
    it('should initialize with correct default values', () => {
      expect(trendsHandler.predictionEngine).toBe(mockPredictionEngine);
      expect(trendsHandler.dataManager).toBe(mockDataManager);
      expect(trendsHandler.defaultPeriod).toBe('weekly');
      expect(trendsHandler.supportedPeriods).toEqual(['daily', 'weekly', 'monthly']);
    });
  });

  describe('handleXuHuongLo', () => {
    const mockTrendsData = {
      hot: [
        { number: '12', frequency: 15, count: 15 },
        { number: '34', frequency: 12, count: 12 }
      ],
      cold: [
        { number: '78', frequency: 2, count: 2 },
        { number: '90', frequency: 1, count: 1 }
      ],
      type: 'lo',
      period: 'weekly'
    };

    beforeEach(() => {
      mockPredictionEngine.getTrends.mockResolvedValue(mockTrendsData);
      mockDataManager.getNumberStatistics.mockResolvedValue([
        { number: '12', count: 15, lastSeen: new Date('2024-01-15') },
        { number: '34', count: 12, lastSeen: new Date('2024-01-14') }
      ]);
      mockDataManager.getLotteryHistory.mockResolvedValue([
        { date: new Date('2024-01-15'), numbers: { lo: ['12', '34'] } }
      ]);
      MessageFormatter.formatTrends.mockReturnValue('Formatted trends message');
    });

    it('should handle lo trends request successfully', async () => {
      await trendsHandler.handleXuHuongLo(mockCtx);

      expect(mockPredictionEngine.getTrends).toHaveBeenCalledWith('lo', 'weekly');
      expect(MessageFormatter.formatTrends).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'lo',
          period: 'Tuần qua'
        })
      );
      expect(mockCtx.reply).toHaveBeenCalledWith(
        'Formatted trends message',
        expect.objectContaining({
          parse_mode: 'HTML',
          reply_markup: expect.any(Object)
        })
      );
    });

    it('should parse command parameters correctly', async () => {
      mockCtx.message.text = '/xuhuonglo monthly 15';

      await trendsHandler.handleXuHuongLo(mockCtx);

      expect(mockPredictionEngine.getTrends).toHaveBeenCalledWith('lo', 'monthly');
    });

    it('should handle empty trends data', async () => {
      mockPredictionEngine.getTrends.mockResolvedValue({ hot: [], cold: [] });

      await trendsHandler.handleXuHuongLo(mockCtx);

      expect(mockCtx.reply).toHaveBeenCalledWith('Không có dữ liệu xu hướng số lô.');
    });

    it('should handle errors gracefully', async () => {
      const error = new Error('Prediction engine failed');
      mockPredictionEngine.getTrends.mockRejectedValue(error);
      MessageFormatter.formatError.mockReturnValue('Error message');

      await trendsHandler.handleXuHuongLo(mockCtx);

      expect(MessageFormatter.formatError).toHaveBeenCalledWith(error, 'xu hướng số lô');
      expect(mockCtx.reply).toHaveBeenCalledWith('Error message');
    });
  });

  describe('handleXuHuongDe', () => {
    const mockTrendsData = {
      hot: [
        { number: '123', frequency: 8, count: 8 },
        { number: '456', frequency: 6, count: 6 }
      ],
      cold: [
        { number: '789', frequency: 1, count: 1 },
        { number: '012', frequency: 0, count: 0 }
      ],
      type: 'de',
      period: 'weekly'
    };

    beforeEach(() => {
      mockPredictionEngine.getTrends.mockResolvedValue(mockTrendsData);
      mockDataManager.getNumberStatistics.mockResolvedValue([]);
      mockDataManager.getLotteryHistory.mockResolvedValue([]);
      MessageFormatter.formatTrends.mockReturnValue('Formatted de trends message');
    });

    it('should handle de trends request successfully', async () => {
      await trendsHandler.handleXuHuongDe(mockCtx);

      expect(mockPredictionEngine.getTrends).toHaveBeenCalledWith('de', 'weekly');
      expect(MessageFormatter.formatTrends).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'de',
          period: 'Tuần qua'
        })
      );
      expect(mockCtx.reply).toHaveBeenCalledWith(
        'Formatted de trends message',
        expect.objectContaining({
          parse_mode: 'HTML',
          reply_markup: expect.any(Object)
        })
      );
    });

    it('should handle null trends data', async () => {
      mockPredictionEngine.getTrends.mockResolvedValue(null);

      await trendsHandler.handleXuHuongDe(mockCtx);

      expect(mockCtx.reply).toHaveBeenCalledWith('Không có dữ liệu xu hướng số đề.');
    });
  });

  describe('_parseTrendParams', () => {
    it('should parse default parameters', () => {
      const result = trendsHandler._parseTrendParams('/xuhuonglo');
      expect(result).toEqual({
        period: 'weekly',
        limit: 10
      });
    });

    it('should parse period parameter', () => {
      const result = trendsHandler._parseTrendParams('/xuhuonglo monthly');
      expect(result).toEqual({
        period: 'monthly',
        limit: 10
      });
    });

    it('should parse period and limit parameters', () => {
      const result = trendsHandler._parseTrendParams('/xuhuonglo daily 15');
      expect(result).toEqual({
        period: 'daily',
        limit: 15
      });
    });

    it('should handle invalid parameters gracefully', () => {
      const result = trendsHandler._parseTrendParams('/xuhuonglo invalid abc');
      expect(result).toEqual({
        period: 'weekly', // Default
        limit: 10 // Default
      });
    });

    it('should cap limit at maximum', () => {
      const result = trendsHandler._parseTrendParams('/xuhuonglo weekly 50');
      expect(result).toEqual({
        period: 'weekly',
        limit: 20 // Capped at 20
      });
    });
  });

  describe('_enhanceTrendsData', () => {
    const mockTrends = {
      hot: [{ number: '12', frequency: 15 }],
      cold: [{ number: '78', frequency: 2 }]
    };

    beforeEach(() => {
      mockDataManager.getNumberStatistics.mockResolvedValue([
        { number: '12', count: 15, lastSeen: new Date('2024-01-15') },
        { number: '78', count: 2, lastSeen: new Date('2024-01-10') }
      ]);
      mockDataManager.getLotteryHistory.mockResolvedValue([]);
    });

    it('should enhance trends data with statistics', async () => {
      const result = await trendsHandler._enhanceTrendsData(mockTrends, 'lo', 'weekly');

      expect(result.hot[0]).toEqual(
        expect.objectContaining({
          number: '12',
          frequency: 15,
          percentage: expect.any(String),
          lastSeen: expect.any(Date)
        })
      );

      expect(result.cold[0]).toEqual(
        expect.objectContaining({
          number: '78',
          frequency: 2,
          daysSinceLastSeen: expect.any(Number)
        })
      );
    });

    it('should handle enhancement errors gracefully', async () => {
      mockDataManager.getNumberStatistics.mockRejectedValue(new Error('Stats error'));

      const result = await trendsHandler._enhanceTrendsData(mockTrends, 'lo', 'weekly');

      // Should return original trends if enhancement fails
      expect(result).toBe(mockTrends);
    });
  });

  describe('_getPeriodDays', () => {
    it('should return correct days for each period', () => {
      expect(trendsHandler._getPeriodDays('daily')).toBe(1);
      expect(trendsHandler._getPeriodDays('weekly')).toBe(7);
      expect(trendsHandler._getPeriodDays('monthly')).toBe(30);
      expect(trendsHandler._getPeriodDays('invalid')).toBe(7); // Default
    });
  });

  describe('_getPeriodDisplayName', () => {
    it('should return correct display names', () => {
      expect(trendsHandler._getPeriodDisplayName('daily')).toBe('Hôm nay');
      expect(trendsHandler._getPeriodDisplayName('weekly')).toBe('Tuần qua');
      expect(trendsHandler._getPeriodDisplayName('monthly')).toBe('Tháng qua');
      expect(trendsHandler._getPeriodDisplayName('invalid')).toBe('Tuần qua'); // Default
    });
  });

  describe('_calculateStreak', () => {
    beforeEach(() => {
      mockDataManager.getLotteryHistory.mockResolvedValue([
        { date: new Date('2024-01-15'), numbers: { lo: ['12', '34'] } },
        { date: new Date('2024-01-14'), numbers: { lo: ['12', '56'] } },
        { date: new Date('2024-01-13'), numbers: { lo: ['78', '90'] } },
        { date: new Date('2024-01-12'), numbers: { lo: ['12', '34'] } }
      ]);
    });

    it('should calculate streak correctly for consecutive appearances', async () => {
      const streak = await trendsHandler._calculateStreak('12', 'lo');
      expect(streak).toBe(2); // Appeared in last 2 results
    });

    it('should return 0 for numbers not in recent results', async () => {
      const streak = await trendsHandler._calculateStreak('99', 'lo');
      expect(streak).toBe(0);
    });

    it('should handle empty history', async () => {
      mockDataManager.getLotteryHistory.mockResolvedValue([]);
      const streak = await trendsHandler._calculateStreak('12', 'lo');
      expect(streak).toBe(0);
    });

    it('should handle errors gracefully', async () => {
      mockDataManager.getLotteryHistory.mockRejectedValue(new Error('DB error'));
      const streak = await trendsHandler._calculateStreak('12', 'lo');
      expect(streak).toBe(0);
    });
  });

  describe('_createTrendsKeyboard', () => {
    it('should create keyboard with period and action buttons', () => {
      const keyboard = trendsHandler._createTrendsKeyboard('lo', 'weekly');

      expect(keyboard).toEqual({
        inline_keyboard: [
          [
            { text: 'Hôm nay', callback_data: 'trends:lo:daily' },
            { text: 'Tháng qua', callback_data: 'trends:lo:monthly' }
          ],
          [
            { text: '🔄 Làm mới', callback_data: 'trends:lo:weekly' },
            { text: 'Xu hướng Đề', callback_data: 'trends:de:weekly' }
          ]
        ]
      });
    });

    it('should create keyboard for de type', () => {
      const keyboard = trendsHandler._createTrendsKeyboard('de', 'daily');

      expect(keyboard.inline_keyboard[1]).toContainEqual({
        text: 'Xu hướng Lô',
        callback_data: 'trends:lo:daily'
      });
    });
  });

  describe('handleTrendsCallback', () => {
    beforeEach(() => {
      const mockTrendsData = {
        hot: [{ number: '12', frequency: 15 }],
        cold: [{ number: '78', frequency: 2 }]
      };

      mockPredictionEngine.getTrends.mockResolvedValue(mockTrendsData);
      mockDataManager.getNumberStatistics.mockResolvedValue([]);
      mockDataManager.getLotteryHistory.mockResolvedValue([]);
      MessageFormatter.formatTrends.mockReturnValue('Updated trends message');
    });

    it('should handle callback successfully', async () => {
      await trendsHandler.handleTrendsCallback(mockCtx);

      expect(mockPredictionEngine.getTrends).toHaveBeenCalledWith('lo', 'weekly');
      expect(mockCtx.editMessageText).toHaveBeenCalledWith(
        'Updated trends message',
        expect.objectContaining({
          parse_mode: 'HTML',
          reply_markup: expect.any(Object)
        })
      );
      expect(mockCtx.answerCbQuery).toHaveBeenCalled();
    });

    it('should handle invalid callback data', async () => {
      mockCtx.callbackQuery.data = 'trends:invalid:invalid';

      await trendsHandler.handleTrendsCallback(mockCtx);

      expect(mockCtx.answerCbQuery).toHaveBeenCalledWith('Tham số không hợp lệ.');
    });

    it('should handle empty trends data in callback', async () => {
      mockPredictionEngine.getTrends.mockResolvedValue({ hot: [], cold: [] });

      await trendsHandler.handleTrendsCallback(mockCtx);

      expect(mockCtx.answerCbQuery).toHaveBeenCalledWith('Không có dữ liệu xu hướng số lo.');
    });

    it('should handle callback errors', async () => {
      mockPredictionEngine.getTrends.mockRejectedValue(new Error('Engine error'));

      await trendsHandler.handleTrendsCallback(mockCtx);

      expect(mockCtx.answerCbQuery).toHaveBeenCalledWith('Lỗi khi tải dữ liệu xu hướng.');
    });
  });

  describe('getComprehensiveTrends', () => {
    beforeEach(() => {
      mockPredictionEngine.getTrends.mockResolvedValue({
        hot: [{ number: '12', frequency: 15 }],
        cold: [{ number: '78', frequency: 2 }]
      });
      mockDataManager.getNumberStatistics.mockResolvedValue([]);
      mockDataManager.getLotteryHistory.mockResolvedValue([]);
    });

    it('should get comprehensive trends for all periods', async () => {
      const result = await trendsHandler.getComprehensiveTrends('lo');

      expect(result).toEqual(
        expect.objectContaining({
          type: 'lo',
          analysis: expect.objectContaining({
            daily: expect.any(Object),
            weekly: expect.any(Object),
            monthly: expect.any(Object)
          }),
          generatedAt: expect.any(Date),
          summary: expect.any(Object)
        })
      );

      expect(mockPredictionEngine.getTrends).toHaveBeenCalledTimes(3); // daily, weekly, monthly
    });

    it('should include patterns when requested', async () => {
      mockDataManager.getLotteryHistory.mockResolvedValue([
        { date: new Date('2024-01-15'), numbers: { lo: ['12', '13', '34'] } },
        { date: new Date('2024-01-14'), numbers: { lo: ['56', '78', '90'] } }
      ]);

      const result = await trendsHandler.getComprehensiveTrends('lo', {
        includePatterns: true
      });

      expect(result.analysis.patterns).toBeDefined();
      expect(result.analysis.patterns.consecutive).toBeDefined();
      expect(result.analysis.patterns.evenOdd).toBeDefined();
    });

    it('should handle errors gracefully', async () => {
      mockPredictionEngine.getTrends.mockRejectedValue(new Error('Engine error'));

      await expect(trendsHandler.getComprehensiveTrends('lo')).rejects.toThrow('Engine error');
    });
  });

  describe('pattern analysis methods', () => {
    const mockHistory = [
      { date: new Date('2024-01-15'), numbers: { lo: ['12', '13', '34', '56'] } },
      { date: new Date('2024-01-14'), numbers: { lo: ['23', '24', '78', '90'] } },
      { date: new Date('2024-01-13'), numbers: { lo: ['11', '22', '33', '44'] } }
    ];

    describe('_findConsecutivePatterns', () => {
      it('should find consecutive number patterns', () => {
        const result = trendsHandler._findConsecutivePatterns(mockHistory, 'lo');

        expect(result).toContainEqual({ pair: '12-13', count: 1 });
        expect(result).toContainEqual({ pair: '23-24', count: 1 });
      });
    });

    describe('_analyzeEvenOddDistribution', () => {
      it('should analyze even/odd distribution', () => {
        const result = trendsHandler._analyzeEvenOddDistribution(mockHistory, 'lo');

        expect(result).toEqual({
          even: { count: 8, percentage: '66.7' }, // 12, 34, 56, 24, 78, 90, 22, 44
          odd: { count: 4, percentage: '33.3' }, // 13, 23, 11, 33
          total: 12
        });
      });
    });

    describe('_analyzeSumRanges', () => {
      it('should analyze sum ranges', () => {
        const result = trendsHandler._analyzeSumRanges(mockHistory, 'lo');

        expect(result).toBeInstanceOf(Array);
        expect(result[0]).toHaveProperty('range');
        expect(result[0]).toHaveProperty('count');
      });
    });

    describe('_analyzeDigitPatterns', () => {
      it('should analyze digit frequency patterns', () => {
        const result = trendsHandler._analyzeDigitPatterns(mockHistory, 'lo');

        expect(result).toBeInstanceOf(Array);
        expect(result[0]).toHaveProperty('digit');
        expect(result[0]).toHaveProperty('count');

        // Should include digits like '1', '2', '3', '4' with their frequencies
        const digitCounts = result.reduce((acc, item) => {
          acc[item.digit] = item.count;
          return acc;
        }, {});

        expect(digitCounts['1']).toBeGreaterThan(0);
        expect(digitCounts['2']).toBeGreaterThan(0);
      });
    });
  });

  describe('_generateTrendsSummary', () => {
    it('should generate trends summary', () => {
      const analysis = {
        weekly: {
          hot: [
            { number: '12', frequency: 15 },
            { number: '34', frequency: 12 }
          ],
          cold: [
            { number: '78', frequency: 2 },
            { number: '90', frequency: 1 }
          ]
        }
      };

      const summary = trendsHandler._generateTrendsSummary(analysis);

      expect(summary).toEqual({
        hotNumbers: analysis.weekly.hot.slice(0, 5),
        coldNumbers: analysis.weekly.cold.slice(0, 5),
        recommendations: [
          'Các số nóng có xu hướng xuất hiện thường xuyên',
          'Các số lạnh có thể sắp "nóng" trở lại'
        ],
        insights: []
      });
    });

    it('should handle empty analysis', () => {
      const summary = trendsHandler._generateTrendsSummary({});

      expect(summary).toEqual({
        hotNumbers: [],
        coldNumbers: [],
        recommendations: [],
        insights: []
      });
    });
  });
});