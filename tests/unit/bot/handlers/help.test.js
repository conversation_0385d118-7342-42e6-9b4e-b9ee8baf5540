const HelpHandler = require('../../../../lib/bot/handlers/help');

// Mock dependencies
const MessageFormatter = {
  bold: jest.fn((text) => `<b>${text}</b>`),
  italic: jest.fn((text) => `<i>${text}</i>`),
  code: jest.fn((text) => `<code>${text}</code>`),
  truncateMessage: jest.fn((msg) => msg),
  formatError: jest.fn((error, context) => `❌ Error: ${context}`)
};

jest.mock('../../../../lib/bot/utils/formatter', () => MessageFormatter);
jest.mock('../../../../lib/logger', () => () => ({
  logInfo: jest.fn(),
  logError: jest.fn()
}));

describe('HelpHandler', () => {
  let helpHandler;
  let mockCtx;

  beforeEach(() => {
    helpHandler = new HelpHandler();

    // Mock Telegram context
    mockCtx = {
      args: [],
      userId: 123456,
      reply: jest.fn(),
      editMessageText: jest.fn(),
      callbackQuery: { data: 'help:main' },
      answerCbQuery: jest.fn()
    };

    // Reset mocks
    jest.clearAllMocks();
  });

  describe('constructor', () => {
    it('should initialize with command definitions', () => {
      expect(helpHandler.commands).toBeDefined();
      expect(Array.isArray(helpHandler.commands)).toBe(true);
      expect(helpHandler.commands.length).toBeGreaterThan(0);
    });

    it('should have all required command categories', () => {
      const categories = helpHandler.commands.map(cat => cat.category);

      expect(categories).toContain('Dự Đoán');
      expect(categories).toContain('Lịch Sử');
      expect(categories).toContain('Xu Hướng');
      expect(categories).toContain('Phân Tích');
      expect(categories).toContain('Hỗ Trợ');
    });

    it('should have all required commands', () => {
      const allCommands = [];
      helpHandler.commands.forEach(category => {
        category.commands.forEach(cmd => {
          allCommands.push(cmd.command.replace('/', ''));
        });
      });

      expect(allCommands).toContain('dukienlo');
      expect(allCommands).toContain('dukiende');
      expect(allCommands).toContain('lichsu');
      expect(allCommands).toContain('xuhuonglo');
      expect(allCommands).toContain('xuhuongde');
      expect(allCommands).toContain('number');
      expect(allCommands).toContain('help');
    });
  });

  describe('handleHelp', () => {
    it('should show general help when no arguments provided', async () => {
      mockCtx.args = [];

      await helpHandler.handleHelp(mockCtx);

      expect(mockCtx.reply).toHaveBeenCalledWith(
        expect.stringContaining('Bot Dự Đoán Xổ Số'),
        expect.objectContaining({
          parse_mode: 'HTML',
          reply_markup: expect.any(Object)
        })
      );
    });

    it('should show specific command help when command provided', async () => {
      mockCtx.args = ['number'];

      await helpHandler.handleHelp(mockCtx);

      expect(mockCtx.reply).toHaveBeenCalledWith(
        expect.stringContaining('Hướng Dẫn: /number'),
        expect.objectContaining({
          parse_mode: 'HTML',
          reply_markup: expect.any(Object)
        })
      );
    });

    it('should handle invalid command gracefully', async () => {
      mockCtx.args = ['invalidcommand'];

      await helpHandler.handleHelp(mockCtx);

      expect(mockCtx.reply).toHaveBeenCalledWith(
        expect.stringContaining('Không tìm thấy lệnh'),
        expect.objectContaining({ parse_mode: 'HTML' })
      );
    });

    it('should handle errors gracefully', async () => {
      // Mock an internal error during processing
      const originalSendGeneralHelp = helpHandler._sendGeneralHelp;
      helpHandler._sendGeneralHelp = jest.fn().mockRejectedValue(new Error('Internal error'));

      await helpHandler.handleHelp(mockCtx);

      expect(mockCtx.reply).toHaveBeenCalledWith(
        expect.stringContaining('❌ Đã xảy ra lỗi (hiển thị trợ giúp)')
      );

      // Restore original method
      helpHandler._sendGeneralHelp = originalSendGeneralHelp;
    });
  });

  describe('_sendGeneralHelp', () => {
    it('should send comprehensive help message', async () => {
      await helpHandler._sendGeneralHelp(mockCtx);

      expect(mockCtx.reply).toHaveBeenCalledWith(
        expect.stringContaining('Bot Dự Đoán Xổ Số Miền Bắc'),
        expect.objectContaining({
          parse_mode: 'HTML',
          reply_markup: expect.objectContaining({
            inline_keyboard: expect.any(Array)
          })
        })
      );

      const message = mockCtx.reply.mock.calls[0][0];
      expect(message).toContain('Dự Đoán');
      expect(message).toContain('Lịch Sử');
      expect(message).toContain('Xu Hướng');
      expect(message).toContain('Phân Tích');
      expect(message).toContain('Mẹo sử dụng');
    });
  });

  describe('_sendCommandHelp', () => {
    it('should send detailed help for valid command', async () => {
      await helpHandler._sendCommandHelp(mockCtx, 'number');

      expect(mockCtx.reply).toHaveBeenCalledWith(
        expect.stringContaining('Hướng Dẫn: /number'),
        expect.objectContaining({
          parse_mode: 'HTML',
          reply_markup: expect.any(Object)
        })
      );

      const message = mockCtx.reply.mock.calls[0][0];
      expect(message).toContain('Mô tả:');
      expect(message).toContain('Cách sử dụng:');
      expect(message).toContain('Ví dụ:');
      expect(message).toContain('Chi tiết:');
      expect(message).toContain('Mẹo sử dụng:');
    });

    it('should handle invalid command', async () => {
      await helpHandler._sendCommandHelp(mockCtx, 'invalidcommand');

      expect(mockCtx.reply).toHaveBeenCalledWith(
        expect.stringContaining('Không tìm thấy lệnh'),
        expect.objectContaining({ parse_mode: 'HTML' })
      );
    });
  });

  describe('handleHelpCallback', () => {
    it('should handle main help callback', async () => {
      mockCtx.callbackQuery.data = 'help:main';

      await helpHandler.handleHelpCallback(mockCtx);

      expect(mockCtx.answerCbQuery).toHaveBeenCalled();
      expect(mockCtx.reply).toHaveBeenCalledWith(
        expect.stringContaining('Bot Dự Đoán Xổ Số'),
        expect.any(Object)
      );
    });

    it('should handle examples callback', async () => {
      mockCtx.callbackQuery.data = 'help:examples';

      await helpHandler.handleHelpCallback(mockCtx);

      expect(mockCtx.editMessageText).toHaveBeenCalledWith(
        expect.stringContaining('Ví Dụ Sử Dụng'),
        expect.objectContaining({
          parse_mode: 'HTML',
          reply_markup: expect.any(Object)
        })
      );
    });

    it('should handle tips callback', async () => {
      mockCtx.callbackQuery.data = 'help:tips';

      await helpHandler.handleHelpCallback(mockCtx);

      expect(mockCtx.editMessageText).toHaveBeenCalledWith(
        expect.stringContaining('Mẹo Sử Dụng Bot'),
        expect.objectContaining({
          parse_mode: 'HTML',
          reply_markup: expect.any(Object)
        })
      );
    });

    it('should handle specific command callback', async () => {
      mockCtx.callbackQuery.data = 'help:number';

      await helpHandler.handleHelpCallback(mockCtx);

      expect(mockCtx.reply).toHaveBeenCalledWith(
        expect.stringContaining('Hướng Dẫn: /number'),
        expect.any(Object)
      );
    });

    it('should handle callback errors', async () => {
      mockCtx.callbackQuery.data = 'help:main';
      mockCtx.reply.mockRejectedValue(new Error('Network error'));

      await helpHandler.handleHelpCallback(mockCtx);

      expect(mockCtx.answerCbQuery).toHaveBeenCalledWith('❌ Lỗi khi tải trợ giúp.');
    });
  });

  describe('_sendExamples', () => {
    it('should send comprehensive examples', async () => {
      await helpHandler._sendExamples(mockCtx);

      expect(mockCtx.editMessageText).toHaveBeenCalledWith(
        expect.stringContaining('Ví Dụ Sử Dụng'),
        expect.objectContaining({
          parse_mode: 'HTML',
          reply_markup: expect.objectContaining({
            inline_keyboard: [[{ text: '🔙 Quay lại', callback_data: 'help:main' }]]
          })
        })
      );

      const message = mockCtx.editMessageText.mock.calls[0][0];
      expect(message).toContain('/dukienlo');
      expect(message).toContain('/lichsu 10');
      expect(message).toContain('/xuhuonglo 30');
      expect(message).toContain('/number 25');
    });
  });

  describe('_sendTips', () => {
    it('should send usage tips', async () => {
      await helpHandler._sendTips(mockCtx);

      expect(mockCtx.editMessageText).toHaveBeenCalledWith(
        expect.stringContaining('Mẹo Sử Dụng Bot'),
        expect.objectContaining({
          parse_mode: 'HTML',
          reply_markup: expect.objectContaining({
            inline_keyboard: [[{ text: '🔙 Quay lại', callback_data: 'help:main' }]]
          })
        })
      );

      const message = mockCtx.editMessageText.mock.calls[0][0];
      expect(message).toContain('Dự đoán hiệu quả');
      expect(message).toContain('Phân tích dữ liệu');
      expect(message).toContain('Lưu ý quan trọng');
    });
  });

  describe('_findCommand', () => {
    it('should find existing commands', () => {
      expect(helpHandler._findCommand('number')).toBeDefined();
      expect(helpHandler._findCommand('dukienlo')).toBeDefined();
      expect(helpHandler._findCommand('help')).toBeDefined();
    });

    it('should return null for non-existing commands', () => {
      expect(helpHandler._findCommand('nonexistent')).toBeNull();
      expect(helpHandler._findCommand('')).toBeNull();
    });
  });

  describe('_getCommandTips', () => {
    it('should return tips for known commands', () => {
      const numberTips = helpHandler._getCommandTips('number');
      expect(Array.isArray(numberTips)).toBe(true);
      expect(numberTips.length).toBeGreaterThan(0);
      expect(numberTips[0]).toContain('00 đến 99');

      const dukienloTips = helpHandler._getCommandTips('dukienlo');
      expect(Array.isArray(dukienloTips)).toBe(true);
      expect(dukienloTips.length).toBeGreaterThan(0);
    });

    it('should return empty array for unknown commands', () => {
      const tips = helpHandler._getCommandTips('unknown');
      expect(Array.isArray(tips)).toBe(true);
      expect(tips.length).toBe(0);
    });
  });

  describe('_createHelpKeyboard', () => {
    it('should create help keyboard with correct structure', () => {
      const keyboard = helpHandler._createHelpKeyboard();

      expect(keyboard).toHaveProperty('inline_keyboard');
      expect(Array.isArray(keyboard.inline_keyboard)).toBe(true);
      expect(keyboard.inline_keyboard.length).toBeGreaterThan(0);

      // Check for specific buttons
      const allButtons = keyboard.inline_keyboard.flat();
      const buttonTexts = allButtons.map(btn => btn.text);

      expect(buttonTexts).toContain('📚 Ví dụ');
      expect(buttonTexts).toContain('💡 Mẹo');
      expect(buttonTexts).toContain('🎯 Dự đoán');
      expect(buttonTexts).toContain('🔍 Phân tích');
    });
  });

  describe('_createCommandHelpKeyboard', () => {
    it('should create command-specific keyboard', () => {
      const keyboard = helpHandler._createCommandHelpKeyboard('number');

      expect(keyboard).toHaveProperty('inline_keyboard');
      expect(Array.isArray(keyboard.inline_keyboard)).toBe(true);

      // Should always have back button
      const allButtons = keyboard.inline_keyboard.flat();
      const backButton = allButtons.find(btn => btn.text === '🔙 Quay lại');
      expect(backButton).toBeDefined();
      expect(backButton.callback_data).toBe('help:main');
    });

    it('should create default keyboard for unknown commands', () => {
      const keyboard = helpHandler._createCommandHelpKeyboard('unknown');

      expect(keyboard).toHaveProperty('inline_keyboard');
      const allButtons = keyboard.inline_keyboard.flat();
      const backButton = allButtons.find(btn => btn.text === '🔙 Quay lại');
      expect(backButton).toBeDefined();
    });
  });

  describe('handleInvalidCommand', () => {
    it('should handle invalid command with suggestions', async () => {
      await helpHandler.handleInvalidCommand(mockCtx, 'numbr');

      expect(mockCtx.reply).toHaveBeenCalledWith(
        expect.stringContaining('Lệnh không hợp lệ'),
        expect.objectContaining({
          parse_mode: 'HTML',
          reply_markup: expect.objectContaining({
            inline_keyboard: expect.arrayContaining([
              [{ text: '📖 Xem trợ giúp', callback_data: 'help:main' }]
            ])
          })
        })
      );

      const message = mockCtx.reply.mock.calls[0][0];
      expect(message).toContain('numbr');
      expect(message).toContain('Có phải bạn muốn dùng');
    });

    it('should handle invalid command without suggestions', async () => {
      await helpHandler.handleInvalidCommand(mockCtx, 'completelywrongcommand');

      expect(mockCtx.reply).toHaveBeenCalledWith(
        expect.stringContaining('Lệnh không hợp lệ'),
        expect.any(Object)
      );
    });

    it('should handle errors in invalid command processing', async () => {
      // Mock an internal error during processing
      const originalGetSuggestions = helpHandler._getSuggestions;
      helpHandler._getSuggestions = jest.fn().mockImplementation(() => {
        throw new Error('Internal error');
      });

      await helpHandler.handleInvalidCommand(mockCtx, 'test');

      expect(mockCtx.reply).toHaveBeenCalledWith(
        expect.stringContaining('❌ Đã xảy ra lỗi (xử lý lệnh không hợp lệ)')
      );

      // Restore original method
      helpHandler._getSuggestions = originalGetSuggestions;
    });
  });

  describe('_getSuggestions', () => {
    it('should find exact matches', () => {
      const suggestions = helpHandler._getSuggestions('number');
      expect(suggestions.length).toBeGreaterThan(0);
      expect(suggestions[0].command).toBe('number');
    });

    it('should find partial matches', () => {
      const suggestions = helpHandler._getSuggestions('num');
      expect(suggestions.length).toBeGreaterThan(0);
      expect(suggestions.some(s => s.command === 'number')).toBe(true);
    });

    it('should find fuzzy matches', () => {
      const suggestions = helpHandler._getSuggestions('numbr');
      expect(suggestions.length).toBeGreaterThan(0);
      expect(suggestions.some(s => s.command === 'number')).toBe(true);
    });

    it('should limit suggestions to 3', () => {
      const suggestions = helpHandler._getSuggestions('u');
      expect(suggestions.length).toBeLessThanOrEqual(3);
    });

    it('should return empty array for no matches', () => {
      const suggestions = helpHandler._getSuggestions('zzzzzzzzz');
      expect(suggestions.length).toBe(0);
    });
  });

  describe('_calculateDistance', () => {
    it('should calculate edit distance correctly', () => {
      expect(helpHandler._calculateDistance('', '')).toBe(0);
      expect(helpHandler._calculateDistance('abc', 'abc')).toBe(0);
      expect(helpHandler._calculateDistance('abc', 'ab')).toBe(1);
      expect(helpHandler._calculateDistance('abc', 'def')).toBe(3);
      expect(helpHandler._calculateDistance('number', 'numbr')).toBe(1);
    });

    it('should handle empty strings', () => {
      expect(helpHandler._calculateDistance('', 'abc')).toBe(3);
      expect(helpHandler._calculateDistance('abc', '')).toBe(3);
    });
  });

  describe('command structure validation', () => {
    it('should have valid structure for all commands', () => {
      helpHandler.commands.forEach(category => {
        expect(category).toHaveProperty('category');
        expect(category).toHaveProperty('emoji');
        expect(category).toHaveProperty('commands');
        expect(Array.isArray(category.commands)).toBe(true);

        category.commands.forEach(cmd => {
          expect(cmd).toHaveProperty('command');
          expect(cmd).toHaveProperty('description');
          expect(cmd).toHaveProperty('usage');
          expect(cmd).toHaveProperty('example');
          expect(cmd.command).toMatch(/^\/\w+$/);
        });
      });
    });

    it('should have unique commands', () => {
      const allCommands = [];
      helpHandler.commands.forEach(category => {
        category.commands.forEach(cmd => {
          allCommands.push(cmd.command);
        });
      });

      const uniqueCommands = [...new Set(allCommands)];
      expect(uniqueCommands.length).toBe(allCommands.length);
    });
  });
});