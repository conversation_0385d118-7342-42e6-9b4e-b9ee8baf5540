const AnalysisHandler = require('../../../../lib/bot/handlers/analysis');

// Mock dependencies
const MessageFormatter = {
  formatNumber: jest.fn((num) => `<code>${num}</code>`),
  bold: jest.fn((text) => `<b>${text}</b>`),
  truncateMessage: jest.fn((msg) => msg),
  formatError: jest.fn((error, context) => `❌ Error: ${context}`)
};

jest.mock('../../../../lib/bot/utils/formatter', () => MessageFormatter);
jest.mock('../../../../lib/logger', () => () => ({
  logInfo: jest.fn(),
  logError: jest.fn()
}));

describe('AnalysisHandler', () => {
  let analysisHandler;
  let mockPredictionEngine;
  let mockDataManager;
  let mockCtx;

  beforeEach(() => {
    // Mock PredictionEngine
    mockPredictionEngine = {
      numberAnalyzer: {
        analyzeNumber: jest.fn()
      }
    };

    // Mock DataManager
    mockDataManager = {
      getLotteryHistory: jest.fn()
    };

    // Mock Telegram context
    mockCtx = {
      args: ['25'],
      userId: 123456,
      reply: jest.fn(),
      editMessageText: jest.fn(),
      callbackQuery: { data: 'analysis:25:detailed' },
      answerCbQuery: jest.fn()
    };

    analysisHandler = new AnalysisHandler(mockPredictionEngine, mockDataManager);

    // Reset mocks
    jest.clearAllMocks();
  });

  describe('constructor', () => {
    it('should initialize with correct dependencies', () => {
      expect(analysisHandler.predictionEngine).toBe(mockPredictionEngine);
      expect(analysisHandler.dataManager).toBe(mockDataManager);
      expect(analysisHandler.maxAnalysisHistory).toBe(365);
    });
  });

  describe('handleNumberAnalysis', () => {
    const mockHistoryData = [
      {
        date: new Date('2024-01-15'),
        numbers: { lo: ['25', '34', '56'], de: ['25'] }
      },
      {
        date: new Date('2024-01-14'),
        numbers: { lo: ['12', '78', '90'], de: ['78'] }
      }
    ];

    const mockAnalysisResult = {
      number: '25',
      type: 'lo',
      totalOccurrences: 1,
      frequency: 0.5,
      expectedFrequency: 0.27,
      relativeFrequency: 1.85,
      lastSeen: new Date('2024-01-15'),
      daysSinceLastSeen: 1,
      trends: {
        short: { trend: 'hot', occurrences: 1, strength: 0.5 }
      },
      recommendations: [
        {
          type: 'trend',
          priority: 'high',
          message: 'Số 25 đang trong xu hướng nóng',
          confidence: 75
        }
      ]
    };

    beforeEach(() => {
      mockDataManager.getLotteryHistory.mockResolvedValue(mockHistoryData);
      mockPredictionEngine.numberAnalyzer.analyzeNumber.mockResolvedValue(mockAnalysisResult);
      mockCtx.reply.mockResolvedValue({ message_id: 123 });
    });

    it('should handle valid number analysis successfully', async () => {
      await analysisHandler.handleNumberAnalysis(mockCtx);

      expect(mockCtx.reply).toHaveBeenCalledWith(
        expect.stringContaining('Đang phân tích số')
      );

      expect(mockDataManager.getLotteryHistory).toHaveBeenCalledWith({
        region: 'north',
        limit: 365,
        sortBy: 'date',
        sortOrder: 'desc'
      });

      expect(mockPredictionEngine.numberAnalyzer.analyzeNumber).toHaveBeenCalledTimes(2); // lo and de
      expect(mockCtx.editMessageText).toHaveBeenCalledWith(
        expect.stringContaining('Phân Tích Số'),
        expect.objectContaining({
          message_id: 123,
          parse_mode: 'HTML',
          reply_markup: expect.any(Object)
        })
      );
    });

    it('should handle missing number argument', async () => {
      mockCtx.args = [];

      await analysisHandler.handleNumberAnalysis(mockCtx);

      expect(mockCtx.reply).toHaveBeenCalledWith(
        expect.stringContaining('Vui lòng cung cấp số cần phân tích')
      );
      expect(mockDataManager.getLotteryHistory).not.toHaveBeenCalled();
    });

    it('should handle invalid number format', async () => {
      mockCtx.args = ['abc'];

      await analysisHandler.handleNumberAnalysis(mockCtx);

      expect(mockCtx.reply).toHaveBeenCalledWith(
        expect.stringContaining('Số không hợp lệ')
      );
      expect(mockDataManager.getLotteryHistory).not.toHaveBeenCalled();
    });

    it('should handle number out of range', async () => {
      mockCtx.args = ['100'];

      await analysisHandler.handleNumberAnalysis(mockCtx);

      expect(mockCtx.reply).toHaveBeenCalledWith(
        expect.stringContaining('Số phải từ 00 đến 99')
      );
      expect(mockDataManager.getLotteryHistory).not.toHaveBeenCalled();
    });

    it('should handle empty historical data', async () => {
      mockDataManager.getLotteryHistory.mockResolvedValue([]);

      await analysisHandler.handleNumberAnalysis(mockCtx);

      expect(mockCtx.editMessageText).toHaveBeenCalledWith(
        expect.stringContaining('Không có dữ liệu lịch sử'),
        expect.objectContaining({ message_id: 123 })
      );
    });

    it('should handle data manager error', async () => {
      mockDataManager.getLotteryHistory.mockRejectedValue(new Error('DB Error'));

      await analysisHandler.handleNumberAnalysis(mockCtx);

      expect(mockCtx.editMessageText).toHaveBeenCalledWith(
        expect.stringContaining('❌'),
        expect.objectContaining({ message_id: 123 })
      );
    });

    it('should handle analysis engine error gracefully', async () => {
      mockPredictionEngine.numberAnalyzer.analyzeNumber.mockRejectedValue(new Error('Analysis Error'));

      await analysisHandler.handleNumberAnalysis(mockCtx);

      // Should show error message when analysis fails
      expect(mockCtx.editMessageText).toHaveBeenCalledWith(
        expect.stringContaining('❌'),
        expect.objectContaining({ message_id: 123 })
      );
    });

    it('should format number with leading zero', async () => {
      mockCtx.args = ['5'];

      await analysisHandler.handleNumberAnalysis(mockCtx);

      expect(mockPredictionEngine.numberAnalyzer.analyzeNumber).toHaveBeenCalledWith(
        '05', // Should be padded
        mockHistoryData,
        'lo'
      );
    });
  });

  describe('handleAnalysisCallback', () => {
    const mockHistoryData = [
      {
        date: new Date('2024-01-15'),
        numbers: { lo: ['25'], de: ['25'] }
      }
    ];

    const mockAnalysisResult = {
      number: '25',
      totalOccurrences: 1,
      frequency: 0.5,
      correlations: [
        { number: '26', correlation: 0.8, strength: 'strong' }
      ],
      trends: {
        short: { trend: 'hot', occurrences: 1 }
      }
    };

    beforeEach(() => {
      mockDataManager.getLotteryHistory.mockResolvedValue(mockHistoryData);
      mockPredictionEngine.numberAnalyzer.analyzeNumber.mockResolvedValue(mockAnalysisResult);
    });

    it('should handle detailed analysis callback', async () => {
      mockCtx.callbackQuery.data = 'analysis:25:detailed';

      await analysisHandler.handleAnalysisCallback(mockCtx);

      expect(mockCtx.answerCbQuery).toHaveBeenCalledWith('🔍 Đang tải phân tích chi tiết...');
      expect(mockCtx.editMessageText).toHaveBeenCalledWith(
        expect.stringContaining('Chi Tiết'),
        expect.objectContaining({
          parse_mode: 'HTML',
          reply_markup: expect.any(Object)
        })
      );
    });

    it('should handle trends analysis callback', async () => {
      mockCtx.callbackQuery.data = 'analysis:25:trends';

      await analysisHandler.handleAnalysisCallback(mockCtx);

      expect(mockCtx.editMessageText).toHaveBeenCalledWith(
        expect.stringContaining('Xu Hướng'),
        expect.any(Object)
      );
    });

    it('should handle correlations analysis callback', async () => {
      mockCtx.callbackQuery.data = 'analysis:25:correlations';

      await analysisHandler.handleAnalysisCallback(mockCtx);

      expect(mockCtx.editMessageText).toHaveBeenCalledWith(
        expect.stringContaining('Tương Quan'),
        expect.any(Object)
      );
    });

    it('should handle summary analysis callback', async () => {
      mockCtx.callbackQuery.data = 'analysis:25:summary';

      await analysisHandler.handleAnalysisCallback(mockCtx);

      expect(mockCtx.editMessageText).toHaveBeenCalledWith(
        expect.stringContaining('Phân Tích Số'),
        expect.any(Object)
      );
    });

    it('should handle non-analysis callback', async () => {
      mockCtx.callbackQuery.data = 'other:25:detailed';

      await analysisHandler.handleAnalysisCallback(mockCtx);

      expect(mockCtx.answerCbQuery).not.toHaveBeenCalled();
      expect(mockCtx.editMessageText).not.toHaveBeenCalled();
    });

    it('should handle callback error', async () => {
      mockCtx.callbackQuery.data = 'analysis:25:detailed';
      mockDataManager.getLotteryHistory.mockRejectedValue(new Error('DB Error'));

      await analysisHandler.handleAnalysisCallback(mockCtx);

      expect(mockCtx.answerCbQuery).toHaveBeenCalledWith('❌ Lỗi khi tải phân tích.');
    });
  });

  describe('_validateNumber', () => {
    it('should validate valid numbers', () => {
      expect(analysisHandler._validateNumber('25')).toEqual({
        isValid: true,
        number: '25'
      });

      expect(analysisHandler._validateNumber('5')).toEqual({
        isValid: true,
        number: '05'
      });

      expect(analysisHandler._validateNumber('00')).toEqual({
        isValid: true,
        number: '00'
      });

      expect(analysisHandler._validateNumber('99')).toEqual({
        isValid: true,
        number: '99'
      });
    });

    it('should reject invalid numbers', () => {
      expect(analysisHandler._validateNumber('abc').isValid).toBe(false);
      expect(analysisHandler._validateNumber('100').isValid).toBe(false);
      expect(analysisHandler._validateNumber('').isValid).toBe(false);
      // Note: '-1' becomes '1' after cleaning, which is valid
      expect(analysisHandler._validateNumber('xyz').isValid).toBe(false);
    });

    it('should clean input by removing non-digits', () => {
      expect(analysisHandler._validateNumber('2a5b')).toEqual({
        isValid: true,
        number: '25'
      });

      expect(analysisHandler._validateNumber('0-5')).toEqual({
        isValid: true,
        number: '05'
      });
    });
  });

  describe('_performBasicAnalysis', () => {
    const mockHistoryData = [
      {
        date: new Date('2024-01-15'),
        numbers: { lo: ['25', '34'], de: ['25'] }
      },
      {
        date: new Date('2024-01-14'),
        numbers: { lo: ['12', '78'], de: ['78'] }
      },
      {
        date: new Date('2024-01-13'),
        numbers: { lo: ['25', '90'], de: ['90'] }
      }
    ];

    it('should perform basic analysis for lo numbers', () => {
      const result = analysisHandler._performBasicAnalysis('25', mockHistoryData, 'lo');

      expect(result).toEqual({
        number: '25',
        type: 'lo',
        totalOccurrences: 2,
        frequency: 2/3,
        expectedFrequency: 0.27,
        relativeFrequency: (2/3) / 0.27,
        lastSeen: new Date('2024-01-13'), // Last occurrence in the test data
        daysSinceLastSeen: expect.any(Number),
        trends: expect.any(Object),
        recommendations: expect.any(Array)
      });
    });

    it('should perform basic analysis for de numbers', () => {
      const result = analysisHandler._performBasicAnalysis('25', mockHistoryData, 'de');

      expect(result).toEqual({
        number: '25',
        type: 'de',
        totalOccurrences: 1,
        frequency: 1/3,
        expectedFrequency: 0.02,
        relativeFrequency: (1/3) / 0.02,
        lastSeen: new Date('2024-01-15'),
        daysSinceLastSeen: expect.any(Number),
        trends: expect.any(Object),
        recommendations: expect.any(Array)
      });
    });

    it('should handle numbers that never appeared', () => {
      const result = analysisHandler._performBasicAnalysis('99', mockHistoryData, 'lo');

      expect(result.totalOccurrences).toBe(0);
      expect(result.frequency).toBe(0);
      expect(result.lastSeen).toBeNull();
      expect(result.daysSinceLastSeen).toBeNull();
    });
  });

  describe('_formatAnalysisResults', () => {
    const mockLoAnalysis = {
      number: '25',
      totalOccurrences: 5,
      frequency: 0.5,
      lastSeen: new Date('2024-01-15'),
      daysSinceLastSeen: 2,
      trends: {
        short: { trend: 'hot', occurrences: 3 }
      },
      recommendations: [
        {
          type: 'trend',
          priority: 'high',
          message: 'Số 25 đang nóng',
          confidence: 80
        }
      ]
    };

    const mockDeAnalysis = {
      number: '25',
      totalOccurrences: 2,
      frequency: 0.2,
      lastSeen: new Date('2024-01-10'),
      daysSinceLastSeen: 7,
      trends: {
        short: { trend: 'cold', occurrences: 0 }
      },
      recommendations: []
    };

    it('should format analysis results correctly', () => {
      const result = analysisHandler._formatAnalysisResults('25', mockLoAnalysis, mockDeAnalysis, 10);

      expect(result).toContain('Phân Tích Số');
      expect(result).toContain('25');
      expect(result).toContain('Phân Tích Lô');
      expect(result).toContain('Phân Tích Đề');
      expect(result).toContain('5 lần');
      expect(result).toContain('2 lần');
      expect(result).toContain('🔥 hot');
      expect(result).toContain('❄️ cold');
      expect(result).toContain('Khuyến Nghị');
    });

    it('should handle missing last seen dates', () => {
      const analysisWithoutDate = { ...mockLoAnalysis, lastSeen: null, daysSinceLastSeen: null };

      const result = analysisHandler._formatAnalysisResults('25', analysisWithoutDate, mockDeAnalysis, 10);

      expect(result).toContain('Chưa từng xuất hiện');
    });
  });

  describe('_createAnalysisKeyboard', () => {
    it('should create analysis keyboard with correct buttons', () => {
      const keyboard = analysisHandler._createAnalysisKeyboard('25');

      expect(keyboard).toEqual({
        inline_keyboard: [
          [
            { text: '📊 Chi tiết', callback_data: 'analysis:25:detailed' },
            { text: '📈 Xu hướng', callback_data: 'analysis:25:trends' }
          ],
          [
            { text: '🔗 Tương quan', callback_data: 'analysis:25:correlations' },
            { text: '🔄 Làm mới', callback_data: 'analysis:25:summary' }
          ]
        ]
      });
    });
  });

  describe('_generateBasicRecommendations', () => {
    it('should generate frequency-based recommendations', () => {
      const recommendations = analysisHandler._generateBasicRecommendations('25', 10, 0.5, 0.27, 5);

      expect(recommendations).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            type: 'frequency',
            priority: 'medium',
            message: expect.stringContaining('thường xuyên hơn'),
            confidence: expect.any(Number)
          })
        ])
      );
    });

    it('should generate absence-based recommendations', () => {
      const recommendations = analysisHandler._generateBasicRecommendations('25', 5, 0.2, 0.27, 35);

      expect(recommendations).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            type: 'absence',
            priority: 'low',
            message: expect.stringContaining('không xuất hiện 35 ngày'),
            confidence: expect.any(Number)
          })
        ])
      );
    });

    it('should return empty recommendations for normal patterns', () => {
      const recommendations = analysisHandler._generateBasicRecommendations('25', 5, 0.27, 0.27, 10);

      expect(recommendations).toHaveLength(0);
    });
  });
});