const HistoryHandler = require('../../../../lib/bot/handlers/history');

// Mock dependencies
const MessageFormatter = {
  formatHistory: jest.fn(),
  formatError: jest.fn()
};

jest.mock('../../../../lib/bot/utils/formatter', () => MessageFormatter);
jest.mock('../../../../lib/logger', () => () => ({
  logInfo: jest.fn(),
  logError: jest.fn()
}));

describe('HistoryHandler', () => {
  let historyHandler;
  let mockDataManager;
  let mockCtx;

  beforeEach(() => {
    // Mock DataManager
    mockDataManager = {
      getLotteryHistory: jest.fn(),
      getLotteryResultByDate: jest.fn(),
      getNumberStatistics: jest.fn()
    };

    // Mock Telegram context
    mockCtx = {
      message: { text: '/lichsu' },
      from: { id: 123456 },
      chat: { id: -789012 },
      reply: jest.fn(),
      editMessageText: jest.fn(),
      answerCbQuery: jest.fn(),
      callbackQuery: { data: 'history:5:0' }
    };

    historyHandler = new HistoryHandler(mockDataManager);

    // Reset mocks
    jest.clearAllMocks();
  });

  describe('constructor', () => {
    it('should initialize with correct default values', () => {
      expect(historyHandler.dataManager).toBe(mockDataManager);
      expect(historyHandler.defaultLimit).toBe(5);
      expect(historyHandler.maxLimit).toBe(20);
    });
  });

  describe('handleLichSu', () => {
    const mockHistoryData = [
      {
        date: new Date('2024-01-15'),
        region: 'north',
        prizes: { special: '12345' },
        numbers: { lo: ['12', '34', '56'], de: ['123'] }
      },
      {
        date: new Date('2024-01-14'),
        region: 'north',
        prizes: { special: '67890' },
        numbers: { lo: ['78', '90', '12'], de: ['678'] }
      }
    ];

    beforeEach(() => {
      mockDataManager.getLotteryHistory.mockResolvedValue(mockHistoryData);
      MessageFormatter.formatHistory.mockReturnValue('Formatted history message');
    });

    it('should handle basic history request successfully', async () => {
      await historyHandler.handleLichSu(mockCtx);

      expect(mockDataManager.getLotteryHistory).toHaveBeenCalledWith({
        region: 'north',
        limit: 5,
        sortBy: 'date',
        sortOrder: 'desc'
      });

      expect(mockCtx.reply).toHaveBeenCalledWith(
        expect.stringContaining('📊'),
        expect.objectContaining({
          parse_mode: 'HTML',
          reply_markup: expect.any(Object)
        })
      );
    });

    it('should parse command parameters correctly', async () => {
      mockCtx.message.text = '/lichsu 10 2 central';

      await historyHandler.handleLichSu(mockCtx);

      expect(mockDataManager.getLotteryHistory).toHaveBeenCalledWith({
        region: 'central',
        limit: 20, // 10 * (2)
        sortBy: 'date',
        sortOrder: 'desc'
      });
    });

    it('should handle empty history data', async () => {
      mockDataManager.getLotteryHistory.mockResolvedValue([]);

      await historyHandler.handleLichSu(mockCtx);

      expect(mockCtx.reply).toHaveBeenCalledWith('Không có dữ liệu lịch sử xổ số.');
    });

    it('should handle pagination correctly', async () => {
      const largeHistoryData = Array(15).fill(null).map((_, index) => ({
        date: new Date(`2024-01-${15 - index}`),
        region: 'north',
        prizes: { special: `1234${index}` },
        numbers: { lo: [`${index}0`, `${index}1`], de: [`${index}23`] }
      }));

      mockDataManager.getLotteryHistory.mockResolvedValue(largeHistoryData);
      mockCtx.message.text = '/lichsu 5 2'; // Page 2, 5 items per page

      await historyHandler.handleLichSu(mockCtx);

      expect(mockCtx.reply).toHaveBeenCalledWith(
        expect.stringContaining('Trang 2/3'),
        expect.objectContaining({
          parse_mode: 'HTML'
        })
      );
    });

    it('should handle errors gracefully', async () => {
      const error = new Error('Database connection failed');
      mockDataManager.getLotteryHistory.mockRejectedValue(error);

      await historyHandler.handleLichSu(mockCtx);

      expect(mockCtx.reply).toHaveBeenCalledWith(
        expect.stringContaining('❌')
      );
    });

    it('should respect maximum limit', async () => {
      mockCtx.message.text = '/lichsu 50'; // Exceeds maxLimit of 20

      await historyHandler.handleLichSu(mockCtx);

      expect(mockDataManager.getLotteryHistory).toHaveBeenCalledWith({
        region: 'north',
        limit: 20, // Should be capped at maxLimit
        sortBy: 'date',
        sortOrder: 'desc'
      });
    });
  });

  describe('_parseHistoryParams', () => {
    it('should parse default parameters', () => {
      const result = historyHandler._parseHistoryParams('/lichsu');
      expect(result).toEqual({
        limit: 5,
        page: 0,
        region: 'north'
      });
    });

    it('should parse limit parameter', () => {
      const result = historyHandler._parseHistoryParams('/lichsu 10');
      expect(result).toEqual({
        limit: 10,
        page: 0,
        region: 'north'
      });
    });

    it('should parse limit and page parameters', () => {
      const result = historyHandler._parseHistoryParams('/lichsu 8 3');
      expect(result).toEqual({
        limit: 8,
        page: 2, // 3 - 1 (convert to 0-based)
        region: 'north'
      });
    });

    it('should parse all parameters', () => {
      const result = historyHandler._parseHistoryParams('/lichsu 15 2 central');
      expect(result).toEqual({
        limit: 15,
        page: 1, // 2 - 1 (convert to 0-based)
        region: 'central'
      });
    });

    it('should handle invalid parameters gracefully', () => {
      const result = historyHandler._parseHistoryParams('/lichsu abc def invalid');
      expect(result).toEqual({
        limit: 5, // Default
        page: 0, // Default
        region: 'north' // Default (invalid region ignored)
      });
    });

    it('should cap limit at maximum', () => {
      const result = historyHandler._parseHistoryParams('/lichsu 100');
      expect(result).toEqual({
        limit: 20, // Capped at maxLimit
        page: 0,
        region: 'north'
      });
    });
  });

  describe('_createHistoryKeyboard', () => {
    it('should create keyboard with navigation buttons', () => {
      const keyboard = historyHandler._createHistoryKeyboard(1, 5, 10);

      expect(keyboard).toEqual({
        inline_keyboard: [
          [
            { text: '⬅️ Trang trước', callback_data: 'history:10:0' },
            { text: '2/5', callback_data: 'noop' },
            { text: 'Trang sau ➡️', callback_data: 'history:10:2' }
          ],
          [
            { text: '🔄 Làm mới', callback_data: 'history:10:0' }
          ]
        ]
      });
    });

    it('should create keyboard without previous button on first page', () => {
      const keyboard = historyHandler._createHistoryKeyboard(0, 3, 5);

      expect(keyboard.inline_keyboard[0]).toEqual([
        { text: '1/3', callback_data: 'noop' },
        { text: 'Trang sau ➡️', callback_data: 'history:5:1' }
      ]);
    });

    it('should create keyboard without next button on last page', () => {
      const keyboard = historyHandler._createHistoryKeyboard(2, 3, 5);

      expect(keyboard.inline_keyboard[0]).toEqual([
        { text: '⬅️ Trang trước', callback_data: 'history:5:1' },
        { text: '3/3', callback_data: 'noop' }
      ]);
    });

    it('should return undefined for single page', () => {
      const keyboard = historyHandler._createHistoryKeyboard(0, 1, 5);

      // Should still have refresh button
      expect(keyboard.inline_keyboard).toHaveLength(1);
      expect(keyboard.inline_keyboard[0]).toContainEqual({
        text: '🔄 Làm mới',
        callback_data: 'history:5:0'
      });
    });
  });

  describe('handleHistoryCallback', () => {
    beforeEach(() => {
      const mockHistoryData = [
        {
          date: new Date('2024-01-15'),
          region: 'north',
          prizes: { special: '12345' },
          numbers: { lo: ['12', '34'], de: ['123'] }
        }
      ];

      mockDataManager.getLotteryHistory.mockResolvedValue(mockHistoryData);
      MessageFormatter.formatHistory.mockReturnValue('Updated history message');
    });

    it('should handle callback successfully', async () => {
      await historyHandler.handleHistoryCallback(mockCtx);

      expect(mockDataManager.getLotteryHistory).toHaveBeenCalled();
      expect(mockCtx.editMessageText).toHaveBeenCalledWith(
        expect.stringContaining('📊'),
        expect.objectContaining({
          parse_mode: 'HTML',
          reply_markup: expect.any(Object)
        })
      );
      expect(mockCtx.answerCbQuery).toHaveBeenCalled();
    });

    it('should handle callback with no data', async () => {
      mockDataManager.getLotteryHistory.mockResolvedValue([]);

      await historyHandler.handleHistoryCallback(mockCtx);

      expect(mockCtx.answerCbQuery).toHaveBeenCalledWith('Không có dữ liệu lịch sử.');
    });

    it('should handle callback errors', async () => {
      mockDataManager.getLotteryHistory.mockRejectedValue(new Error('DB Error'));

      await historyHandler.handleHistoryCallback(mockCtx);

      expect(mockCtx.answerCbQuery).toHaveBeenCalledWith('Lỗi khi tải dữ liệu lịch sử.');
    });
  });

  describe('getHistoryByDate', () => {
    it('should get history for specific date', async () => {
      const mockResult = {
        date: new Date('2024-01-15'),
        region: 'north',
        prizes: { special: '12345' },
        numbers: { lo: ['12', '34'], de: ['123'] }
      };

      mockDataManager.getLotteryResultByDate.mockResolvedValue(mockResult);

      const result = await historyHandler.getHistoryByDate('2024-01-15');

      expect(mockDataManager.getLotteryResultByDate).toHaveBeenCalledWith('2024-01-15', 'north');
      expect(result).toEqual({
        date: mockResult.date,
        region: mockResult.region,
        prizes: mockResult.prizes,
        numbers: mockResult.numbers,
        formatted: expect.stringContaining('📊')
      });
    });

    it('should return null for non-existent date', async () => {
      mockDataManager.getLotteryResultByDate.mockResolvedValue(null);

      const result = await historyHandler.getHistoryByDate('2024-01-15');

      expect(result).toBeNull();
    });
  });

  describe('getHistoryStats', () => {
    it('should calculate history statistics', async () => {
      const mockHistory = [
        {
          date: new Date('2024-01-15'),
          numbers: { lo: ['12', '34', '56'], de: ['123'] },
          prizes: { special: '12345', first: '67890' }
        },
        {
          date: new Date('2024-01-14'),
          numbers: { lo: ['12', '78', '90'], de: ['456'] },
          prizes: { special: '11111', first: '22222' }
        }
      ];

      mockDataManager.getLotteryHistory.mockResolvedValue(mockHistory);

      const stats = await historyHandler.getHistoryStats(30, 'north');

      expect(stats).toEqual({
        totalResults: 2,
        period: 30,
        region: 'north',
        dateRange: {
          from: mockHistory[1].date,
          to: mockHistory[0].date
        },
        numberFrequency: {
          lo: [
            { number: '12', count: 2 },
            { number: '34', count: 1 },
            { number: '56', count: 1 },
            { number: '78', count: 1 },
            { number: '90', count: 1 }
          ],
          de: [
            { number: '123', count: 1 },
            { number: '456', count: 1 }
          ]
        },
        prizeDistribution: {
          special: [['45', 1], ['11', 1]],
          first: [['90', 1], ['22', 1]]
        }
      });
    });

    it('should handle empty history', async () => {
      mockDataManager.getLotteryHistory.mockResolvedValue([]);

      const stats = await historyHandler.getHistoryStats(30, 'north');

      expect(stats).toEqual({
        totalResults: 0,
        period: 30,
        region: 'north'
      });
    });
  });

  describe('_calculateNumberFrequency', () => {
    it('should calculate number frequency correctly', () => {
      const history = [
        { numbers: { lo: ['12', '34'], de: ['123'] } },
        { numbers: { lo: ['12', '56'], de: ['456'] } },
        { numbers: { lo: ['78', '90'], de: ['123'] } }
      ];

      const result = historyHandler._calculateNumberFrequency(history);

      expect(result.lo).toEqual([
        { number: '12', count: 2 },
        { number: '34', count: 1 },
        { number: '56', count: 1 },
        { number: '78', count: 1 },
        { number: '90', count: 1 }
      ]);

      expect(result.de).toEqual([
        { number: '123', count: 2 },
        { number: '456', count: 1 }
      ]);
    });
  });

  describe('_calculatePrizeDistribution', () => {
    it('should calculate prize distribution correctly', () => {
      const history = [
        { prizes: { special: '12345', first: '67890' } },
        { prizes: { special: '11145', first: '22290' } }
      ];

      const result = historyHandler._calculatePrizeDistribution(history);

      expect(result.special).toEqual([['45', 2]]);
      expect(result.first).toEqual([['90', 2]]);
    });
  });
});