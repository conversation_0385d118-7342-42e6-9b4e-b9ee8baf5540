const LotteryCollector = require('../../../lib/data/collectors/lotteryCollector');

// Mock axios completely
jest.mock('axios', () => ({
  create: jest.fn(() => ({
    get: jest.fn()
  }))
}));

describe('LotteryCollector', () => {
  let collector;
  let mockHtml;

  beforeEach(() => {
    collector = new LotteryCollector({
      baseUrl: 'https://xoso.com.vn',
      timeout: 5000,
      retryAttempts: 2
    });

    // Mock HTML response from xoso.com.vn
    mockHtml = `
      <div class="box_kqxs">
        <table>
          <tr>
            <td>ĐB</td>
            <td>12345</td>
          </tr>
          <tr>
            <td>1</td>
            <td>67890</td>
          </tr>
          <tr>
            <td>2</td>
            <td>11111</td>
            <td>22222</td>
          </tr>
          <tr>
            <td>3</td>
            <td>33333</td>
            <td>44444</td>
            <td>55555</td>
          </tr>
          <tr>
            <td>4</td>
            <td>1234</td>
            <td>5678</td>
          </tr>
          <tr>
            <td>5</td>
            <td>9012</td>
            <td>3456</td>
          </tr>
          <tr>
            <td>6</td>
            <td>123</td>
            <td>456</td>
          </tr>
          <tr>
            <td>7</td>
            <td>78</td>
            <td>90</td>
          </tr>
        </table>
      </div>
    `;

    // Reset axios mock
    const axios = require('axios');
    axios.create.mockClear();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('constructor', () => {
    test('should initialize with default options', () => {
      const defaultCollector = new LotteryCollector();
      expect(defaultCollector.options.baseUrl).toBe('https://xoso.com.vn');
      expect(defaultCollector.options.timeout).toBe(30000);
      expect(defaultCollector.options.retryAttempts).toBe(3);
      expect(defaultCollector.axiosInstance).toBeDefined();
    });

    test('should initialize with custom options', () => {
      expect(collector.options.baseUrl).toBe('https://xoso.com.vn');
      expect(collector.options.timeout).toBe(5000);
      expect(collector.options.retryAttempts).toBe(2);
      expect(collector.axiosInstance).toBeDefined();
    });
  });

  describe('_buildUrl', () => {
    test('should build correct URL for north region', () => {
      const date = new Date('2024-01-15');
      const url = collector._buildUrl(date, 'north');
      expect(url).toBe('https://xoso.com.vn/xo-so-mien-bac/xsmb-15-01-2024.html');
    });

    test('should build correct URL for central region', () => {
      const date = new Date('2024-01-15');
      const url = collector._buildUrl(date, 'central');
      expect(url).toBe('https://xoso.com.vn/xo-so-mien-trung/xsmb-15-01-2024.html');
    });

    test('should default to north region for invalid region', () => {
      const date = new Date('2024-01-15');
      const url = collector._buildUrl(date, 'invalid');
      expect(url).toBe('https://xoso.com.vn/xo-so-mien-bac/xsmb-15-01-2024.html');
    });
  });

  describe('_parseResults', () => {
    test('should parse HTML correctly', () => {
      const date = new Date('2024-01-15');
      const results = collector._parseResults(mockHtml, date, 'north');

      expect(results.date).toEqual(date);
      expect(results.region).toBe('north');
      expect(results.source).toBe('xoso.com.vn');
      expect(results.collectedAt).toBeInstanceOf(Date);

      // Check prizes
      expect(results.prizes.special).toBe('12345');
      expect(results.prizes.first).toBe('67890');
      expect(results.prizes.second).toEqual(['11111', '22222']);
      expect(results.prizes.third).toEqual(['33333', '44444', '55555']);

      // Check numbers
      expect(results.numbers.de).toBe('45'); // Last 2 digits of special prize
      expect(results.numbers.lo).toContain('45'); // Should contain lo numbers
      expect(results.numbers.lo).toContain('90');
      expect(results.numbers.lo.length).toBeGreaterThan(0);
    });

    test('should throw error if no results table found', () => {
      const invalidHtml = '<div>No table here</div>';
      const date = new Date('2024-01-15');

      expect(() => {
        collector._parseResults(invalidHtml, date, 'north');
      }).toThrow('Could not find lottery results table in HTML');
    });
  });

  describe('_extractLoNumbers', () => {
    test('should extract lo numbers correctly', () => {
      const prizes = {
        special: '12345',
        first: '67890',
        second: ['11111', '22222'],
        seventh: ['78', '90']
      };
      const numbers = { lo: [], de: [] };

      collector._extractLoNumbers(prizes, numbers);

      expect(numbers.lo).toContain('45'); // from special
      expect(numbers.lo).toContain('90'); // from first
      expect(numbers.lo).toContain('11'); // from second
      expect(numbers.lo).toContain('22'); // from second
      expect(numbers.lo).toContain('78'); // from seventh
      expect(numbers.lo).toContain('90'); // from seventh
    });
  });

  describe('_extractDeNumber', () => {
    test('should extract de number correctly', () => {
      const prizes = { special: '12345' };
      const numbers = { lo: [], de: null };

      collector._extractDeNumber(prizes, numbers);

      expect(numbers.de).toBe('45'); // Last 2 digits of special prize
    });

    test('should handle missing special prize', () => {
      const prizes = { first: '67890' };
      const numbers = { lo: [], de: null };

      collector._extractDeNumber(prizes, numbers);

      expect(numbers.de).toBeNull();
    });
  });

  describe('_validateResults', () => {
    test('should validate correct results', () => {
      const validResults = {
        date: new Date(),
        region: 'north',
        prizes: { special: '12345' },
        numbers: { lo: ['45'], de: '45' }
      };

      expect(() => {
        collector._validateResults(validResults);
      }).not.toThrow();
    });

    test('should throw error for missing required fields', () => {
      const invalidResults = {
        region: 'north',
        prizes: { special: '12345' }
        // Missing date and numbers
      };

      expect(() => {
        collector._validateResults(invalidResults);
      }).toThrow('Missing required fields in parsed results');
    });

    test('should throw error for invalid special prize format', () => {
      const invalidResults = {
        date: new Date(),
        region: 'north',
        prizes: { special: '123' }, // Invalid: not 5 digits
        numbers: { lo: ['45'], de: '45' }
      };

      expect(() => {
        collector._validateResults(invalidResults);
      }).toThrow('Special prize must be 5 digits');
    });

    test('should throw error for missing de number', () => {
      const invalidResults = {
        date: new Date(),
        region: 'north',
        prizes: { special: '12345' },
        numbers: { lo: ['45'], de: null }
      };

      expect(() => {
        collector._validateResults(invalidResults);
      }).toThrow('De number is required');
    });

    test('should throw error for missing lo numbers', () => {
      const invalidResults = {
        date: new Date(),
        region: 'north',
        prizes: { special: '12345' },
        numbers: { lo: [], de: '45' }
      };

      expect(() => {
        collector._validateResults(invalidResults);
      }).toThrow('Lo numbers are required');
    });
  });

  describe('fetchLotteryResults', () => {
    test('should fetch and parse results successfully', async () => {
      const mockAxiosInstance = {
        get: jest.fn().mockResolvedValue({
          status: 200,
          data: mockHtml
        })
      };

      collector.axiosInstance = mockAxiosInstance;

      const date = new Date('2024-01-15');
      const results = await collector.fetchLotteryResults(date, 'north');

      expect(results.date).toEqual(date);
      expect(results.region).toBe('north');
      expect(results.prizes.special).toBe('12345');
      expect(results.numbers.de).toBe('45');
      expect(mockAxiosInstance.get).toHaveBeenCalledWith(
        'https://xoso.com.vn/xo-so-mien-bac/xsmb-15-01-2024.html'
      );
    });

    test('should retry on failure and eventually succeed', async () => {
      const mockAxiosInstance = {
        get: jest.fn()
          .mockRejectedValueOnce(new Error('Network error'))
          .mockResolvedValueOnce({
            status: 200,
            data: mockHtml
          })
      };

      collector.axiosInstance = mockAxiosInstance;

      const date = new Date('2024-01-15');
      const results = await collector.fetchLotteryResults(date, 'north');

      expect(results.prizes.special).toBe('12345');
      expect(mockAxiosInstance.get).toHaveBeenCalledTimes(2);
    });

    test('should throw error after max retries', async () => {
      const mockAxiosInstance = {
        get: jest.fn().mockRejectedValue(new Error('Network error'))
      };

      collector.axiosInstance = mockAxiosInstance;

      const date = new Date('2024-01-15');

      await expect(collector.fetchLotteryResults(date, 'north'))
        .rejects.toThrow('Data collection failed');

      expect(mockAxiosInstance.get).toHaveBeenCalledTimes(2); // retryAttempts = 2
    });
  });

  describe('fetchMultipleDates', () => {
    test('should fetch results for multiple dates', async () => {
      const mockAxiosInstance = {
        get: jest.fn().mockResolvedValue({
          status: 200,
          data: mockHtml
        })
      };

      collector.axiosInstance = mockAxiosInstance;

      const startDate = new Date('2024-01-15');
      const endDate = new Date('2024-01-17');

      const results = await collector.fetchMultipleDates(startDate, endDate, 'north');

      expect(results).toHaveLength(3); // 3 days
      expect(mockAxiosInstance.get).toHaveBeenCalledTimes(3);
      results.forEach(result => {
        expect(result.prizes.special).toBe('12345');
        expect(result.numbers.de).toBe('45');
      });
    });

    test('should continue on individual failures', async () => {
      const mockAxiosInstance = {
        get: jest.fn()
          .mockResolvedValueOnce({ status: 200, data: mockHtml })
          .mockRejectedValueOnce(new Error('Network error'))
          .mockRejectedValueOnce(new Error('Network error'))
          .mockRejectedValueOnce(new Error('Network error'))
          .mockRejectedValueOnce(new Error('Network error'))
          .mockRejectedValueOnce(new Error('Network error'))
          .mockRejectedValueOnce(new Error('Network error'))
      };

      collector.axiosInstance = mockAxiosInstance;

      const startDate = new Date('2024-01-15');
      const endDate = new Date('2024-01-17');

      const results = await collector.fetchMultipleDates(startDate, endDate, 'north');

      expect(results).toHaveLength(1); // Only 1 successful, others fail
      expect(mockAxiosInstance.get).toHaveBeenCalled();
    });
  });

  describe('fetchLatestResults', () => {
    test('should fetch today results first', async () => {
      const mockAxiosInstance = {
        get: jest.fn().mockResolvedValue({
          status: 200,
          data: mockHtml
        })
      };

      collector.axiosInstance = mockAxiosInstance;

      const results = await collector.fetchLatestResults('north');

      expect(results.prizes.special).toBe('12345');
      expect(results.numbers.de).toBe('45');
      expect(mockAxiosInstance.get).toHaveBeenCalledTimes(1);
    });

    test('should fallback to yesterday if today fails', async () => {
      const mockAxiosInstance = {
        get: jest.fn()
          .mockRejectedValueOnce(new Error('No results for today'))
          .mockRejectedValueOnce(new Error('No results for today'))
          .mockResolvedValueOnce({
            status: 200,
            data: mockHtml
          })
      };

      collector.axiosInstance = mockAxiosInstance;

      const results = await collector.fetchLatestResults('north');

      expect(results.prizes.special).toBe('12345');
      expect(mockAxiosInstance.get).toHaveBeenCalledTimes(3); // 2 attempts for today + 1 for yesterday
    });
  });
});
