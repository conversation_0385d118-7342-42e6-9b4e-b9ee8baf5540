// Simple test to verify managers can be imported and instantiated
// This is a basic smoke test to ensure the code structure is correct

const UserManager = require('../../lib/managers/userManager');
const GroupManager = require('../../lib/managers/groupManager');
const AnalyticsManager = require('../../lib/managers/analyticsManager');

describe('Managers Basic Tests', () => {
  test('UserManager can be instantiated', () => {
    const userManager = new UserManager();
    expect(userManager).toBeDefined();
    expect(userManager.options).toBeDefined();
    expect(userManager.isInitialized).toBe(false);
  });

  test('GroupManager can be instantiated', () => {
    const groupManager = new GroupManager();
    expect(groupManager).toBeDefined();
    expect(groupManager.options).toBeDefined();
    expect(groupManager.isInitialized).toBe(false);
  });

  test('AnalyticsManager can be instantiated', () => {
    const analyticsManager = new AnalyticsManager();
    expect(analyticsManager).toBeDefined();
    expect(analyticsManager.options).toBeDefined();
    expect(analyticsManager.isInitialized).toBe(false);
  });

  test('UserManager has required methods', () => {
    const userManager = new UserManager();
    expect(typeof userManager.initialize).toBe('function');
    expect(typeof userManager.registerUser).toBe('function');
    expect(typeof userManager.getUserById).toBe('function');
    expect(typeof userManager.updateUserPreferences).toBe('function');
    expect(typeof userManager.logUserInteraction).toBe('function');
    expect(typeof userManager.getUserStats).toBe('function');
    expect(typeof userManager.cleanup).toBe('function');
  });

  test('GroupManager has required methods', () => {
    const groupManager = new GroupManager();
    expect(typeof groupManager.initialize).toBe('function');
    expect(typeof groupManager.registerGroup).toBe('function');
    expect(typeof groupManager.getGroupById).toBe('function');
    expect(typeof groupManager.getGroupSettings).toBe('function');
    expect(typeof groupManager.updateGroupSettings).toBe('function');
    expect(typeof groupManager.addGroupAdmin).toBe('function');
    expect(typeof groupManager.removeGroupAdmin).toBe('function');
    expect(typeof groupManager.isGroupAdmin).toBe('function');
    expect(typeof groupManager.logGroupInteraction).toBe('function');
    expect(typeof groupManager.cleanup).toBe('function');
  });

  test('AnalyticsManager has required methods', () => {
    const analyticsManager = new AnalyticsManager();
    expect(typeof analyticsManager.initialize).toBe('function');
    expect(typeof analyticsManager.recordDailyAnalytics).toBe('function');
    expect(typeof analyticsManager.getAnalyticsSummary).toBe('function');
    expect(typeof analyticsManager.getRealtimeStats).toBe('function');
    expect(typeof analyticsManager.getPopularNumbers).toBe('function');
    expect(typeof analyticsManager.cleanupOldAnalytics).toBe('function');
    expect(typeof analyticsManager.cleanup).toBe('function');
  });

  test('UserManager validates preferences correctly', () => {
    const userManager = new UserManager();

    // Test valid preferences
    const validPrefs = {
      notifications: true,
      favoriteNumbers: ['12', '34'],
      timezone: 'Asia/Ho_Chi_Minh',
      language: 'vi'
    };

    const validated = userManager._validatePreferences(validPrefs);
    expect(validated.notifications).toBe(true);
    expect(validated.favoriteNumbers).toEqual(['12', '34']);
    expect(validated.timezone).toBe('Asia/Ho_Chi_Minh');
    expect(validated.language).toBe('vi');
  });

  test('UserManager sanitizes metadata for privacy', () => {
    const userManager = new UserManager({ privacyCompliant: true });

    const metadata = {
      command: 'dukienlo',
      queriedNumber: '12',
      success: true,
      ip: '***********',
      location: 'Vietnam',
      timestamp: new Date()
    };

    const sanitized = userManager._sanitizeMetadata(metadata);
    expect(sanitized.command).toBe('dukienlo');
    expect(sanitized.queriedNumber).toBe('12');
    expect(sanitized.success).toBe(true);
    expect(sanitized.ip).toBeUndefined();
    expect(sanitized.location).toBeUndefined();
    expect(sanitized.timestamp).toBeDefined();
  });

  test('GroupManager validates settings correctly', () => {
    const groupManager = new GroupManager();

    // Test valid settings
    const validSettings = {
      dailyPredictions: true,
      predictionTime: '09:00',
      weeklyReports: false,
      weeklyReportDay: 1,
      language: 'vi',
      enabledCommands: ['dukienlo', 'dukiende'],
      maxPredictionsPerMessage: 5
    };

    const validated = groupManager._validateSettings(validSettings);
    expect(validated.dailyPredictions).toBe(true);
    expect(validated.predictionTime).toBe('09:00');
    expect(validated.weeklyReports).toBe(false);
    expect(validated.weeklyReportDay).toBe(1);
    expect(validated.language).toBe('vi');
    expect(validated.enabledCommands).toEqual(['dukienlo', 'dukiende']);
    expect(validated.maxPredictionsPerMessage).toBe(5);
  });

  test('GroupManager validates group data correctly', () => {
    const groupManager = new GroupManager();

    // Valid group data should not throw
    const validGroupData = {
      id: -123456789,
      title: 'Test Group',
      type: 'supergroup'
    };

    expect(() => {
      groupManager._validateGroupData(validGroupData);
    }).not.toThrow();

    // Invalid group data should throw
    const invalidGroupData = {
      id: 'invalid',
      title: null,
      type: 'invalid'
    };

    expect(() => {
      groupManager._validateGroupData(invalidGroupData);
    }).toThrow();
  });

  test('AnalyticsManager calculates growth correctly', () => {
    const analyticsManager = new AnalyticsManager();

    // Test positive growth
    expect(analyticsManager._calculateGrowth(100, 110)).toBe(10);

    // Test negative growth
    expect(analyticsManager._calculateGrowth(100, 90)).toBe(-10);

    // Test zero old value
    expect(analyticsManager._calculateGrowth(0, 50)).toBe(100);

    // Test same values
    expect(analyticsManager._calculateGrowth(100, 100)).toBe(0);
  });

  test('AnalyticsManager aggregates analytics correctly', () => {
    const analyticsManager = new AnalyticsManager();

    const analyticsArray = [
      {
        metrics: {
          users: { totalUsers: 110, activeToday: 25 },
          groups: { totalGroups: 12, activeToday: 6 },
          commands: { totalDukienlo: 60, totalDukiende: 40 },
          popularNumbers: [{ number: '12', count: 10 }]
        }
      },
      {
        metrics: {
          users: { totalUsers: 100, activeToday: 20 },
          groups: { totalGroups: 10, activeToday: 5 },
          commands: { totalDukienlo: 50, totalDukiende: 30 },
          popularNumbers: [{ number: '34', count: 8 }]
        }
      }
    ];

    const result = analyticsManager._aggregateAnalytics(analyticsArray);

    expect(result.totalDays).toBe(2);
    expect(result.averages.usersPerDay).toBe(22.5); // (25 + 20) / 2
    expect(result.averages.groupsPerDay).toBe(5.5);  // (6 + 5) / 2
    expect(result.averages.commandsPerDay).toBe(90); // ((60+40) + (50+30)) / 2 = 180/2 = 90
    expect(result.trends.userGrowth).toBe(10);    // (110 - 100) / 100 * 100
    expect(result.trends.groupGrowth).toBe(20);   // (12 - 10) / 10 * 100
  });

  test('AnalyticsManager handles empty analytics array', () => {
    const analyticsManager = new AnalyticsManager();

    const result = analyticsManager._aggregateAnalytics([]);

    expect(result).toEqual({
      totalDays: 0,
      averages: {},
      trends: {},
      summary: {}
    });
  });

  test('All managers have proper default options', () => {
    const userManager = new UserManager();
    const groupManager = new GroupManager();
    const analyticsManager = new AnalyticsManager();

    // UserManager defaults
    expect(userManager.options.cacheEnabled).toBe(true);
    expect(userManager.options.cacheTTL).toBe(3600);
    expect(userManager.options.privacyCompliant).toBe(true);

    // GroupManager defaults
    expect(groupManager.options.cacheEnabled).toBe(true);
    expect(groupManager.options.cacheTTL).toBe(21600);
    expect(groupManager.options.maxGroupsPerUser).toBe(50);
    expect(groupManager.options.defaultSettings).toBeDefined();

    // AnalyticsManager defaults
    expect(analyticsManager.options.cacheEnabled).toBe(true);
    expect(analyticsManager.options.cacheTTL).toBe(900);
    expect(analyticsManager.options.retentionDays).toBe(90);
  });

  test('Managers can be configured with custom options', () => {
    const customOptions = {
      cacheEnabled: false,
      cacheTTL: 1800,
      privacyCompliant: false
    };

    const userManager = new UserManager(customOptions);
    expect(userManager.options.cacheEnabled).toBe(false);
    expect(userManager.options.cacheTTL).toBe(1800);
    expect(userManager.options.privacyCompliant).toBe(false);

    const groupManager = new GroupManager(customOptions);
    expect(groupManager.options.cacheEnabled).toBe(false);
    expect(groupManager.options.cacheTTL).toBe(1800);

    const analyticsManager = new AnalyticsManager(customOptions);
    expect(analyticsManager.options.cacheEnabled).toBe(false);
    expect(analyticsManager.options.cacheTTL).toBe(1800);
  });
});
