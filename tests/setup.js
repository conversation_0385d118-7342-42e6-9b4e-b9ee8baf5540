// Jest setup file
// Global test configuration and utilities

// Load environment variables for testing
require('dotenv').config({ path: '.env.test' });

// Global test utilities
global.testUtils = {
  // Mock Telegram context
  createMockTelegramContext: (overrides = {}) => ({
    from: {
      id: 123456789,
      username: 'testuser',
      first_name: 'Test',
      last_name: 'User',
      ...overrides.from
    },
    chat: {
      id: -987654321,
      type: 'group',
      title: 'Test Group',
      ...overrides.chat
    },
    message: {
      text: '/test',
      date: Math.floor(Date.now() / 1000),
      ...overrides.message
    },
    reply: jest.fn(),
    replyWithMarkdown: jest.fn(),
    ...overrides
  }),

  // Mock lottery data
  createMockLotteryResult: (overrides = {}) => ({
    date: new Date(),
    region: 'north',
    prizes: {
      special: '12345',
      first: '67890',
      second: ['11111', '22222'],
      third: ['33333', '44444', '55555'],
      fourth: ['66666', '77777', '88888', '99999'],
      fifth: ['00000', '11111', '22222', '33333', '44444', '55555'],
      sixth: ['66666', '77777', '88888'],
      seventh: ['99999', '00000', '11111', '22222']
    },
    numbers: {
      lo: ['12', '34', '56', '78', '90'],
      de: ['12', '34', '56']
    },
    ...overrides
  }),

  // Mock prediction data
  createMockPrediction: (overrides = {}) => ({
    predictions: [
      { number: '12', confidence: 85, reasoning: 'High frequency' },
      { number: '34', confidence: 78, reasoning: 'Trend analysis' },
      { number: '56', confidence: 72, reasoning: 'Pattern match' }
    ],
    confidence: 78,
    method: 'ensemble',
    ...overrides
  }),

  // Wait for async operations
  wait: (ms = 100) => new Promise(resolve => setTimeout(resolve, ms)),

  // Clean up test data
  cleanup: async () => {
    // Clean up test database collections if needed
    // This will be implemented when database tests are added
  }
};

// Mock external services by default
jest.mock('node-telegram-bot-api');
jest.mock('openai');
jest.mock('@tensorflow/tfjs-node');

// Console suppression for cleaner test output
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;

beforeAll(() => {
  // Suppress console.error and console.warn in tests unless explicitly needed
  console.error = jest.fn();
  console.warn = jest.fn();
});

afterAll(() => {
  // Restore original console methods
  console.error = originalConsoleError;
  console.warn = originalConsoleWarn;
});

// Global test cleanup
afterEach(async () => {
  // Clear all mocks after each test
  jest.clearAllMocks();

  // Clean up test data
  await global.testUtils.cleanup();
});