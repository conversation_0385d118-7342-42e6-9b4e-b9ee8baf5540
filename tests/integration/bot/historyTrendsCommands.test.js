const HistoryHandler = require('../../../lib/bot/handlers/history');
const TrendsHandler = require('../../../lib/bot/handlers/trends');
const DataManager = require('../../../lib/data');
const PredictionEngine = require('../../../lib/prediction');

// Mock external dependencies
jest.mock('../../../lib/logger', () => () => ({
  logInfo: jest.fn(),
  logError: jest.fn()
}));

describe('History and Trends Commands Integration', () => {
  let dataManager;
  let predictionEngine;
  let historyHandler;
  let trendsHandler;
  let mockCtx;

  beforeEach(() => {
    // Initialize components
    dataManager = new DataManager();
    predictionEngine = new PredictionEngine(dataManager);
    historyHandler = new HistoryHandler(dataManager);
    trendsHandler = new TrendsHandler(predictionEngine, dataManager);

    // Mock Telegram context
    mockCtx = {
      message: { text: '/lichsu' },
      from: { id: 123456, username: 'testuser' },
      chat: { id: -789012, title: 'Test Group' },
      reply: jest.fn(),
      editMessageText: jest.fn(),
      answerCbQuery: jest.fn(),
      callbackQuery: { data: 'history:5:0' }
    };

    // Mock data manager methods
    dataManager.getLotteryHistory = jest.fn();
    dataManager.getLotteryResultByDate = jest.fn();
    dataManager.getNumberStatistics = jest.fn();
    dataManager.getHotColdNumbers = jest.fn();

    // Mock prediction engine methods
    predictionEngine.getTrends = jest.fn();
    predictionEngine.initialize = jest.fn().mockResolvedValue();

    // Reset mocks
    jest.clearAllMocks();
  });

  describe('History Command Integration', () => {
    const mockHistoryData = [
      {
        _id: '507f1f77bcf86cd799439011',
        date: new Date('2024-01-15T00:00:00.000Z'),
        region: 'north',
        prizes: {
          special: '12345',
          first: '67890',
          second: ['11111', '22222'],
          third: ['33333', '44444', '55555']
        },
        numbers: {
          lo: ['12', '34', '45', '67', '89', '90', '11', '22'],
          de: ['123', '345', '678']
        },
        createdAt: new Date('2024-01-15T10:00:00.000Z'),
        updatedAt: new Date('2024-01-15T10:00:00.000Z')
      },
      {
        _id: '507f1f77bcf86cd799439012',
        date: new Date('2024-01-14T00:00:00.000Z'),
        region: 'north',
        prizes: {
          special: '98765',
          first: '54321',
          second: ['77777', '88888'],
          third: ['99999', '00000', '12121']
        },
        numbers: {
          lo: ['98', '76', '65', '54', '32', '21', '77', '88'],
          de: ['987', '765', '543']
        },
        createdAt: new Date('2024-01-14T10:00:00.000Z'),
        updatedAt: new Date('2024-01-14T10:00:00.000Z')
      }
    ];

    beforeEach(() => {
      dataManager.getLotteryHistory.mockResolvedValue(mockHistoryData);
    });

    it('should handle /lichsu command with real data flow', async () => {
      await historyHandler.handleLichSu(mockCtx);

      expect(dataManager.getLotteryHistory).toHaveBeenCalledWith({
        region: 'north',
        limit: 5,
        sortBy: 'date',
        sortOrder: 'desc'
      });

      expect(mockCtx.reply).toHaveBeenCalledWith(
        expect.stringContaining('📊'),
        expect.objectContaining({
          parse_mode: 'HTML',
          reply_markup: expect.objectContaining({
            inline_keyboard: expect.any(Array)
          })
        })
      );
    });

    it('should handle pagination correctly with real data', async () => {
      // Create larger dataset for pagination testing
      const largeDataset = Array(15).fill(null).map((_, index) => ({
        ...mockHistoryData[0],
        _id: `507f1f77bcf86cd79943901${index}`,
        date: new Date(`2024-01-${15 - index}T00:00:00.000Z`),
        prizes: { ...mockHistoryData[0].prizes, special: `1234${index}` }
      }));

      dataManager.getLotteryHistory.mockResolvedValue(largeDataset);
      mockCtx.message.text = '/lichsu 5 2'; // Page 2, 5 items

      await historyHandler.handleLichSu(mockCtx);

      expect(dataManager.getLotteryHistory).toHaveBeenCalledWith({
        region: 'north',
        limit: 10, // 5 * 2
        sortBy: 'date',
        sortOrder: 'desc'
      });

      const replyCall = mockCtx.reply.mock.calls[0];
      expect(replyCall[0]).toContain('Trang 2/3');
    });

    it('should handle history callback with real data flow', async () => {
      mockCtx.callbackQuery.data = 'history:10:1';

      await historyHandler.handleHistoryCallback(mockCtx);

      expect(dataManager.getLotteryHistory).toHaveBeenCalledWith({
        region: 'north',
        limit: 30, // 10 * (1 + 2)
        sortBy: 'date',
        sortOrder: 'desc'
      });

      expect(mockCtx.editMessageText).toHaveBeenCalled();
      expect(mockCtx.answerCbQuery).toHaveBeenCalled();
    });

    it('should get history statistics with real calculations', async () => {
      const stats = await historyHandler.getHistoryStats(30, 'north');

      expect(dataManager.getLotteryHistory).toHaveBeenCalledWith({
        region: 'north',
        fromDate: expect.any(Date),
        toDate: expect.any(Date)
      });

      expect(stats).toEqual({
        totalResults: 2,
        period: 30,
        region: 'north',
        dateRange: {
          from: mockHistoryData[1].date,
          to: mockHistoryData[0].date
        },
        numberFrequency: {
          lo: expect.arrayContaining([
            expect.objectContaining({ number: expect.any(String), count: expect.any(Number) })
          ]),
          de: expect.arrayContaining([
            expect.objectContaining({ number: expect.any(String), count: expect.any(Number) })
          ])
        },
        prizeDistribution: expect.objectContaining({
          special: expect.any(Array),
          first: expect.any(Array)
        })
      });
    });
  });

  describe('Trends Command Integration', () => {
    const mockTrendsData = {
      hot: [
        { number: '12', frequency: 15, count: 15 },
        { number: '34', frequency: 12, count: 12 },
        { number: '56', frequency: 10, count: 10 }
      ],
      cold: [
        { number: '78', frequency: 2, count: 2 },
        { number: '90', frequency: 1, count: 1 },
        { number: '13', frequency: 1, count: 1 }
      ],
      type: 'lo',
      period: 'weekly'
    };

    const mockStatistics = [
      { number: '12', count: 15, lastSeen: new Date('2024-01-15'), frequency: '7.50' },
      { number: '34', count: 12, lastSeen: new Date('2024-01-14'), frequency: '6.00' },
      { number: '78', count: 2, lastSeen: new Date('2024-01-10'), frequency: '1.00' }
    ];

    beforeEach(() => {
      predictionEngine.getTrends.mockResolvedValue(mockTrendsData);
      dataManager.getNumberStatistics.mockResolvedValue(mockStatistics);
      dataManager.getLotteryHistory.mockResolvedValue([
        { date: new Date('2024-01-15'), numbers: { lo: ['12', '34'] } },
        { date: new Date('2024-01-14'), numbers: { lo: ['12', '56'] } }
      ]);
    });

    it('should handle /xuhuonglo command with real data flow', async () => {
      mockCtx.message.text = '/xuhuonglo';

      await trendsHandler.handleXuHuongLo(mockCtx);

      expect(predictionEngine.getTrends).toHaveBeenCalledWith('lo', 'weekly');
      expect(dataManager.getNumberStatistics).toHaveBeenCalledWith('lo', 7);

      expect(mockCtx.reply).toHaveBeenCalledWith(
        expect.stringContaining('📈'),
        expect.objectContaining({
          parse_mode: 'HTML',
          reply_markup: expect.objectContaining({
            inline_keyboard: expect.any(Array)
          })
        })
      );
    });

    it('should handle /xuhuongde command with real data flow', async () => {
      mockCtx.message.text = '/xuhuongde';
      const deTrendsData = { ...mockTrendsData, type: 'de' };
      predictionEngine.getTrends.mockResolvedValue(deTrendsData);

      await trendsHandler.handleXuHuongDe(mockCtx);

      expect(predictionEngine.getTrends).toHaveBeenCalledWith('de', 'weekly');
      expect(dataManager.getNumberStatistics).toHaveBeenCalledWith('de', 7);

      expect(mockCtx.reply).toHaveBeenCalledWith(
        expect.stringContaining('📈'),
        expect.objectContaining({
          parse_mode: 'HTML'
        })
      );
    });

    it('should handle trends callback with period change', async () => {
      mockCtx.callbackQuery.data = 'trends:lo:monthly';

      await trendsHandler.handleTrendsCallback(mockCtx);

      expect(predictionEngine.getTrends).toHaveBeenCalledWith('lo', 'monthly');
      expect(dataManager.getNumberStatistics).toHaveBeenCalledWith('lo', 30);

      expect(mockCtx.editMessageText).toHaveBeenCalled();
      expect(mockCtx.answerCbQuery).toHaveBeenCalled();
    });

    it('should calculate streak correctly with real data', async () => {
      const streak = await trendsHandler._calculateStreak('12', 'lo');

      expect(dataManager.getLotteryHistory).toHaveBeenCalledWith({
        limit: 30,
        sortBy: 'date',
        sortOrder: 'desc'
      });

      expect(streak).toBe(2); // '12' appears in both recent results
    });

    it('should get comprehensive trends with all periods', async () => {
      const result = await trendsHandler.getComprehensiveTrends('lo', {
        includePatterns: true
      });

      expect(predictionEngine.getTrends).toHaveBeenCalledTimes(3); // daily, weekly, monthly
      expect(predictionEngine.getTrends).toHaveBeenCalledWith('lo', 'daily');
      expect(predictionEngine.getTrends).toHaveBeenCalledWith('lo', 'weekly');
      expect(predictionEngine.getTrends).toHaveBeenCalledWith('lo', 'monthly');

      expect(result).toEqual({
        type: 'lo',
        analysis: {
          daily: expect.any(Object),
          weekly: expect.any(Object),
          monthly: expect.any(Object),
          patterns: expect.objectContaining({
            consecutive: expect.any(Array),
            evenOdd: expect.any(Object),
            sumRanges: expect.any(Array),
            digitPatterns: expect.any(Array)
          })
        },
        generatedAt: expect.any(Date),
        summary: expect.objectContaining({
          hotNumbers: expect.any(Array),
          coldNumbers: expect.any(Array),
          recommendations: expect.any(Array)
        })
      });
    });
  });

  describe('Cross-Handler Integration', () => {
    it('should work together for comprehensive lottery analysis', async () => {
      // Setup data for both handlers
      const historyData = [
        {
          date: new Date('2024-01-15'),
          region: 'north',
          prizes: { special: '12345' },
          numbers: { lo: ['12', '34', '56'], de: ['123'] }
        }
      ];

      const trendsData = {
        hot: [{ number: '12', frequency: 15 }],
        cold: [{ number: '78', frequency: 2 }]
      };

      dataManager.getLotteryHistory.mockResolvedValue(historyData);
      predictionEngine.getTrends.mockResolvedValue(trendsData);
      dataManager.getNumberStatistics.mockResolvedValue([
        { number: '12', count: 15, lastSeen: new Date('2024-01-15') }
      ]);

      // Test history command
      await historyHandler.handleLichSu(mockCtx);
      expect(mockCtx.reply).toHaveBeenCalledTimes(1);

      // Reset reply mock
      mockCtx.reply.mockClear();

      // Test trends command
      mockCtx.message.text = '/xuhuonglo';
      await trendsHandler.handleXuHuongLo(mockCtx);
      expect(mockCtx.reply).toHaveBeenCalledTimes(1);

      // Both should have used the same data manager
      expect(dataManager.getLotteryHistory).toHaveBeenCalledTimes(2); // Once for history, once for trends
    });

    it('should handle data consistency between handlers', async () => {
      const sharedHistoryData = [
        {
          date: new Date('2024-01-15'),
          numbers: { lo: ['12', '34'], de: ['123'] }
        },
        {
          date: new Date('2024-01-14'),
          numbers: { lo: ['12', '56'], de: ['456'] }
        }
      ];

      dataManager.getLotteryHistory.mockResolvedValue(sharedHistoryData);

      // Get history stats
      const historyStats = await historyHandler.getHistoryStats(7, 'north');

      // Get trends data
      predictionEngine.getTrends.mockResolvedValue({
        hot: [{ number: '12', frequency: 2 }],
        cold: []
      });

      const trendsData = await trendsHandler.getComprehensiveTrends('lo');

      // Both should reflect the same underlying data
      expect(historyStats.numberFrequency.lo).toContainEqual(
        expect.objectContaining({ number: '12', count: 2 })
      );

      expect(trendsData.analysis.daily.hot).toContainEqual(
        expect.objectContaining({ number: '12' })
      );
    });
  });

  describe('Error Handling Integration', () => {
    it('should handle data manager failures gracefully', async () => {
      const dbError = new Error('Database connection failed');
      dataManager.getLotteryHistory.mockRejectedValue(dbError);

      await historyHandler.handleLichSu(mockCtx);

      expect(mockCtx.reply).toHaveBeenCalledWith(
        expect.stringContaining('❌')
      );
    });

    it('should handle prediction engine failures gracefully', async () => {
      const engineError = new Error('Prediction engine failed');
      predictionEngine.getTrends.mockRejectedValue(engineError);

      mockCtx.message.text = '/xuhuonglo';
      await trendsHandler.handleXuHuongLo(mockCtx);

      expect(mockCtx.reply).toHaveBeenCalledWith(
        expect.stringContaining('❌')
      );
    });

    it('should handle partial data gracefully', async () => {
      // History with missing data
      dataManager.getLotteryHistory.mockResolvedValue([
        { date: new Date('2024-01-15'), numbers: { lo: null, de: null } }
      ]);

      await historyHandler.handleLichSu(mockCtx);

      expect(mockCtx.reply).toHaveBeenCalled();
      // Should not crash, should handle gracefully
    });
  });

  describe('Performance Integration', () => {
    it('should handle large datasets efficiently', async () => {
      // Create large dataset
      const largeDataset = Array(1000).fill(null).map((_, index) => ({
        date: new Date(`2024-01-${(index % 30) + 1}`),
        region: 'north',
        numbers: {
          lo: [`${index % 100}`.padStart(2, '0'), `${(index + 1) % 100}`.padStart(2, '0')],
          de: [`${index % 1000}`.padStart(3, '0')]
        }
      }));

      dataManager.getLotteryHistory.mockResolvedValue(largeDataset);

      const startTime = Date.now();
      const stats = await historyHandler.getHistoryStats(30, 'north');
      const endTime = Date.now();

      expect(stats.totalResults).toBe(1000);
      expect(endTime - startTime).toBeLessThan(1000); // Should complete within 1 second
    });

    it('should cache results appropriately', async () => {
      const historyData = [
        { date: new Date('2024-01-15'), numbers: { lo: ['12'], de: ['123'] } }
      ];

      dataManager.getLotteryHistory.mockResolvedValue(historyData);

      // First call
      await historyHandler.handleLichSu(mockCtx);

      // Second call with same parameters
      mockCtx.reply.mockClear();
      await historyHandler.handleLichSu(mockCtx);

      // Should have called data manager twice (no caching in this test setup)
      expect(dataManager.getLotteryHistory).toHaveBeenCalledTimes(2);
    });
  });
});