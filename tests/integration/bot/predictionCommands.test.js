const PredictionHandler = require('../../../lib/bot/handlers/prediction');
const MessageFormatter = require('../../../lib/bot/utils/formatter');

// Mock dependencies
jest.mock('../../../lib/logger', () => () => ({
  logInfo: jest.fn(),
  logError: jest.fn()
}));

// Mock the classes directly
const mockPredictionEngine = {
  predictLo: jest.fn(),
  predictDe: jest.fn(),
  initialize: jest.fn().mockResolvedValue(true)
};

const mockLLMService = {
  generatePredictionReport: jest.fn(),
  initialize: jest.fn().mockResolvedValue(true)
};

const mockDataManager = {
  logUserInteraction: jest.fn().mockResolvedValue(true),
  updateUserStats: jest.fn().mockResolvedValue(true)
};

describe('Prediction Commands Integration Tests', () => {
  let predictionHandler;
  let mockBot;
  let mockCtx;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Mock Telegram bot
    mockBot = {
      sendMessage: jest.fn().mockResolvedValue({ message_id: 123 }),
      editMessageText: jest.fn().mockResolvedValue(true)
    };

    // Mock context
    mockCtx = {
      userId: 12345,
      chatId: 67890,
      bot: mockBot,
      message: { text: '/dukienlo' }
    };

    // Create handler instance
    predictionHandler = new PredictionHandler(
      mockPredictionEngine,
      mockLLMService,
      mockDataManager
    );
  });

  describe('handleDukienLo', () => {
    it('should successfully handle lo prediction request', async () => {
      // Arrange
      const mockPrediction = {
        predictions: [
          { number: '12', confidence: 85, reasoning: 'High frequency' },
          { number: '34', confidence: 78, reasoning: 'Trend analysis' },
          { number: '56', confidence: 72, reasoning: 'Statistical model' }
        ],
        confidence: 80,
        method: 'ensemble',
        type: 'lo',
        timestamp: new Date()
      };

      mockPredictionEngine.predictLo.mockResolvedValue(mockPrediction);
      mockLLMService.generatePredictionReport.mockResolvedValue('AI analysis report');

      // Act
      await predictionHandler.handleDukienLo(mockCtx);

      // Assert
      expect(mockBot.sendMessage).toHaveBeenCalledWith(
        mockCtx.chatId,
        '🔄 Đang phân tích dữ liệu và tạo dự đoán số lô...',
        { parse_mode: 'HTML' }
      );

      expect(mockPredictionEngine.predictLo).toHaveBeenCalledWith({
        type: 'lo',
        maxPredictions: 10,
        includeConfidence: true
      });

      expect(mockLLMService.generatePredictionReport).toHaveBeenCalledWith(
        mockPrediction.predictions.slice(0, 5),
        [],
        'lo'
      );

      expect(mockBot.editMessageText).toHaveBeenCalled();
      expect(mockDataManager.logUserInteraction).toHaveBeenCalledWith(
        mockCtx.userId,
        'dukienlo',
        expect.objectContaining({
          chatId: mockCtx.chatId,
          predictionCount: 3,
          confidence: 80
        })
      );
    });

    it('should handle prediction engine errors gracefully', async () => {
      // Arrange
      const errorMessage = 'Prediction engine failed';
      mockPredictionEngine.predictLo.mockRejectedValue(new Error(errorMessage));

      // Act
      await predictionHandler.handleDukienLo(mockCtx);

      // Assert
      expect(mockBot.sendMessage).toHaveBeenCalledWith(
        mockCtx.chatId,
        expect.stringContaining('❌ Đã xảy ra lỗi khi tạo dự đoán số lô'),
        { parse_mode: 'HTML' }
      );
    });

    it('should handle empty predictions', async () => {
      // Arrange
      const mockPrediction = {
        predictions: [],
        confidence: 0,
        method: 'statistical',
        type: 'lo'
      };

      mockPredictionEngine.predictLo.mockResolvedValue(mockPrediction);

      // Act
      await predictionHandler.handleDukienLo(mockCtx);

      // Assert
      expect(mockBot.editMessageText).toHaveBeenCalledWith(
        expect.stringContaining('❌ Không có dự đoán khả dụng cho hôm nay'),
        expect.objectContaining({
          chat_id: mockCtx.chatId,
          message_id: 123
        })
      );
    });

    it('should continue without LLM report if LLM service fails', async () => {
      // Arrange
      const mockPrediction = {
        predictions: [
          { number: '12', confidence: 85, reasoning: 'High frequency' }
        ],
        confidence: 80,
        method: 'statistical',
        type: 'lo'
      };

      mockPredictionEngine.predictLo.mockResolvedValue(mockPrediction);
      mockLLMService.generatePredictionReport.mockRejectedValue(new Error('LLM failed'));

      // Act
      await predictionHandler.handleDukienLo(mockCtx);

      // Assert
      expect(mockBot.editMessageText).toHaveBeenCalled();
      expect(mockDataManager.logUserInteraction).toHaveBeenCalled();
    });

    it('should handle DataManager logging errors gracefully', async () => {
      // Arrange
      const mockPrediction = {
        predictions: [{ number: '12', confidence: 85 }],
        confidence: 80,
        method: 'statistical',
        type: 'lo'
      };

      mockPredictionEngine.predictLo.mockResolvedValue(mockPrediction);
      mockDataManager.logUserInteraction.mockRejectedValue(new Error('Logging failed'));

      // Act
      await predictionHandler.handleDukienLo(mockCtx);

      // Assert - Should still complete successfully
      expect(mockBot.editMessageText).toHaveBeenCalled();
    });
  });

  describe('handleDukienDe', () => {
    it('should successfully handle de prediction request', async () => {
      // Arrange
      const mockPrediction = {
        predictions: [
          { number: '78', confidence: 82, reasoning: 'Pattern analysis' },
          { number: '90', confidence: 75, reasoning: 'Frequency model' }
        ],
        confidence: 78,
        method: 'lstm',
        type: 'de',
        timestamp: new Date()
      };

      mockPredictionEngine.predictDe.mockResolvedValue(mockPrediction);
      mockLLMService.generatePredictionReport.mockResolvedValue('AI analysis for de');

      // Act
      await predictionHandler.handleDukienDe(mockCtx);

      // Assert
      expect(mockBot.sendMessage).toHaveBeenCalledWith(
        mockCtx.chatId,
        '🔄 Đang phân tích dữ liệu và tạo dự đoán số đề...',
        { parse_mode: 'HTML' }
      );

      expect(mockPredictionEngine.predictDe).toHaveBeenCalledWith({
        type: 'de',
        maxPredictions: 8,
        includeConfidence: true
      });

      expect(mockLLMService.generatePredictionReport).toHaveBeenCalledWith(
        mockPrediction.predictions.slice(0, 5),
        [],
        'de'
      );

      expect(mockDataManager.logUserInteraction).toHaveBeenCalledWith(
        mockCtx.userId,
        'dukiende',
        expect.objectContaining({
          chatId: mockCtx.chatId,
          predictionCount: 2,
          confidence: 78
        })
      );
    });

    it('should handle prediction with error property', async () => {
      // Arrange
      const mockPrediction = {
        error: 'Insufficient data',
        predictions: [],
        confidence: 0
      };

      mockPredictionEngine.predictDe.mockResolvedValue(mockPrediction);

      // Act
      await predictionHandler.handleDukienDe(mockCtx);

      // Assert
      expect(mockBot.sendMessage).toHaveBeenCalledWith(
        mockCtx.chatId,
        expect.stringContaining('❌ Đã xảy ra lỗi khi tạo dự đoán số đề'),
        { parse_mode: 'HTML' }
      );
    });
  });

  describe('Response Formatting', () => {
    it('should format prediction response correctly', async () => {
      // Arrange
      const mockPrediction = {
        predictions: [
          { number: '12', confidence: 85, reasoning: 'High frequency' },
          { number: '34', confidence: 78, reasoning: 'Trend analysis' }
        ],
        confidence: 80,
        method: 'ensemble',
        type: 'lo',
        confidenceFactors: {
          baseConfidence: 70,
          consistencyBonus: 5,
          modelAgreementBonus: 3,
          accuracyBonus: 2
        }
      };

      mockPredictionEngine.predictLo.mockResolvedValue(mockPrediction);

      // Act
      await predictionHandler.handleDukienLo(mockCtx);

      // Assert
      const editCall = mockBot.editMessageText.mock.calls[0];
      const responseMessage = editCall[0];

      expect(responseMessage).toContain('🎲');
      expect(responseMessage).toContain('Dự Đoán Số Lô');
      expect(responseMessage).toContain('📊 Độ tin cậy:');
      expect(responseMessage).toContain('🤖 Phương pháp:');
      expect(responseMessage).toContain('🏆 Top Dự Đoán:');
      expect(responseMessage).toContain('1. <code>12</code>');
      expect(responseMessage).toContain('2. <code>34</code>');
      expect(responseMessage).toContain('📈 Chi tiết độ tin cậy:');
      expect(responseMessage).toContain('💡');
    });

    it('should format error responses correctly', async () => {
      // Arrange
      mockPredictionEngine.predictLo.mockRejectedValue(new Error('DataManager not available'));

      // Act
      await predictionHandler.handleDukienLo(mockCtx);

      // Assert
      const sendCall = mockBot.sendMessage.mock.calls[1]; // Second call is the error message
      const errorMessage = sendCall[1];

      expect(errorMessage).toContain('❌ Dịch vụ dữ liệu hiện không khả dụng');
      expect(errorMessage).toContain('Vui lòng thử lại sau 5-10 phút');
    });
  });

  describe('Method Display Names', () => {
    it('should display correct method names', () => {
      const handler = new PredictionHandler(mockPredictionEngine, mockLLMService, mockDataManager);

      expect(handler._getMethodDisplayName('statistical')).toBe('Thống kê');
      expect(handler._getMethodDisplayName('lstm')).toBe('LSTM Neural Network');
      expect(handler._getMethodDisplayName('ensemble')).toBe('Kết hợp nhiều mô hình');
      expect(handler._getMethodDisplayName('combined')).toBe('Tổng hợp');
      expect(handler._getMethodDisplayName('unknown')).toBe('unknown');
    });
  });

  describe('Prediction Validation', () => {
    it('should validate prediction data correctly', () => {
      const handler = new PredictionHandler(mockPredictionEngine, mockLLMService, mockDataManager);

      // Valid prediction
      const validPrediction = {
        predictions: [
          { number: '12', confidence: 85 },
          { number: '34', confidence: 78 }
        ]
      };

      expect(() => handler._validatePrediction(validPrediction, 'lo')).not.toThrow();

      // Invalid prediction - null
      expect(() => handler._validatePrediction(null, 'lo')).toThrow('Prediction data is null or undefined');

      // Invalid prediction - with error
      const errorPrediction = { error: 'Test error' };
      expect(() => handler._validatePrediction(errorPrediction, 'lo')).toThrow('Test error');

      // Invalid prediction - missing predictions array
      const noPredictions = {};
      expect(() => handler._validatePrediction(noPredictions, 'lo')).toThrow('Invalid prediction format');

      // Invalid prediction - invalid number
      const invalidNumber = {
        predictions: [{ number: null, confidence: 85 }]
      };
      expect(() => handler._validatePrediction(invalidNumber, 'lo')).toThrow('Invalid prediction: number is missing');

      // Invalid prediction - invalid confidence
      const invalidConfidence = {
        predictions: [{ number: '12', confidence: 150 }]
      };
      expect(() => handler._validatePrediction(invalidConfidence, 'lo')).toThrow('Invalid prediction: confidence must be between 0 and 100');
    });

    it('should validate edge cases in prediction data', () => {
      const handler = new PredictionHandler(mockPredictionEngine, mockLLMService, mockDataManager);

      // Empty predictions array (should be valid)
      const emptyPredictions = { predictions: [] };
      expect(() => handler._validatePrediction(emptyPredictions, 'lo')).not.toThrow();

      // Prediction with zero confidence (should be valid)
      const zeroConfidence = {
        predictions: [{ number: '12', confidence: 0 }]
      };
      expect(() => handler._validatePrediction(zeroConfidence, 'lo')).not.toThrow();

      // Prediction with undefined confidence (should be valid)
      const undefinedConfidence = {
        predictions: [{ number: '12' }]
      };
      expect(() => handler._validatePrediction(undefinedConfidence, 'lo')).not.toThrow();

      // Prediction with string number (should be valid)
      const stringNumber = {
        predictions: [{ number: '05', confidence: 85 }]
      };
      expect(() => handler._validatePrediction(stringNumber, 'lo')).not.toThrow();

      // Prediction with negative confidence (should be invalid)
      const negativeConfidence = {
        predictions: [{ number: '12', confidence: -5 }]
      };
      expect(() => handler._validatePrediction(negativeConfidence, 'lo')).toThrow('confidence must be between 0 and 100');
    });
  });

  describe('Handler Registration', () => {
    it('should return correct handler functions', () => {
      const handler = new PredictionHandler(mockPredictionEngine, mockLLMService, mockDataManager);
      const handlers = handler.getHandlers();

      expect(handlers).toHaveProperty('dukienlo');
      expect(handlers).toHaveProperty('dukiende');
      expect(typeof handlers.dukienlo).toBe('function');
      expect(typeof handlers.dukiende).toBe('function');
    });
  });

  describe('Enhanced Error Handling', () => {
    it('should handle timeout errors with appropriate message', async () => {
      // Arrange
      mockPredictionEngine.predictLo.mockImplementation(() =>
        new Promise(resolve => setTimeout(() => resolve({ predictions: [] }), 15000))
      );

      // Act
      await predictionHandler.handleDukienLo(mockCtx);

      // Assert
      expect(mockBot.sendMessage).toHaveBeenCalledWith(
        mockCtx.chatId,
        expect.stringContaining('❌ Lỗi kết nối mạng khi tạo'),
        { parse_mode: 'HTML' }
      );
    });

    it('should handle rate limit errors with specific message', async () => {
      // Arrange
      const rateLimitError = new Error('rate limit exceeded');
      mockPredictionEngine.predictLo.mockRejectedValue(rateLimitError);

      // Act
      await predictionHandler.handleDukienLo(mockCtx);

      // Assert
      expect(mockBot.sendMessage).toHaveBeenCalledWith(
        mockCtx.chatId,
        expect.stringContaining('❌ Bạn đã gửi quá nhiều yêu cầu'),
        { parse_mode: 'HTML' }
      );
    });

    it('should handle network errors with appropriate guidance', async () => {
      // Arrange
      const networkError = new Error('network timeout');
      mockPredictionEngine.predictLo.mockRejectedValue(networkError);

      // Act
      await predictionHandler.handleDukienLo(mockCtx);

      // Assert
      expect(mockBot.sendMessage).toHaveBeenCalledWith(
        mockCtx.chatId,
        expect.stringContaining('❌ Lỗi kết nối mạng'),
        { parse_mode: 'HTML' }
      );
    });

    it('should use fallback prediction when main prediction fails', async () => {
      // Arrange
      const fallbackPrediction = {
        predictions: [{ number: '12', confidence: 60 }],
        confidence: 60,
        method: 'statistical',
        type: 'lo',
        fallback: true,
        originalError: 'Main model failed'
      };

      // First call fails, second call (fallback) succeeds
      mockPredictionEngine.predictLo
        .mockRejectedValueOnce(new Error('Main model failed'))
        .mockResolvedValueOnce(fallbackPrediction);

      // Act
      await predictionHandler.handleDukienLo(mockCtx);

      // Assert
      expect(mockPredictionEngine.predictLo).toHaveBeenCalledTimes(2);
      expect(mockBot.editMessageText).toHaveBeenCalledWith(
        expect.stringContaining('⚠️'),
        expect.objectContaining({
          chat_id: mockCtx.chatId,
          message_id: 123
        })
      );
    });
  });

  describe('Enhanced Response Formatting', () => {
    it('should include additional information in response', async () => {
      // Arrange
      const mockPrediction = {
        predictions: [{ number: '12', confidence: 85 }],
        confidence: 80,
        method: 'ensemble',
        type: 'lo',
        dataPoints: 1000,
        processingTime: 250
      };

      mockPredictionEngine.predictLo.mockResolvedValue(mockPrediction);

      // Act
      await predictionHandler.handleDukienLo(mockCtx);

      // Assert
      const editCall = mockBot.editMessageText.mock.calls[0];
      const responseMessage = editCall[0];

      expect(responseMessage).toContain('ℹ️ Thông tin bổ sung:');
      expect(responseMessage).toContain('Thời gian tạo:');
      expect(responseMessage).toContain('1000 mẫu');
      expect(responseMessage).toContain('250ms');
      expect(responseMessage).toContain('Lưu ý quan trọng:');
      expect(responseMessage).toContain('Sử dụng dự đoán một cách có trách nhiệm');
    });

    it('should show fallback notice when using fallback prediction', async () => {
      // Arrange
      const fallbackPrediction = {
        predictions: [{ number: '12', confidence: 60 }],
        confidence: 60,
        method: 'statistical',
        type: 'lo',
        fallback: true
      };

      mockPredictionEngine.predictLo.mockResolvedValue(fallbackPrediction);

      // Act
      await predictionHandler.handleDukienLo(mockCtx);

      // Assert
      const editCall = mockBot.editMessageText.mock.calls[0];
      const responseMessage = editCall[0];

      expect(responseMessage).toContain('⚠️');
      expect(responseMessage).toContain('Sử dụng mô hình dự phòng');
    });
  });

  describe('Prediction Retry Logic', () => {
    it('should retry failed predictions with exponential backoff', async () => {
      // Arrange
      mockPredictionEngine.predictLo
        .mockRejectedValueOnce(new Error('Temporary failure'))
        .mockResolvedValueOnce({
          predictions: [{ number: '12', confidence: 85 }],
          confidence: 80,
          method: 'statistical',
          type: 'lo'
        });

      const startTime = Date.now();

      // Act
      await predictionHandler.handleDukienLo(mockCtx);

      // Assert
      const endTime = Date.now();
      expect(endTime - startTime).toBeGreaterThan(1000); // Should have waited for retry
      expect(mockPredictionEngine.predictLo).toHaveBeenCalledTimes(2);
      expect(mockBot.editMessageText).toHaveBeenCalled();
    });

    it('should fail after maximum retries', async () => {
      // Arrange
      mockPredictionEngine.predictLo.mockRejectedValue(new Error('Persistent failure'));

      // Act
      await predictionHandler.handleDukienLo(mockCtx);

      // Assert
      expect(mockPredictionEngine.predictLo).toHaveBeenCalledTimes(3); // 2 retries + 1 fallback
      expect(mockBot.sendMessage).toHaveBeenCalledWith(
        mockCtx.chatId,
        expect.stringContaining('❌ Đã xảy ra lỗi'),
        { parse_mode: 'HTML' }
      );
    });
  });

  describe('Performance and Response Time', () => {
    it('should complete prediction within reasonable time', async () => {
      // Arrange
      const mockPrediction = {
        predictions: [{ number: '12', confidence: 85 }],
        confidence: 80,
        method: 'statistical',
        type: 'lo'
      };

      mockPredictionEngine.predictLo.mockResolvedValue(mockPrediction);

      const startTime = Date.now();

      // Act
      await predictionHandler.handleDukienLo(mockCtx);

      // Assert
      const responseTime = Date.now() - startTime;
      expect(responseTime).toBeLessThan(5000); // Should complete within 5 seconds

      // Check that response time is logged
      expect(mockDataManager.logUserInteraction).toHaveBeenCalledWith(
        mockCtx.userId,
        'dukienlo',
        expect.objectContaining({
          responseTime: expect.any(Number)
        })
      );
    });

    it('should timeout long-running predictions', async () => {
      // Arrange
      mockPredictionEngine.predictLo.mockImplementation(() =>
        new Promise(resolve => setTimeout(() => resolve({ predictions: [] }), 12000))
      );

      // Act
      const startTime = Date.now();
      await predictionHandler.handleDukienLo(mockCtx);
      const endTime = Date.now();

      // Assert
      expect(endTime - startTime).toBeLessThan(25000); // Should timeout before 25 seconds (includes retries)
      expect(mockBot.sendMessage).toHaveBeenCalledWith(
        mockCtx.chatId,
        expect.stringContaining('❌'),
        { parse_mode: 'HTML' }
      );
    });
  });
});