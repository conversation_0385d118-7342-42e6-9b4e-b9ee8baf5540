const DataManager = require('../../lib/data');
const ExternalApiManager = require('../../lib/data/collectors/externalApi');
const LotteryCollector = require('../../lib/data/collectors/lotteryCollector');

// Mock external dependencies
jest.mock('axios');

describe('Data Collection Integration Tests', () => {
  let dataManager;
  let mockHtml;

  beforeAll(async () => {
    // Setup test data
    mockHtml = `
      <div class="box_kqxs">
        <table>
          <tr>
            <td>ĐB</td>
            <td>12345</td>
          </tr>
          <tr>
            <td>1</td>
            <td>67890</td>
          </tr>
          <tr>
            <td>2</td>
            <td>11111</td>
            <td>22222</td>
          </tr>
          <tr>
            <td>3</td>
            <td>33333</td>
            <td>44444</td>
            <td>55555</td>
          </tr>
        </table>
      </div>
    `;
  });

  beforeEach(async () => {
    dataManager = new DataManager();
    await dataManager.initialize();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('External API Manager Integration', () => {
    test('should initialize external API manager correctly', () => {
      expect(dataManager.externalApi).toBeInstanceOf(ExternalApiManager);
      expect(dataManager.externalApi.collectors.has('xoso.com.vn')).toBe(true);
    });

    test('should fetch results through external API manager', async () => {
      // Mock successful response
      const mockAxiosInstance = {
        get: jest.fn().mockResolvedValue({
          status: 200,
          data: mockHtml
        })
      };

      dataManager.externalApi.collectors.get('xoso.com.vn').axiosInstance = mockAxiosInstance;

      const date = new Date('2024-01-15');
      const results = await dataManager.collectLotteryResults(date, 'north');

      expect(results).toBeDefined();
      expect(results.date).toEqual(date);
      expect(results.region).toBe('north');
      expect(results.prizes.special).toBe('12345');
      expect(results.numbers.de).toBe('45');
      expect(results.source).toBe('xoso.com.vn');
    });

    test('should handle external API failures gracefully', async () => {
      // Mock failed response
      const mockAxiosInstance = {
        get: jest.fn().mockRejectedValue(new Error('Network error'))
      };

      dataManager.externalApi.collectors.get('xoso.com.vn').axiosInstance = mockAxiosInstance;

      const date = new Date('2024-01-15');

      await expect(dataManager.collectLotteryResults(date, 'north'))
        .rejects.toThrow('Data collection failed');
    });
  });

  describe('Batch Data Collection', () => {
    test('should collect multiple dates successfully', async () => {
      // Mock successful responses
      const mockAxiosInstance = {
        get: jest.fn().mockResolvedValue({
          status: 200,
          data: mockHtml
        })
      };

      dataManager.externalApi.collectors.get('xoso.com.vn').axiosInstance = mockAxiosInstance;

      const startDate = new Date('2024-01-15');
      const endDate = new Date('2024-01-17');

      const summary = await dataManager.collectBatchResults(startDate, endDate, 'north');

      expect(summary.total).toBe(3);
      expect(summary.successful).toBe(3);
      expect(summary.failed).toBe(0);
      expect(mockAxiosInstance.get).toHaveBeenCalledTimes(3);
    });

    test('should handle partial failures in batch collection', async () => {
      // Mock mixed responses
      const mockAxiosInstance = {
        get: jest.fn()
          .mockResolvedValueOnce({ status: 200, data: mockHtml })
          .mockRejectedValueOnce(new Error('Network error'))
          .mockResolvedValueOnce({ status: 200, data: mockHtml })
      };

      dataManager.externalApi.collectors.get('xoso.com.vn').axiosInstance = mockAxiosInstance;

      const startDate = new Date('2024-01-15');
      const endDate = new Date('2024-01-17');

      const summary = await dataManager.collectBatchResults(startDate, endDate, 'north');

      expect(summary.total).toBe(3);
      expect(summary.successful).toBe(2);
      expect(summary.failed).toBe(1);
    });
  });

  describe('Latest Results Collection', () => {
    test('should collect latest results successfully', async () => {
      // Mock successful response
      const mockAxiosInstance = {
        get: jest.fn().mockResolvedValue({
          status: 200,
          data: mockHtml
        })
      };

      dataManager.externalApi.collectors.get('xoso.com.vn').axiosInstance = mockAxiosInstance;

      const results = await dataManager.collectLatestResults('north');

      expect(results).toBeDefined();
      expect(results.region).toBe('north');
      expect(results.prizes.special).toBe('12345');
      expect(results.numbers.de).toBe('45');
    });

    test('should fallback to yesterday if today fails', async () => {
      // Mock responses: today fails, yesterday succeeds
      const mockAxiosInstance = {
        get: jest.fn()
          .mockRejectedValueOnce(new Error('No results for today'))
          .mockRejectedValueOnce(new Error('No results for today'))
          .mockRejectedValueOnce(new Error('No results for today'))
          .mockResolvedValueOnce({
            status: 200,
            data: mockHtml
          })
      };

      dataManager.externalApi.collectors.get('xoso.com.vn').axiosInstance = mockAxiosInstance;

      const results = await dataManager.collectLatestResults('north');

      expect(results).toBeDefined();
      expect(results.prizes.special).toBe('12345');
      expect(mockAxiosInstance.get).toHaveBeenCalledTimes(4); // 3 retries for today + 1 for yesterday
    });
  });

  describe('Cache Integration', () => {
    test('should use cached results when available', async () => {
      const mockAxiosInstance = {
        get: jest.fn().mockResolvedValue({
          status: 200,
          data: mockHtml
        })
      };

      dataManager.externalApi.collectors.get('xoso.com.vn').axiosInstance = mockAxiosInstance;

      const date = new Date('2024-01-15');

      // First call - should fetch from API
      const results1 = await dataManager.collectLotteryResults(date, 'north');
      expect(mockAxiosInstance.get).toHaveBeenCalledTimes(1);

      // Second call - should use cache
      const results2 = await dataManager.collectLotteryResults(date, 'north');
      expect(mockAxiosInstance.get).toHaveBeenCalledTimes(1); // No additional call

      expect(results1).toEqual(results2);
    });

    test('should clear cache when requested', async () => {
      const mockAxiosInstance = {
        get: jest.fn().mockResolvedValue({
          status: 200,
          data: mockHtml
        })
      };

      dataManager.externalApi.collectors.get('xoso.com.vn').axiosInstance = mockAxiosInstance;

      const date = new Date('2024-01-15');

      // First call
      await dataManager.collectLotteryResults(date, 'north');
      expect(mockAxiosInstance.get).toHaveBeenCalledTimes(1);

      // Clear cache
      dataManager.externalApi.clearCache();

      // Second call - should fetch again
      await dataManager.collectLotteryResults(date, 'north');
      expect(mockAxiosInstance.get).toHaveBeenCalledTimes(2);
    });
  });

  describe('Data Validation Integration', () => {
    test('should validate collected data format', async () => {
      const mockAxiosInstance = {
        get: jest.fn().mockResolvedValue({
          status: 200,
          data: mockHtml
        })
      };

      dataManager.externalApi.collectors.get('xoso.com.vn').axiosInstance = mockAxiosInstance;

      const date = new Date('2024-01-15');
      const results = await dataManager.collectLotteryResults(date, 'north');

      // Validate structure
      expect(results).toHaveProperty('date');
      expect(results).toHaveProperty('region');
      expect(results).toHaveProperty('prizes');
      expect(results).toHaveProperty('numbers');
      expect(results).toHaveProperty('source');
      expect(results).toHaveProperty('collectedAt');

      // Validate prizes
      expect(results.prizes).toHaveProperty('special');
      expect(results.prizes.special).toMatch(/^\d{5}$/);

      // Validate numbers
      expect(results.numbers).toHaveProperty('de');
      expect(results.numbers).toHaveProperty('lo');
      expect(results.numbers.de).toMatch(/^\d{2}$/);
      expect(results.numbers.lo.length).toBeGreaterThan(0);
    });

    test('should reject invalid data format', async () => {
      const invalidHtml = '<div>Invalid HTML without lottery table</div>';

      const mockAxiosInstance = {
        get: jest.fn().mockResolvedValue({
          status: 200,
          data: invalidHtml
        })
      };

      dataManager.externalApi.collectors.get('xoso.com.vn').axiosInstance = mockAxiosInstance;

      const date = new Date('2024-01-15');

      await expect(dataManager.collectLotteryResults(date, 'north'))
        .rejects.toThrow('Data collection failed');
    });
  });

  describe('Error Handling and Recovery', () => {
    test('should handle network timeouts', async () => {
      const mockAxiosInstance = {
        get: jest.fn().mockRejectedValue(new Error('ECONNABORTED'))
      };

      dataManager.externalApi.collectors.get('xoso.com.vn').axiosInstance = mockAxiosInstance;

      const date = new Date('2024-01-15');

      await expect(dataManager.collectLotteryResults(date, 'north'))
        .rejects.toThrow('Data collection failed');
    });

    test('should handle HTTP errors', async () => {
      const mockAxiosInstance = {
        get: jest.fn().mockRejectedValue({
          response: { status: 404, statusText: 'Not Found' }
        })
      };

      dataManager.externalApi.collectors.get('xoso.com.vn').axiosInstance = mockAxiosInstance;

      const date = new Date('2024-01-15');

      await expect(dataManager.collectLotteryResults(date, 'north'))
        .rejects.toThrow('Data collection failed');
    });

    test('should handle malformed HTML gracefully', async () => {
      const malformedHtml = '<div><table><tr><td>Incomplete';

      const mockAxiosInstance = {
        get: jest.fn().mockResolvedValue({
          status: 200,
          data: malformedHtml
        })
      };

      dataManager.externalApi.collectors.get('xoso.com.vn').axiosInstance = mockAxiosInstance;

      const date = new Date('2024-01-15');

      await expect(dataManager.collectLotteryResults(date, 'north'))
        .rejects.toThrow('Data collection failed');
    });
  });

  describe('Performance and Rate Limiting', () => {
    test('should respect rate limiting in batch operations', async () => {
      const mockAxiosInstance = {
        get: jest.fn().mockResolvedValue({
          status: 200,
          data: mockHtml
        })
      };

      dataManager.externalApi.collectors.get('xoso.com.vn').axiosInstance = mockAxiosInstance;

      const startDate = new Date('2024-01-15');
      const endDate = new Date('2024-01-19'); // 5 days

      const startTime = Date.now();
      await dataManager.collectBatchResults(startDate, endDate, 'north');
      const endTime = Date.now();

      // Should take at least some time due to delays between batches
      expect(endTime - startTime).toBeGreaterThan(1000); // At least 1 second
      expect(mockAxiosInstance.get).toHaveBeenCalledTimes(5);
    });
  });
});
