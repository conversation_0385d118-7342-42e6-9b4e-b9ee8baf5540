// Integration tests for User, Group, and Analytics Managers
// Tests the interaction between managers and real database operations

const { MongoMemoryServer } = require('mongodb-memory-server');
const mongoose = require('mongoose');
const redis = require('redis');

const UserManager = require('../../../lib/managers/userManager');
const GroupManager = require('../../../lib/managers/groupManager');
const AnalyticsManager = require('../../../lib/managers/analyticsManager');

// Mock logger to avoid log output during tests
jest.mock('../../../lib/logger', () => {
  return () => ({
    logInfo: jest.fn(),
    logError: jest.fn(),
    logWarn: jest.fn()
  });
});

describe('Managers Integration Tests', () => {
  let mongoServer;
  let mongoConnection;
  let redisClient;
  let userManager;
  let groupManager;
  let analyticsManager;

  beforeAll(async () => {
    // Start in-memory MongoDB
    mongoServer = await MongoMemoryServer.create();
    const mongoUri = mongoServer.getUri();
    
    mongoConnection = await mongoose.createConnection(mongoUri);

    // Mock Redis client (in-memory)
    redisClient = {
      connect: jest.fn().mockResolvedValue(),
      get: jest.fn().mockResolvedValue(null),
      setEx: jest.fn().mockResolvedValue(),
      del: jest.fn().mockResolvedValue(),
      quit: jest.fn().mockResolvedValue(),
      isOpen: true
    };

    // Mock the connection modules
    jest.doMock('../../../lib/connections/mongo', () => {
      return () => mongoConnection;
    });

    jest.doMock('../../../lib/connections/redis', () => {
      return () => ({
        getConnection: () => redisClient
      });
    });
  });

  afterAll(async () => {
    if (mongoConnection) {
      await mongoConnection.close();
    }
    if (mongoServer) {
      await mongoServer.stop();
    }
  });

  beforeEach(async () => {
    // Clear all collections
    const collections = await mongoConnection.db.collections();
    for (const collection of collections) {
      await collection.deleteMany({});
    }

    // Reset Redis mock
    jest.clearAllMocks();
    redisClient.get.mockResolvedValue(null);

    // Initialize managers
    userManager = new UserManager({
      cacheEnabled: true,
      cacheTTL: 3600,
      privacyCompliant: true
    });

    groupManager = new GroupManager({
      cacheEnabled: true,
      cacheTTL: 21600
    });

    analyticsManager = new AnalyticsManager({
      cacheEnabled: true,
      cacheTTL: 900,
      retentionDays: 90
    });

    await Promise.all([
      userManager.initialize(),
      groupManager.initialize(),
      analyticsManager.initialize()
    ]);
  });

  afterEach(async () => {
    await Promise.all([
      userManager.cleanup(),
      groupManager.cleanup(),
      analyticsManager.cleanup()
    ]);
  });

  describe('User Management Integration', () => {
    test('should register and manage users end-to-end', async () => {
      // Register a new user
      const telegramUserData = {
        id: 123456789,
        username: 'testuser',
        first_name: 'Test',
        last_name: 'User',
        language_code: 'vi'
      };

      const user = await userManager.registerUser(telegramUserData);
      expect(user).toMatchObject({
        telegramId: 123456789,
        username: 'testuser',
        firstName: 'Test',
        lastName: 'User'
      });

      // Get user by ID
      const retrievedUser = await userManager.getUserById(123456789);
      expect(retrievedUser.telegramId).toBe(123456789);

      // Update user preferences
      const preferences = {
        notifications: false,
        favoriteNumbers: ['12', '34', '56'],
        timezone: 'Asia/Bangkok'
      };

      await userManager.updateUserPreferences(123456789, preferences);

      // Log user interactions
      await userManager.logUserInteraction(123456789, 'dukienlo', {
        queriedNumber: '12',
        success: true
      });

      await userManager.logUserInteraction(123456789, 'dukiende', {
        queriedNumber: '34',
        success: true
      });

      // Get user stats
      const stats = await userManager.getUserStats(123456789);
      expect(stats.totalQueries).toBe(2);
      expect(stats.mostQueriedNumbers).toContain('12');
      expect(stats.mostQueriedNumbers).toContain('34');

      // Get interaction history
      const history = await userManager.getUserInteractionHistory(123456789);
      expect(history).toHaveLength(2);
      expect(history[0].command).toBe('dukiende'); // Most recent first
      expect(history[1].command).toBe('dukienlo');
    });

    test('should handle multiple users and analytics', async () => {
      // Register multiple users
      const users = [
        { id: 111, username: 'user1', first_name: 'User', last_name: 'One' },
        { id: 222, username: 'user2', first_name: 'User', last_name: 'Two' },
        { id: 333, username: 'user3', first_name: 'User', last_name: 'Three' }
      ];

      for (const userData of users) {
        await userManager.registerUser(userData);
        await userManager.logUserInteraction(userData.id, 'dukienlo');
      }

      // Get user analytics
      const analytics = await userManager.getUserAnalytics();
      expect(analytics.totalUsers).toBe(3);
      expect(analytics.activeToday).toBe(3);

      // Get active users
      const activeUsers = await userManager.getActiveUsers(1);
      expect(activeUsers).toBe(3);
    });
  });

  describe('Group Management Integration', () => {
    test('should register and manage groups end-to-end', async () => {
      // Register a new group
      const telegramGroupData = {
        id: -123456789,
        title: 'Test Group',
        type: 'supergroup',
        username: 'testgroup',
        description: 'A test group for integration testing',
        member_count: 50
      };

      const group = await groupManager.registerGroup(telegramGroupData);
      expect(group).toMatchObject({
        telegramId: -123456789,
        title: 'Test Group',
        type: 'supergroup'
      });

      // Get group settings
      const settings = await groupManager.getGroupSettings(-123456789);
      expect(settings).toMatchObject({
        dailyPredictions: false,
        predictionTime: '08:00',
        language: 'vi'
      });

      // Update group settings
      const newSettings = {
        dailyPredictions: true,
        predictionTime: '09:00',
        weeklyReports: true,
        language: 'en'
      };

      await groupManager.updateGroupSettings(-123456789, newSettings);

      const updatedSettings = await groupManager.getGroupSettings(-123456789);
      expect(updatedSettings.dailyPredictions).toBe(true);
      expect(updatedSettings.predictionTime).toBe('09:00');
      expect(updatedSettings.language).toBe('en');

      // Add group admin
      const adminData = {
        telegramId: 987654321,
        username: 'admin',
        firstName: 'Admin',
        lastName: 'User'
      };

      await groupManager.addGroupAdmin(-123456789, adminData);

      // Check admin status
      const isAdmin = await groupManager.isGroupAdmin(-123456789, 987654321);
      expect(isAdmin).toBe(true);

      // Log group interactions
      await groupManager.logGroupInteraction(-123456789, 'dukienlo', 55);
      await groupManager.logGroupInteraction(-123456789, 'dukiende', 55);
      await groupManager.logGroupInteraction(-123456789, 'lichsu', 55);

      // Remove admin
      await groupManager.removeGroupAdmin(-123456789, 987654321);

      const isStillAdmin = await groupManager.isGroupAdmin(-123456789, 987654321);
      expect(isStillAdmin).toBe(false);
    });

    test('should handle scheduled operations', async () => {
      // Register groups with different settings
      const groups = [
        {
          id: -111,
          title: 'Daily Group',
          type: 'group',
          settings: { dailyPredictions: true }
        },
        {
          id: -222,
          title: 'Weekly Group',
          type: 'group',
          settings: { weeklyReports: true, weeklyReportDay: 1 }
        },
        {
          id: -333,
          title: 'Regular Group',
          type: 'group'
        }
      ];

      for (const groupData of groups) {
        await groupManager.registerGroup(groupData);
        if (groupData.settings) {
          await groupManager.updateGroupSettings(groupData.id, groupData.settings);
        }
      }

      // Get groups for daily predictions
      const dailyGroups = await groupManager.getGroupsForDailyPredictions();
      expect(dailyGroups).toHaveLength(1);
      expect(dailyGroups[0].telegramId).toBe(-111);

      // Get groups for weekly reports
      const weeklyGroups = await groupManager.getGroupsForWeeklyReports(1);
      expect(weeklyGroups).toHaveLength(1);
      expect(weeklyGroups[0].telegramId).toBe(-222);

      // Get group analytics
      const analytics = await groupManager.getGroupAnalytics();
      expect(analytics.totalGroups).toBe(3);
      expect(analytics.groupsWithDailyPredictions).toBe(1);
      expect(analytics.groupsWithWeeklyReports).toBe(1);
    });
  });

  describe('Analytics Integration', () => {
    test('should record and aggregate analytics end-to-end', async () => {
      // Set up test data
      // Register users
      const users = [
        { id: 111, username: 'user1' },
        { id: 222, username: 'user2' },
        { id: 333, username: 'user3' }
      ];

      for (const userData of users) {
        await userManager.registerUser(userData);
        await userManager.logUserInteraction(userData.id, 'dukienlo', {
          queriedNumber: '12'
        });
      }

      // Register groups
      const groups = [
        { id: -111, title: 'Group 1', type: 'group' },
        { id: -222, title: 'Group 2', type: 'supergroup' }
      ];

      for (const groupData of groups) {
        await groupManager.registerGroup(groupData);
        await groupManager.logGroupInteraction(groupData.id, 'dukienlo');
        await groupManager.logGroupInteraction(groupData.id, 'dukiende');
      }

      // Record daily analytics
      const analytics = await analyticsManager.recordDailyAnalytics();
      
      expect(analytics).toMatchObject({
        type: 'daily',
        metrics: {
          users: {
            totalUsers: 3,
            activeToday: 3
          },
          groups: {
            totalGroups: 2,
            activeToday: 2
          },
          commands: {
            totalDukienlo: 2,
            totalDukiende: 2
          }
        }
      });

      // Get popular numbers
      const popularNumbers = await analyticsManager.getPopularNumbers(1, 5);
      expect(popularNumbers).toHaveLength(1);
      expect(popularNumbers[0]).toMatchObject({
        number: '12',
        count: 3
      });

      // Get realtime stats
      const realtimeStats = await analyticsManager.getRealtimeStats();
      expect(realtimeStats.users.totalUsers).toBe(3);
      expect(realtimeStats.groups.totalGroups).toBe(2);

      // Get analytics summary
      const summary = await analyticsManager.getAnalyticsSummary(1);
      expect(summary.totalDays).toBe(1);
      expect(summary.summary.currentUsers).toBe(3);
      expect(summary.summary.currentGroups).toBe(2);
    });

    test('should handle analytics over multiple days', async () => {
      // Mock different dates for testing trends
      const today = new Date();
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);

      // Insert mock analytics for yesterday
      await analyticsManager.db.collection('analytics').insertOne({
        date: yesterday,
        type: 'daily',
        metrics: {
          users: { totalUsers: 50, activeToday: 10 },
          groups: { totalGroups: 5, activeToday: 2 },
          commands: { totalDukienlo: 20, totalDukiende: 15 }
        },
        createdAt: yesterday
      });

      // Set up current data
      await userManager.registerUser({ id: 111, username: 'user1' });
      await groupManager.registerGroup({ id: -111, title: 'Group 1', type: 'group' });

      // Record today's analytics
      const todayAnalytics = await analyticsManager.recordDailyAnalytics();

      // Get 2-day summary
      const summary = await analyticsManager.getAnalyticsSummary(2);
      
      expect(summary.totalDays).toBe(2);
      expect(summary.trends.userGrowth).toBeLessThan(0); // Negative growth from 50 to 1
      expect(summary.trends.groupGrowth).toBeLessThan(0); // Negative growth from 5 to 1
    });
  });

  describe('Cross-Manager Integration', () => {
    test('should work together for complete user journey', async () => {
      // User joins a group
      const user = await userManager.registerUser({
        id: 123456789,
        username: 'testuser',
        first_name: 'Test',
        last_name: 'User'
      });

      const group = await groupManager.registerGroup({
        id: -987654321,
        title: 'Test Group',
        type: 'supergroup',
        member_count: 1
      });

      // User becomes admin
      await groupManager.addGroupAdmin(-987654321, {
        telegramId: 123456789,
        username: 'testuser',
        firstName: 'Test'
      });

      // Admin updates group settings
      await groupManager.updateGroupSettings(-987654321, {
        dailyPredictions: true,
        weeklyReports: true
      }, 123456789);

      // User interacts with bot
      await userManager.logUserInteraction(123456789, 'dukienlo', {
        queriedNumber: '12',
        success: true
      });

      await groupManager.logGroupInteraction(-987654321, 'dukienlo', 1);

      // Record analytics
      const analytics = await analyticsManager.recordDailyAnalytics();

      // Verify everything is connected
      expect(analytics.metrics.users.totalUsers).toBe(1);
      expect(analytics.metrics.groups.totalGroups).toBe(1);
      expect(analytics.metrics.groups.groupsWithDailyPredictions).toBe(1);
      expect(analytics.metrics.commands.totalDukienlo).toBe(1);

      const popularNumbers = await analyticsManager.getPopularNumbers();
      expect(popularNumbers[0].number).toBe('12');
      expect(popularNumbers[0].count).toBe(1);

      // Verify user can be retrieved and has correct stats
      const userStats = await userManager.getUserStats(123456789);
      expect(userStats.totalQueries).toBe(1);

      // Verify group settings are correct
      const groupSettings = await groupManager.getGroupSettings(-987654321);
      expect(groupSettings.dailyPredictions).toBe(true);
      expect(groupSettings.weeklyReports).toBe(true);

      // Verify admin status
      const isAdmin = await groupManager.isGroupAdmin(-987654321, 123456789);
      expect(isAdmin).toBe(true);
    });
  });

  describe('Error Handling and Edge Cases', () => {
    test('should handle duplicate registrations gracefully', async () => {
      const userData = { id: 123, username: 'test' };
      const groupData = { id: -123, title: 'Test', type: 'group' };

      // Register once
      const user1 = await userManager.registerUser(userData);
      const group1 = await groupManager.registerGroup(groupData);

      // Register again - should return existing
      const user2 = await userManager.registerUser(userData);
      const group2 = await groupManager.registerGroup(groupData);

      expect(user1.telegramId).toBe(user2.telegramId);
      expect(group1.telegramId).toBe(group2.telegramId);
    });

    test('should handle invalid data gracefully', async () => {
      // Invalid user preferences
      await userManager.registerUser({ id: 123, username: 'test' });
      
      const invalidPreferences = {
        notifications: 'invalid',
        favoriteNumbers: ['invalid', '12', '34'],
        timezone: 123
      };

      // Should not throw, but should validate data
      await expect(userManager.updateUserPreferences(123, invalidPreferences))
        .resolves.toBe(true);

      // Invalid group settings
      await groupManager.registerGroup({ id: -123, title: 'Test', type: 'group' });
      
      const invalidSettings = {
        dailyPredictions: 'invalid',
        predictionTime: '25:00',
        weeklyReportDay: 10
      };

      // Should not throw, but should validate data
      await expect(groupManager.updateGroupSettings(-123, invalidSettings))
        .resolves.toBe(true);
    });

    test('should handle missing data gracefully', async () => {
      // Get non-existent user
      const user = await userManager.getUserById(999999);
      expect(user).toBeNull();

      // Get settings for non-existent group
      const settings = await groupManager.getGroupSettings(-999999);
      expect(settings).toEqual(groupManager.options.defaultSettings);

      // Check admin status for non-existent group/user
      const isAdmin = await groupManager.isGroupAdmin(-999999, 999999);
      expect(isAdmin).toBe(false);
    });
  });
});
