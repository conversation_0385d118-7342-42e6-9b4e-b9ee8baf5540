const RedisCache = require('../../../lib/data/cache/redisCache');
const CacheKeys = require('../../../lib/data/cache/cacheKeys');

// Mock Redis client
const mockRedisClient = {
  isReady: true,
  get: jest.fn(),
  set: jest.fn(),
  setEx: jest.fn(),
  del: jest.fn(),
  keys: jest.fn(),
  exists: jest.fn(),
  ttl: jest.fn(),
  incrBy: jest.fn(),
  hSet: jest.fn(),
  hGet: jest.fn(),
  hGetAll: jest.fn(),
  expire: jest.fn(),
  info: jest.fn(),
  dbSize: jest.fn(),
  mGet: jest.fn(),
  multi: jest.fn(() => ({
    setEx: jest.fn().mockReturnThis(),
    exec: jest.fn()
  })),
  eval: jest.fn(),
  quit: jest.fn()
};

describe('RedisCache Integration Tests', () => {
  let cache;
  let keys;

  beforeEach(() => {
    jest.clearAllMocks();
    cache = new RedisCache(mockRedisClient, {
      keyPrefix: 'test:',
      defaultTTL: 3600,
      enableWarmup: false // Disable for tests
    });
    keys = new CacheKeys('test:');
  });

  afterEach(async () => {
    await cache.disconnect();
  });

  describe('Basic Cache Operations', () => {
    it('should set and get cache values', async () => {
      const testData = { message: 'test data', timestamp: new Date().toISOString() };
      mockRedisClient.setEx.mockResolvedValue('OK');
      mockRedisClient.get.mockResolvedValue(JSON.stringify(testData));

      // Set value
      const setResult = await cache.set('test:key', testData, 1800);
      expect(setResult).toBe(true);
      expect(mockRedisClient.setEx).toHaveBeenCalledWith(
        'test:key',
        1800,
        JSON.stringify(testData)
      );

      // Get value
      const getValue = await cache.get('test:key');
      expect(getValue).toEqual(testData);
      expect(mockRedisClient.get).toHaveBeenCalledWith('test:key');
    });

    it('should handle cache misses', async () => {
      mockRedisClient.get.mockResolvedValue(null);

      const result = await cache.get('nonexistent:key');
      expect(result).toBeNull();
      expect(cache.getStats().misses).toBe(1);
    });

    it('should delete cache entries', async () => {
      mockRedisClient.del.mockResolvedValue(1);

      const result = await cache.del('test:key');
      expect(result).toBe(1);
      expect(mockRedisClient.del).toHaveBeenCalledWith('test:key');
    });

    it('should check if key exists', async () => {
      mockRedisClient.exists.mockResolvedValue(1);

      const result = await cache.exists('test:key');
      expect(result).toBe(1);
      expect(mockRedisClient.exists).toHaveBeenCalledWith('test:key');
    });

    it('should get TTL for key', async () => {
      mockRedisClient.ttl.mockResolvedValue(3600);

      const result = await cache.ttl('test:key');
      expect(result).toBe(3600);
      expect(mockRedisClient.ttl).toHaveBeenCalledWith('test:key');
    });
  });

  describe('Advanced Cache Operations', () => {
    it('should get multiple values', async () => {
      const testData1 = { id: 1, name: 'test1' };
      const testData2 = { id: 2, name: 'test2' };
      
      mockRedisClient.mGet.mockResolvedValue([
        JSON.stringify(testData1),
        JSON.stringify(testData2),
        null
      ]);

      const results = await cache.mget(['key1', 'key2', 'key3']);
      
      expect(results).toEqual([testData1, testData2, null]);
      expect(mockRedisClient.mGet).toHaveBeenCalledWith([
        'test:key1',
        'test:key2',
        'test:key3'
      ]);
      expect(cache.getStats().hits).toBe(2);
      expect(cache.getStats().misses).toBe(1);
    });

    it('should set multiple values', async () => {
      const pipeline = {
        setEx: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue(['OK', 'OK'])
      };
      mockRedisClient.multi.mockReturnValue(pipeline);

      const keyValuePairs = [
        ['key1', { data: 'value1' }],
        ['key2', { data: 'value2' }]
      ];

      const result = await cache.mset(keyValuePairs, 1800);
      
      expect(result).toBe(true);
      expect(pipeline.setEx).toHaveBeenCalledTimes(2);
      expect(pipeline.exec).toHaveBeenCalled();
    });

    it('should handle hash operations', async () => {
      const hashData = { field1: 'value1', field2: 'value2' };
      
      // Set hash field
      mockRedisClient.hSet.mockResolvedValue(1);
      const setResult = await cache.setHash('hash:key', 'field1', 'value1');
      expect(setResult).toBe(true);

      // Get hash field
      mockRedisClient.hGet.mockResolvedValue(JSON.stringify('value1'));
      const getResult = await cache.getHash('hash:key', 'field1');
      expect(getResult).toBe('value1');

      // Get entire hash
      mockRedisClient.hGetAll.mockResolvedValue({
        field1: JSON.stringify('value1'),
        field2: JSON.stringify('value2')
      });
      const getAllResult = await cache.getHash('hash:key');
      expect(getAllResult).toEqual({ field1: 'value1', field2: 'value2' });
    });
  });

  describe('Cache Invalidation', () => {
    it('should invalidate by pattern', async () => {
      mockRedisClient.keys.mockResolvedValue([
        'test:predictions:lo:2024-01-15',
        'test:predictions:de:2024-01-15'
      ]);
      mockRedisClient.del.mockResolvedValue(2);

      const result = await cache.invalidateByPattern('predictions:*');
      
      expect(result).toBe(2);
      expect(mockRedisClient.keys).toHaveBeenCalledWith('test:predictions:*');
      expect(mockRedisClient.del).toHaveBeenCalledWith([
        'test:predictions:lo:2024-01-15',
        'test:predictions:de:2024-01-15'
      ]);
    });

    it('should invalidate by tags', async () => {
      mockRedisClient.keys.mockResolvedValue(['test:data:tag1:key1', 'test:data:tag2:key2']);
      mockRedisClient.del.mockResolvedValue(1);

      const result = await cache.invalidateByTags(['tag1', 'tag2']);
      
      expect(result).toBe(2); // 1 for each tag
      expect(mockRedisClient.keys).toHaveBeenCalledTimes(2);
    });

    it('should clean up expired keys', async () => {
      // Mock keys with old dates that should be expired
      const oldDate = new Date(Date.now() - 48 * 60 * 60 * 1000).toISOString().split('T')[0]; // 2 days ago
      const recentDate = new Date().toISOString().split('T')[0]; // today
      
      mockRedisClient.keys.mockResolvedValue([
        `test:old:${oldDate}:key1`,
        `test:old:${oldDate}:key2`,
        `test:recent:${recentDate}:key3`
      ]);
      mockRedisClient.ttl.mockResolvedValueOnce(-1).mockResolvedValueOnce(-1).mockResolvedValueOnce(3600);
      mockRedisClient.del.mockResolvedValue(1);

      const result = await cache.invalidateExpired();
      
      expect(result).toBe(2); // 2 expired keys cleaned
      expect(mockRedisClient.del).toHaveBeenCalledTimes(2);
    });
  });

  describe('Distributed Locking', () => {
    it('should acquire and release locks', async () => {
      const lockValue = '1234567890-0.123';
      mockRedisClient.set.mockResolvedValue('OK');
      mockRedisClient.eval.mockResolvedValue(1);

      // Acquire lock
      const acquiredLock = await cache.lock('test-resource', 60);
      expect(acquiredLock).toBeTruthy();
      expect(mockRedisClient.set).toHaveBeenCalledWith(
        'test:locks:test-resource',
        expect.any(String),
        { PX: 60000, NX: true }
      );

      // Release lock
      const released = await cache.unlock('test-resource', acquiredLock);
      expect(released).toBe(1);
      expect(mockRedisClient.eval).toHaveBeenCalled();
    });

    it('should fail to acquire existing lock', async () => {
      mockRedisClient.set.mockResolvedValue(null);

      const result = await cache.lock('existing-resource', 60);
      expect(result).toBeNull();
    });
  });

  describe('Cache Warming', () => {
    it('should warm cache with data provider', async () => {
      const mockDataProvider = {
        getData: jest.fn()
          .mockResolvedValueOnce({ prediction: 'lo:12' })
          .mockResolvedValueOnce({ prediction: 'de:123' })
          .mockResolvedValueOnce(null)
      };

      mockRedisClient.exists.mockResolvedValue(0); // Keys don't exist
      mockRedisClient.setEx.mockResolvedValue('OK');

      const result = await cache.warmCache(mockDataProvider);
      
      expect(result).toBeGreaterThan(0);
      expect(mockDataProvider.getData).toHaveBeenCalled();
    });

    it('should skip warming for existing keys', async () => {
      const mockDataProvider = {
        getData: jest.fn()
      };

      mockRedisClient.exists.mockResolvedValue(1); // Key exists

      const result = await cache.warmCacheKey('test:existing:key', mockDataProvider);
      
      expect(result).toBe(true);
      expect(mockDataProvider.getData).not.toHaveBeenCalled();
    });
  });

  describe('Error Handling', () => {
    it('should handle Redis connection errors gracefully', async () => {
      mockRedisClient.get.mockRejectedValue(new Error('Connection failed'));

      const result = await cache.get('test:key');
      
      expect(result).toBeNull();
      expect(cache.getStats().errors).toBe(1);
    });

    it('should retry operations on failure', async () => {
      mockRedisClient.setEx
        .mockRejectedValueOnce(new Error('Temporary failure'))
        .mockResolvedValueOnce('OK');

      const result = await cache.set('test:key', { data: 'test' });
      
      expect(result).toBe(true);
      expect(mockRedisClient.setEx).toHaveBeenCalledTimes(2);
    });

    it('should fail after max retries', async () => {
      mockRedisClient.setEx.mockRejectedValue(new Error('Persistent failure'));

      const result = await cache.set('test:key', { data: 'test' });
      
      expect(result).toBe(false);
      expect(mockRedisClient.setEx).toHaveBeenCalledTimes(3); // maxRetries = 3
      expect(cache.getStats().errors).toBe(1);
    });
  });

  describe('Statistics', () => {
    it('should track cache statistics', async () => {
      mockRedisClient.get.mockResolvedValueOnce(JSON.stringify({ data: 'hit' }))
                          .mockResolvedValueOnce(null);
      mockRedisClient.setEx.mockResolvedValue('OK');
      mockRedisClient.del.mockResolvedValue(1);

      // Generate some cache activity
      await cache.get('hit:key');
      await cache.get('miss:key');
      await cache.set('new:key', { data: 'new' });
      await cache.del('old:key');

      const stats = cache.getStats();
      
      expect(stats.hits).toBe(1);
      expect(stats.misses).toBe(1);
      expect(stats.sets).toBe(1);
      expect(stats.deletes).toBe(1);
      expect(stats.hitRate).toBe(0.5);
      expect(stats.connected).toBe(true);
    });

    it('should reset statistics', () => {
      cache.stats.hits = 10;
      cache.stats.misses = 5;
      
      cache.resetStats();
      
      const stats = cache.getStats();
      expect(stats.hits).toBe(0);
      expect(stats.misses).toBe(0);
      expect(stats.hitRate).toBe(0);
    });
  });
});

describe('CacheKeys', () => {
  let keys;

  beforeEach(() => {
    keys = new CacheKeys('test:');
  });

  describe('Key Generation', () => {
    it('should generate prediction keys', () => {
      expect(keys.predictions.lo('2024-01-15')).toBe('test:predictions:lo:2024-01-15');
      expect(keys.predictions.de('2024-01-15')).toBe('test:predictions:de:2024-01-15');
    });

    it('should generate lottery keys', () => {
      expect(keys.lottery.history('north', '2024-01-14', '2024-01-15'))
        .toBe('test:history:north:2024-01-14:2024-01-15');
      expect(keys.lottery.latest('north', 10)).toBe('test:latest:north:10');
    });

    it('should generate user and group keys', () => {
      expect(keys.user.stats(123456)).toBe('test:user:stats:123456');
      expect(keys.group.settings(-987654)).toBe('test:group:settings:-987654');
    });
  });

  describe('TTL Strategies', () => {
    it('should return appropriate TTLs for different key types', () => {
      expect(keys.getTTL('predictions:lo')).toBe(24 * 60 * 60); // 24 hours
      expect(keys.getTTL('trends')).toBe(1 * 60 * 60); // 1 hour
      expect(keys.getTTL('user:stats')).toBe(1 * 60 * 60); // 1 hour
      expect(keys.getTTL('system:health')).toBe(5 * 60); // 5 minutes
    });

    it('should adjust TTL based on context', () => {
      const baseTTL = keys.getTTL('history');
      const historicalTTL = keys.getTTL('history', { isHistorical: true });
      const realTimeTTL = keys.getTTL('history', { isRealTime: true });

      expect(historicalTTL).toBe(baseTTL * 2);
      expect(realTimeTTL).toBeLessThanOrEqual(5 * 60);
    });
  });

  describe('Utility Methods', () => {
    it('should extract dates from keys', () => {
      expect(keys.extractDate('test:predictions:lo:2024-01-15')).toBe('2024-01-15');
      expect(keys.extractDate('test:invalid:key')).toBeNull();
    });

    it('should check if keys are expired', () => {
      const oldKey = 'test:data:2020-01-01';
      const recentKey = `test:data:${new Date().toISOString().split('T')[0]}`;

      expect(keys.isExpired(oldKey, 24 * 60 * 60)).toBe(true);
      expect(keys.isExpired(recentKey, 24 * 60 * 60)).toBe(false);
    });

    it('should handle prefix operations', () => {
      expect(keys.addPrefix('user:stats:123')).toBe('test:user:stats:123');
      expect(keys.removePrefix('test:user:stats:123')).toBe('user:stats:123');
      expect(keys.getKeysByPattern('predictions:*')).toBe('test:predictions:*');
    });
  });

  describe('Warmup Keys', () => {
    it('should generate appropriate warmup keys', () => {
      const warmupKeys = keys.getWarmupKeys();
      
      expect(warmupKeys.some(key => key.includes('predictions:lo:'))).toBe(true);
      expect(warmupKeys.some(key => key.includes('predictions:de:'))).toBe(true);
      expect(warmupKeys.some(key => key.includes('latest:north:10'))).toBe(true);
      expect(warmupKeys.some(key => key.includes('trends:lo:weekly'))).toBe(true);
      expect(warmupKeys.some(key => key.includes('system:health'))).toBe(true);
    });
  });
});