# Test Environment Configuration
NODE_ENV=test

# Test Database
MONGODB_URI=mongodb://localhost:27017/lottery_bot_test
REDIS_URL=redis://localhost:6379/1

# Mock API Keys (not real)
TELEGRAM_BOT_TOKEN=123456789:AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
OPENAI_API_KEY=sk-test-key-not-real

# Test Configuration
LOG_LEVEL=error
ENABLE_SCHEDULER=false
ENABLE_NOTIFICATIONS=false

# Test Limits
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=1000