// Example of how to register and use prediction commands
// This demonstrates the integration between BotHandler and PredictionHandler

const BotHandler = require('../lib/bot');
const PredictionHandler = require('../lib/bot/handlers/prediction');
const PredictionEngine = require('../lib/prediction');
const LLMService = require('../lib/llm');
const DataManager = require('../lib/data');

async function setupPredictionBot() {
  try {
    // Initialize core services
    const dataManager = new DataManager({
      cacheEnabled: true,
      cacheTTL: 3600
    });
    await dataManager.initialize();

    const predictionEngine = new PredictionEngine(dataManager, {
      enableLSTM: false, // Disable LSTM for this example
      enableEnsemble: true,
      maxPredictions: 10
    });
    await predictionEngine.initialize();

    const llmService = new LLMService({
      provider: 'openai',
      fallbackEnabled: true,
      dailyBudget: 10 // $10 daily budget
    });
    await llmService.initialize();

    // Create bot handler
    const botToken = process.env.TELEGRAM_BOT_TOKEN;
    if (!botToken) {
      throw new Error('TELEGRAM_BOT_TOKEN environment variable is required');
    }

    const bot = new BotHandler(botToken, {
      polling: true
    });

    // Create prediction handler
    const predictionHandler = new PredictionHandler(
      predictionEngine,
      llmService,
      dataManager
    );

    // Register prediction commands
    const handlers = predictionHandler.getHandlers();
    bot.registerCommand('dukienlo', handlers.dukienlo);
    bot.registerCommand('dukiende', handlers.dukiende);

    // Register other basic commands
    bot.registerCommand('start', async (ctx) => {
      const welcomeMessage =
        '🎯 Chào mừng bạn đến với Bot Dự Đoán Xổ Số!\n\n' +
        '📋 Các lệnh có sẵn:\n' +
        '• /dukienlo - Dự đoán số lô\n' +
        '• /dukiende - Dự đoán số đề\n' +
        '• /help - Xem hướng dẫn chi tiết\n\n' +
        '💡 Gửi một lệnh để bắt đầu!';

      await ctx.bot.sendMessage(ctx.chatId, welcomeMessage, {
        parse_mode: 'HTML'
      });
    });

    bot.registerCommand('help', async (ctx) => {
      const helpMessage =
        '🤖 <b>Hướng Dẫn Sử Dụng Bot Dự Đoán Xổ Số</b>\n\n' +
        '<b>Lệnh Dự Đoán:</b>\n' +
        '• /dukienlo - Dự đoán số lô với độ tin cậy\n' +
        '• /dukiende - Dự đoán số đề với phân tích AI\n\n' +
        '<b>Lệnh Khác:</b>\n' +
        '• /start - Khởi động bot\n' +
        '• /help - Hiển thị hướng dẫn này\n\n' +
        '<b>Cách Sử Dụng:</b>\n' +
        '1. Gửi lệnh dự đoán (ví dụ: /dukienlo)\n' +
        '2. Chờ bot phân tích dữ liệu\n' +
        '3. Nhận kết quả dự đoán với độ tin cậy\n' +
        '4. Đọc phân tích AI (nếu có)\n\n' +
        '⚠️ <i>Lưu ý: Tất cả dự đoán chỉ mang tính chất tham khảo. ' +
        'Xổ số có tính ngẫu nhiên cao, chơi có trách nhiệm!</i>';

      await ctx.bot.sendMessage(ctx.chatId, helpMessage, {
        parse_mode: 'HTML'
      });
    });

    // Start the bot
    await bot.start();
    console.log('🤖 Prediction bot started successfully!');
    console.log('📱 Bot is ready to receive commands');

    // Handle graceful shutdown
    process.on('SIGINT', async () => {
      console.log('\n🛑 Shutting down bot...');
      await bot.stop();
      console.log('✅ Bot stopped successfully');
      process.exit(0);
    });

    return bot;

  } catch (error) {
    console.error('❌ Failed to setup prediction bot:', error);
    process.exit(1);
  }
}

// Example usage with error handling
async function runExample() {
  console.log('🚀 Starting Prediction Bot Example...');

  // Check required environment variables
  const requiredEnvVars = ['TELEGRAM_BOT_TOKEN'];
  const optionalEnvVars = ['OPENAI_API_KEY', 'MONGODB_URI', 'REDIS_URL'];

  console.log('\n📋 Environment Check:');
  requiredEnvVars.forEach(envVar => {
    if (process.env[envVar]) {
      console.log(`✅ ${envVar}: Set`);
    } else {
      console.log(`❌ ${envVar}: Missing (Required)`);
    }
  });

  optionalEnvVars.forEach(envVar => {
    if (process.env[envVar]) {
      console.log(`✅ ${envVar}: Set`);
    } else {
      console.log(`⚠️  ${envVar}: Missing (Optional, will use fallback)`);
    }
  });

  if (!process.env.TELEGRAM_BOT_TOKEN) {
    console.error('\n❌ TELEGRAM_BOT_TOKEN is required. Please set it in your environment.');
    console.log('💡 Get your bot token from @BotFather on Telegram');
    process.exit(1);
  }

  console.log('\n🔧 Initializing services...');
  const bot = await setupPredictionBot();

  console.log('\n🎯 Bot Commands Available:');
  console.log('• /start - Welcome message');
  console.log('• /help - Show help');
  console.log('• /dukienlo - Predict lottery numbers (lo)');
  console.log('• /dukiende - Predict lottery numbers (de)');

  console.log('\n📱 Send a message to your bot to test the commands!');
  console.log('🔄 Bot is running... Press Ctrl+C to stop');
}

// Run the example if this file is executed directly
if (require.main === module) {
  runExample().catch(error => {
    console.error('💥 Example failed:', error);
    process.exit(1);
  });
}

module.exports = {
  setupPredictionBot,
  runExample
};